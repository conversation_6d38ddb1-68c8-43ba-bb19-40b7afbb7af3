"""
OMOP DRUG_ERA table builder.

Creates drug era records by consolidating continuous drug exposures
into treatment periods with appropriate gap allowances.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from ..utils.logger import LoggerMixin
from ..utils.exceptions import TransformationError


class DrugEraBuilder(LoggerMixin):
    """
    Builder for OMOP CDM DRUG_ERA table.

    Consolidates DRUG_EXPOSURE records into continuous treatment eras,
    allowing for specified gap periods between exposures.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize drug era builder.

        Args:
            config: Configuration dictionary containing era settings
        """
        self.config = config
        self.era_config = config.get('omop', {}).get('drug_era', {})

        # Era construction parameters
        self.gap_days = self.era_config.get('gap_days', 30)
        self.min_era_length_days = self.era_config.get('min_era_length_days', 1)
        self.stockpile_days = self.era_config.get('stockpile_days', 180)

        # Builder statistics
        self.builder_stats = {
            'start_time': None,
            'end_time': None,
            'input_exposures': 0,
            'output_eras': 0,
            'unique_patients': 0,
            'unique_drugs': 0,
            'avg_era_length': 0,
            'processing_errors': 0
        }

    def build_drug_era_table(self, drug_exposure_df: pd.DataFrame) -> pd.DataFrame:
        """
        Build DRUG_ERA table from DRUG_EXPOSURE records.

        Args:
            drug_exposure_df: DRUG_EXPOSURE table DataFrame

        Returns:
            DRUG_ERA table DataFrame
        """
        if drug_exposure_df.empty:
            self.logger.warning("Empty DRUG_EXPOSURE table - no eras to generate")
            return self._create_empty_drug_era_table()

        self.builder_stats['start_time'] = datetime.now()
        self.builder_stats['input_exposures'] = len(drug_exposure_df)

        self.logger.info(f"Building DRUG_ERA table from {len(drug_exposure_df)} drug exposures")

        try:
            # Validate required columns
            self._validate_drug_exposure_columns(drug_exposure_df)

            # Prepare data for era construction
            prepared_df = self._prepare_exposure_data(drug_exposure_df)

            # Build eras by patient and drug
            era_records = []

            # Group by patient and drug concept
            for (person_id, drug_concept_id), group_df in prepared_df.groupby(['person_id', 'drug_concept_id']):
                try:
                    patient_eras = self._build_eras_for_patient_drug(
                        person_id, drug_concept_id, group_df
                    )
                    era_records.extend(patient_eras)

                except Exception as e:
                    self.logger.error(f"Error building eras for patient {person_id}, drug {drug_concept_id}: {str(e)}")
                    self.builder_stats['processing_errors'] += 1
                    continue

            # Create DataFrame from era records
            if era_records:
                drug_era_df = pd.DataFrame(era_records)
                drug_era_df = self._finalize_drug_era_table(drug_era_df)
            else:
                drug_era_df = self._create_empty_drug_era_table()

            # Update statistics
            self._update_builder_statistics(drug_era_df)

            self.logger.info(f"Generated {len(drug_era_df)} drug era records")
            return drug_era_df

        except Exception as e:
            self.builder_stats['end_time'] = datetime.now()
            self.logger.error(f"Drug era building failed: {str(e)}")
            raise TransformationError(
                f"Failed to build DRUG_ERA table: {str(e)}",
                omop_table="DRUG_ERA"
            )

    def _validate_drug_exposure_columns(self, df: pd.DataFrame) -> None:
        """Validate required columns are present."""
        required_columns = [
            'person_id', 'drug_concept_id',
            'drug_exposure_start_date', 'drug_exposure_end_date'
        ]

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise TransformationError(
                f"Missing required columns for era building: {missing_columns}",
                omop_table="DRUG_ERA"
            )

    def _prepare_exposure_data(self, drug_exposure_df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare drug exposure data for era construction.

        Args:
            drug_exposure_df: Raw drug exposure data

        Returns:
            Cleaned and prepared DataFrame
        """
        df = drug_exposure_df.copy()

        # Convert dates to datetime
        df['drug_exposure_start_date'] = pd.to_datetime(df['drug_exposure_start_date'])
        df['drug_exposure_end_date'] = pd.to_datetime(df['drug_exposure_end_date'])

        # Handle missing end dates
        df['drug_exposure_end_date'] = df['drug_exposure_end_date'].fillna(
            df['drug_exposure_start_date']
        )

        # Calculate days supply if not present
        if 'days_supply' not in df.columns or df['days_supply'].isnull().all():
            df['days_supply'] = (df['drug_exposure_end_date'] - df['drug_exposure_start_date']).dt.days + 1
        else:
            df['days_supply'] = df['days_supply'].fillna(1)

        # Remove invalid records
        initial_count = len(df)
        df = df[
            (df['drug_exposure_start_date'].notna()) &
            (df['drug_exposure_end_date'].notna()) &
            (df['drug_exposure_start_date'] <= df['drug_exposure_end_date']) &
            (df['person_id'].notna()) &
            (df['drug_concept_id'].notna()) &
            (df['drug_concept_id'] > 0)
        ]

        removed_count = initial_count - len(df)
        if removed_count > 0:
            self.logger.warning(f"Removed {removed_count} invalid drug exposure records")

        # Sort by patient, drug, and start date
        df = df.sort_values(['person_id', 'drug_concept_id', 'drug_exposure_start_date'])

        return df

    def _build_eras_for_patient_drug(
        self,
        person_id: int,
        drug_concept_id: int,
        exposures_df: pd.DataFrame
    ) -> List[Dict[str, Any]]:
        """
        Build drug eras for a specific patient-drug combination.

        Args:
            person_id: Patient identifier
            drug_concept_id: Drug concept identifier
            exposures_df: Drug exposures for this patient-drug combination

        Returns:
            List of drug era records
        """
        exposures = exposures_df.sort_values('drug_exposure_start_date').copy()

        # Calculate stockpile end dates
        exposures['stockpile_end_date'] = (
            exposures['drug_exposure_end_date'] +
            pd.Timedelta(days=self.stockpile_days)
        )

        eras = []
        current_era_start = None
        current_era_end = None
        current_era_exposures = []

        for _, exposure in exposures.iterrows():
            exposure_start = exposure['drug_exposure_start_date']
            exposure_end = exposure['drug_exposure_end_date']
            stockpile_end = exposure['stockpile_end_date']

            if current_era_start is None:
                # Start first era
                current_era_start = exposure_start
                current_era_end = stockpile_end
                current_era_exposures = [exposure]

            else:
                # Check if this exposure continues the current era
                gap_days = (exposure_start - current_era_end).days

                if gap_days <= self.gap_days:
                    # Continue current era
                    current_era_end = max(current_era_end, stockpile_end)
                    current_era_exposures.append(exposure)

                else:
                    # End current era and start new one
                    era_record = self._create_era_record(
                        person_id, drug_concept_id,
                        current_era_start, current_era_end,
                        current_era_exposures
                    )
                    eras.append(era_record)

                    # Start new era
                    current_era_start = exposure_start
                    current_era_end = stockpile_end
                    current_era_exposures = [exposure]

        # Add final era
        if current_era_start is not None:
            era_record = self._create_era_record(
                person_id, drug_concept_id,
                current_era_start, current_era_end,
                current_era_exposures
            )
            eras.append(era_record)

        # Filter eras by minimum length
        valid_eras = []
        for era in eras:
            era_length = (era['drug_era_end_date'] - era['drug_era_start_date']).days + 1
            if era_length >= self.min_era_length_days:
                valid_eras.append(era)

        return valid_eras

    def _create_era_record(
        self,
        person_id: int,
        drug_concept_id: int,
        era_start: datetime,
        era_end: datetime,
        exposures: List[pd.Series]
    ) -> Dict[str, Any]:
        """
        Create a single drug era record.

        Args:
            person_id: Patient identifier
            drug_concept_id: Drug concept identifier
            era_start: Era start date
            era_end: Era end date (stockpile adjusted)
            exposures: List of exposures in this era

        Returns:
            Drug era record dictionary
        """
        # Calculate actual era end (latest actual exposure end, not stockpile)
        actual_era_end = max(exp['drug_exposure_end_date'] for exp in exposures)

        # Use actual end date, not stockpile end date
        era_end_date = actual_era_end

        # Count exposures in era
        drug_exposure_count = len(exposures)

        # Calculate gap days (total era length minus actual exposure days)
        total_era_days = (era_end_date - era_start).days + 1
        total_exposure_days = sum(
            (exp['drug_exposure_end_date'] - exp['drug_exposure_start_date']).days + 1
            for exp in exposures
        )
        gap_days = max(0, total_era_days - total_exposure_days)

        return {
            'person_id': person_id,
            'drug_concept_id': drug_concept_id,
            'drug_era_start_date': era_start.date(),
            'drug_era_end_date': era_end_date.date(),
            'drug_exposure_count': drug_exposure_count,
            'gap_days': gap_days
        }

    def _create_empty_drug_era_table(self) -> pd.DataFrame:
        """Create empty DRUG_ERA table with proper schema."""
        return pd.DataFrame(columns=[
            'drug_era_id',
            'person_id',
            'drug_concept_id',
            'drug_era_start_date',
            'drug_era_end_date',
            'drug_exposure_count',
            'gap_days'
        ])

    def _finalize_drug_era_table(self, drug_era_df: pd.DataFrame) -> pd.DataFrame:
        """
        Finalize drug era table with proper formatting and IDs.

        Args:
            drug_era_df: Raw drug era DataFrame

        Returns:
            Finalized DRUG_ERA table
        """
        df = drug_era_df.copy()

        # Generate sequential drug_era_id
        df['drug_era_id'] = range(1, len(df) + 1)

        # Ensure proper column order and types
        df['person_id'] = df['person_id'].astype('Int64')
        df['drug_concept_id'] = df['drug_concept_id'].astype('Int64')
        df['drug_exposure_count'] = df['drug_exposure_count'].astype('Int64')
        df['gap_days'] = df['gap_days'].astype('Int64')

        # Convert dates to strings in YYYY-MM-DD format
        df['drug_era_start_date'] = pd.to_datetime(df['drug_era_start_date']).dt.strftime('%Y-%m-%d')
        df['drug_era_end_date'] = pd.to_datetime(df['drug_era_end_date']).dt.strftime('%Y-%m-%d')

        # Reorder columns
        column_order = [
            'drug_era_id',
            'person_id',
            'drug_concept_id',
            'drug_era_start_date',
            'drug_era_end_date',
            'drug_exposure_count',
            'gap_days'
        ]

        df = df[column_order]

        # Sort by person and start date
        df = df.sort_values(['person_id', 'drug_era_start_date']).reset_index(drop=True)

        return df

    def _update_builder_statistics(self, drug_era_df: pd.DataFrame) -> None:
        """Update builder statistics."""
        self.builder_stats['end_time'] = datetime.now()
        self.builder_stats['output_eras'] = len(drug_era_df)

        if not drug_era_df.empty:
            self.builder_stats['unique_patients'] = drug_era_df['person_id'].nunique()
            self.builder_stats['unique_drugs'] = drug_era_df['drug_concept_id'].nunique()

            # Calculate average era length
            start_dates = pd.to_datetime(drug_era_df['drug_era_start_date'])
            end_dates = pd.to_datetime(drug_era_df['drug_era_end_date'])
            era_lengths = (end_dates - start_dates).dt.days + 1
            self.builder_stats['avg_era_length'] = era_lengths.mean()

    def get_builder_statistics(self) -> Dict[str, Any]:
        """Get comprehensive builder statistics."""
        stats = self.builder_stats.copy()

        if stats['start_time'] and stats['end_time']:
            execution_time = (stats['end_time'] - stats['start_time']).total_seconds()
            stats['execution_time_seconds'] = execution_time

            if stats['input_exposures'] > 0:
                stats['processing_rate_per_second'] = stats['input_exposures'] / execution_time
                stats['compression_ratio'] = stats['output_eras'] / stats['input_exposures']

        return stats

    def validate_drug_era_output(self, drug_era_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate generated drug era table.

        Args:
            drug_era_df: Generated DRUG_ERA table

        Returns:
            Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }

        if drug_era_df.empty:
            validation_results['warnings'].append("Empty DRUG_ERA table generated")
            return validation_results

        # Check for required columns
        required_columns = [
            'drug_era_id', 'person_id', 'drug_concept_id',
            'drug_era_start_date', 'drug_era_end_date'
        ]

        missing_columns = [col for col in required_columns if col not in drug_era_df.columns]
        if missing_columns:
            validation_results['errors'].append(f"Missing required columns: {missing_columns}")
            validation_results['is_valid'] = False

        # Check for null values in required fields
        for col in required_columns:
            if col in drug_era_df.columns:
                null_count = drug_era_df[col].isnull().sum()
                if null_count > 0:
                    validation_results['errors'].append(f"Null values in {col}: {null_count}")
                    validation_results['is_valid'] = False

        # Validate date logic
        if 'drug_era_start_date' in drug_era_df.columns and 'drug_era_end_date' in drug_era_df.columns:
            start_dates = pd.to_datetime(drug_era_df['drug_era_start_date'])
            end_dates = pd.to_datetime(drug_era_df['drug_era_end_date'])

            invalid_dates = (start_dates > end_dates).sum()
            if invalid_dates > 0:
                validation_results['errors'].append(f"Invalid date ranges: {invalid_dates} eras")
                validation_results['is_valid'] = False

        # Statistics
        validation_results['statistics'] = {
            'total_eras': len(drug_era_df),
            'unique_patients': drug_era_df['person_id'].nunique() if 'person_id' in drug_era_df.columns else 0,
            'unique_drugs': drug_era_df['drug_concept_id'].nunique() if 'drug_concept_id' in drug_era_df.columns else 0
        }

        return validation_results