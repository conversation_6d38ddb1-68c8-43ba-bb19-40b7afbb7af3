"""
Constants and enums for medication normalization pipeline.
"""

from enum import Enum
from typing import Dict, List, Set


# Supported database types
SUPPORTED_DB_TYPES = {
    'sql_server',
    'postgresql',
    'mysql',
    'sqlite',
    'fhir_server'
}

# Standard dose units for normalization
STANDARD_DOSE_UNITS = {
    'weight': {
        'ng': {'factor': 0.000001, 'base_unit': 'mg'},
        'mcg': {'factor': 0.001, 'base_unit': 'mg'},
        'mg': {'factor': 1.0, 'base_unit': 'mg'},
        'g': {'factor': 1000.0, 'base_unit': 'mg'},
        'kg': {'factor': 1000000.0, 'base_unit': 'mg'}
    },
    'volume': {
        'ul': {'factor': 0.001, 'base_unit': 'ml'},
        'ml': {'factor': 1.0, 'base_unit': 'ml'},
        'l': {'factor': 1000.0, 'base_unit': 'ml'}
    },
    'other': {
        '%': {'factor': 1.0, 'base_unit': '%'},
        'units': {'factor': 1.0, 'base_unit': 'units'},
        'iu': {'factor': 1.0, 'base_unit': 'iu'},
        'miu': {'factor': 1000000.0, 'base_unit': 'iu'}
    }
}

# OMOP CDM concept domains
OMOP_CONCEPT_DOMAINS = {
    'DRUG': 'Drug',
    'UNIT': 'Unit',
    'ROUTE': 'Route',
    'TYPE_CONCEPT': 'Type Concept'
}

# RxNorm term types in preference order
RXNORM_TERM_TYPE_PREFERENCE = [
    'SCD',   # Semantic Clinical Drug
    'SBD',   # Semantic Branded Drug
    'GPCK',  # Generic Pack
    'BPCK',  # Branded Pack
    'SCDG',  # Semantic Clinical Drug Group
    'SBDG',  # Semantic Branded Drug Group
    'IN',    # Ingredient
    'BN',    # Brand Name
    'PIN',   # Precise Ingredient
    'MIN'    # Multiple Ingredients
]

# Common medication administration routes
MEDICATION_ROUTES = {
    'oral': ['oral', 'po', 'by mouth', 'mouth', 'orally'],
    'intravenous': ['iv', 'intravenous', 'intravenously', 'i.v.', 'i.v'],
    'intramuscular': ['im', 'intramuscular', 'i.m.', 'i.m'],
    'subcutaneous': ['sc', 'sq', 'subcutaneous', 'subcut', 's.c.', 'subq'],
    'topical': ['topical', 'topically', 'external', 'skin'],
    'inhalation': ['inhaled', 'inhalation', 'nebulized', 'aerosol'],
    'rectal': ['rectal', 'rectally', 'pr', 'p.r.'],
    'ophthalmic': ['ophthalmic', 'eye', 'ocular'],
    'otic': ['otic', 'ear', 'aural'],
    'nasal': ['nasal', 'nose', 'intranasal'],
    'vaginal': ['vaginal', 'vaginally', 'pv', 'p.v.']
}

# Common dose frequency patterns
DOSE_FREQUENCIES = {
    'once_daily': ['qd', 'daily', 'once daily', 'once a day', 'q24h', 'q24'],
    'twice_daily': ['bid', 'b.i.d.', 'twice daily', 'twice a day', 'q12h', 'q12'],
    'three_times_daily': ['tid', 't.i.d.', 'three times daily', 'three times a day', 'q8h', 'q8'],
    'four_times_daily': ['qid', 'q.i.d.', 'four times daily', 'four times a day', 'q6h', 'q6'],
    'every_other_day': ['qod', 'q.o.d.', 'every other day', 'alternate days'],
    'weekly': ['weekly', 'once weekly', 'q week', 'q1week'],
    'as_needed': ['prn', 'p.r.n.', 'as needed', 'when needed']
}

# Transplant-specific immunosuppressive medications
IMMUNOSUPPRESSIVE_MEDICATIONS = {
    'calcineurin_inhibitors': [
        'tacrolimus', 'prograf', 'advagraf', 'envarsus',
        'cyclosporine', 'neoral', 'gengraf', 'sandimmune'
    ],
    'antiproliferative_agents': [
        'mycophenolate', 'mycophenolic acid', 'cellcept', 'myfortic',
        'azathioprine', 'imuran'
    ],
    'mtor_inhibitors': [
        'sirolimus', 'rapamune', 'everolimus', 'zortress', 'afinitor'
    ],
    'corticosteroids': [
        'prednisone', 'prednisolone', 'methylprednisolone',
        'hydrocortisone', 'dexamethasone'
    ],
    'induction_agents': [
        'alemtuzumab', 'campath', 'basiliximab', 'simulect',
        'rituximab', 'rituxan', 'thymoglobulin', 'atgam'
    ]
}

# Data validation constants
DATA_VALIDATION = {
    'max_medication_text_length': 500,
    'min_medication_text_length': 2,
    'valid_dose_range': {'min': 0.001, 'max': 10000},
    'valid_year_range': {'min': 2010, 'max': 2030},
    'required_patient_id_pattern': r'^[A-Za-z0-9_-]+$'
}

# File format mappings
FILE_FORMATS = {
    'csv': {
        'extension': '.csv',
        'compression': None,
        'pandas_kwargs': {'index': False}
    },
    'parquet': {
        'extension': '.parquet',
        'compression': 'snappy',
        'pandas_kwargs': {'index': False, 'compression': 'snappy'}
    },
    'json': {
        'extension': '.json',
        'compression': None,
        'pandas_kwargs': {'orient': 'records', 'lines': True}
    }
}


class SourceSystem(Enum):
    """Enumeration of source systems."""
    MAP_PRO_WEB = "map_pro_web"
    MAP_PRO_MOBILE = "map_pro_mobile"
    PIONEER_RX = "pioneer_rx"
    ALLOCARE = "allocare"


class ProcessingStatus(Enum):
    """Enumeration of processing statuses."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ValidationSeverity(Enum):
    """Enumeration of validation error severities."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


# OMOP CDM standard concept IDs for common values
OMOP_STANDARD_CONCEPTS = {
    'drug_types': {
        'prescription_written': 38000177,
        'prescription_dispensed': 38000175,
        'medication_administered': 38000180,
        'patient_reported': 45754907
    },
    'routes': {
        'oral': 4128794,
        'intravenous': 4171047,
        'intramuscular': 4302612,
        'subcutaneous': 4142048,
        'topical': 4262099,
        'inhalation': 4186831
    },
    'units': {
        'mg': 8576,
        'g': 8504,
        'mcg': 9655,
        'ml': 8587,
        'l': 8519,
        'percent': 8554,
        'units': 8510,
        'iu': 8718
    }
}