"""
OMOP DOSE_ERA table builder.

Creates dose era records by consolidating drug exposures with the same
dose and unit into continuous treatment periods.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

from ..utils.logger import LoggerMixin
from ..utils.exceptions import TransformationError


class DoseEraBuilder(LoggerMixin):
    """
    Builder for OMOP CDM DOSE_ERA table.

    Consolidates DRUG_EXPOSURE records with matching dose values and units
    into continuous dose eras, allowing for specified gap periods.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize dose era builder.

        Args:
            config: Configuration dictionary containing era settings
        """
        self.config = config
        self.era_config = config.get('omop', {}).get('dose_era', {})

        # Era construction parameters
        self.gap_days = self.era_config.get('gap_days', 30)
        self.min_era_length_days = self.era_config.get('min_era_length_days', 1)
        self.dose_tolerance = self.era_config.get('dose_tolerance', 0.1)  # 10% tolerance for dose matching

        # Builder statistics
        self.builder_stats = {
            'start_time': None,
            'end_time': None,
            'input_exposures': 0,
            'output_eras': 0,
            'unique_patients': 0,
            'unique_drug_doses': 0,
            'avg_era_length': 0,
            'processing_errors': 0,
            'exposures_with_dose': 0
        }

    def build_dose_era_table(self, drug_exposure_df: pd.DataFrame) -> pd.DataFrame:
        """
        Build DOSE_ERA table from DRUG_EXPOSURE records.

        Args:
            drug_exposure_df: DRUG_EXPOSURE table DataFrame

        Returns:
            DOSE_ERA table DataFrame
        """
        if drug_exposure_df.empty:
            self.logger.warning("Empty DRUG_EXPOSURE table - no dose eras to generate")
            return self._create_empty_dose_era_table()

        self.builder_stats['start_time'] = datetime.now()
        self.builder_stats['input_exposures'] = len(drug_exposure_df)

        self.logger.info(f"Building DOSE_ERA table from {len(drug_exposure_df)} drug exposures")

        try:
            # Validate required columns
            self._validate_drug_exposure_columns(drug_exposure_df)

            # Prepare data for era construction
            prepared_df = self._prepare_exposure_data(drug_exposure_df)

            if prepared_df.empty:
                self.logger.warning("No valid dose exposure records found")
                return self._create_empty_dose_era_table()

            # Build eras by patient, drug, dose, and unit
            era_records = []

            # Group by patient, drug, dose, and unit
            grouping_columns = ['person_id', 'drug_concept_id', 'dose_value', 'dose_unit_concept_id']
            for group_key, group_df in prepared_df.groupby(grouping_columns):
                try:
                    person_id, drug_concept_id, dose_value, unit_concept_id = group_key

                    patient_dose_eras = self._build_eras_for_patient_drug_dose(
                        person_id, drug_concept_id, dose_value, unit_concept_id, group_df
                    )
                    era_records.extend(patient_dose_eras)

                except Exception as e:
                    self.logger.error(f"Error building dose eras for group {group_key}: {str(e)}")
                    self.builder_stats['processing_errors'] += 1
                    continue

            # Create DataFrame from era records
            if era_records:
                dose_era_df = pd.DataFrame(era_records)
                dose_era_df = self._finalize_dose_era_table(dose_era_df)
            else:
                dose_era_df = self._create_empty_dose_era_table()

            # Update statistics
            self._update_builder_statistics(dose_era_df, prepared_df)

            self.logger.info(f"Generated {len(dose_era_df)} dose era records")
            return dose_era_df

        except Exception as e:
            self.builder_stats['end_time'] = datetime.now()
            self.logger.error(f"Dose era building failed: {str(e)}")
            raise TransformationError(
                f"Failed to build DOSE_ERA table: {str(e)}",
                omop_table="DOSE_ERA"
            )

    def _validate_drug_exposure_columns(self, df: pd.DataFrame) -> None:
        """Validate required columns are present."""
        required_columns = [
            'person_id', 'drug_concept_id',
            'drug_exposure_start_date', 'drug_exposure_end_date'
        ]

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise TransformationError(
                f"Missing required columns for dose era building: {missing_columns}",
                omop_table="DOSE_ERA"
            )

    def _prepare_exposure_data(self, drug_exposure_df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare drug exposure data for dose era construction.

        Args:
            drug_exposure_df: Raw drug exposure data

        Returns:
            Cleaned and prepared DataFrame with dose information
        """
        df = drug_exposure_df.copy()

        # Convert dates to datetime
        df['drug_exposure_start_date'] = pd.to_datetime(df['drug_exposure_start_date'])
        df['drug_exposure_end_date'] = pd.to_datetime(df['drug_exposure_end_date'])

        # Handle missing end dates
        df['drug_exposure_end_date'] = df['drug_exposure_end_date'].fillna(
            df['drug_exposure_start_date']
        )

        # Filter records with dose information
        dose_columns = ['dose_value', 'dose_unit_concept_id']
        has_dose_info = (
            df['dose_value'].notna() &
            (df['dose_value'] > 0) &
            df['dose_unit_concept_id'].notna() &
            (df['dose_unit_concept_id'] > 0)
        )

        self.builder_stats['exposures_with_dose'] = has_dose_info.sum()

        if self.builder_stats['exposures_with_dose'] == 0:
            self.logger.warning("No drug exposures with valid dose information found")
            return pd.DataFrame()

        df_with_dose = df[has_dose_info].copy()

        # Standardize dose values for grouping
        df_with_dose = self._standardize_dose_values(df_with_dose)

        # Remove invalid records
        initial_count = len(df_with_dose)
        df_with_dose = df_with_dose[
            (df_with_dose['drug_exposure_start_date'].notna()) &
            (df_with_dose['drug_exposure_end_date'].notna()) &
            (df_with_dose['drug_exposure_start_date'] <= df_with_dose['drug_exposure_end_date']) &
            (df_with_dose['person_id'].notna()) &
            (df_with_dose['drug_concept_id'].notna()) &
            (df_with_dose['drug_concept_id'] > 0)
        ]

        removed_count = initial_count - len(df_with_dose)
        if removed_count > 0:
            self.logger.warning(f"Removed {removed_count} invalid dose exposure records")

        # Sort by patient, drug, dose, unit, and start date
        df_with_dose = df_with_dose.sort_values([
            'person_id', 'drug_concept_id', 'dose_value',
            'dose_unit_concept_id', 'drug_exposure_start_date'
        ])

        return df_with_dose

    def _standardize_dose_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize dose values for consistent grouping.

        Args:
            df: DataFrame with dose information

        Returns:
            DataFrame with standardized dose values
        """
        # Round dose values to reduce precision artifacts
        df['dose_value'] = df['dose_value'].round(3)

        # Handle dose ranges by taking the middle value
        if 'dose_value_max' in df.columns and df['dose_value_max'].notna().any():
            has_range = df['dose_value_max'].notna() & (df['dose_value_max'] != df['dose_value'])
            df.loc[has_range, 'dose_value'] = (
                df.loc[has_range, 'dose_value'] + df.loc[has_range, 'dose_value_max']
            ) / 2

        return df

    def _build_eras_for_patient_drug_dose(
        self,
        person_id: int,
        drug_concept_id: int,
        dose_value: float,
        unit_concept_id: int,
        exposures_df: pd.DataFrame
    ) -> List[Dict[str, Any]]:
        """
        Build dose eras for a specific patient-drug-dose combination.

        Args:
            person_id: Patient identifier
            drug_concept_id: Drug concept identifier
            dose_value: Dose value
            unit_concept_id: Dose unit concept identifier
            exposures_df: Drug exposures for this combination

        Returns:
            List of dose era records
        """
        exposures = exposures_df.sort_values('drug_exposure_start_date').copy()

        eras = []
        current_era_start = None
        current_era_end = None
        current_era_exposures = []

        for _, exposure in exposures.iterrows():
            exposure_start = exposure['drug_exposure_start_date']
            exposure_end = exposure['drug_exposure_end_date']

            if current_era_start is None:
                # Start first era
                current_era_start = exposure_start
                current_era_end = exposure_end
                current_era_exposures = [exposure]

            else:
                # Check if this exposure continues the current era
                gap_days = (exposure_start - current_era_end).days

                if gap_days <= self.gap_days:
                    # Continue current era
                    current_era_end = max(current_era_end, exposure_end)
                    current_era_exposures.append(exposure)

                else:
                    # End current era and start new one
                    era_record = self._create_dose_era_record(
                        person_id, drug_concept_id, dose_value, unit_concept_id,
                        current_era_start, current_era_end, current_era_exposures
                    )
                    eras.append(era_record)

                    # Start new era
                    current_era_start = exposure_start
                    current_era_end = exposure_end
                    current_era_exposures = [exposure]

        # Add final era
        if current_era_start is not None:
            era_record = self._create_dose_era_record(
                person_id, drug_concept_id, dose_value, unit_concept_id,
                current_era_start, current_era_end, current_era_exposures
            )
            eras.append(era_record)

        # Filter eras by minimum length
        valid_eras = []
        for era in eras:
            era_length = (era['dose_era_end_date'] - era['dose_era_start_date']).days + 1
            if era_length >= self.min_era_length_days:
                valid_eras.append(era)

        return valid_eras

    def _create_dose_era_record(
        self,
        person_id: int,
        drug_concept_id: int,
        dose_value: float,
        unit_concept_id: int,
        era_start: datetime,
        era_end: datetime,
        exposures: List[pd.Series]
    ) -> Dict[str, Any]:
        """
        Create a single dose era record.

        Args:
            person_id: Patient identifier
            drug_concept_id: Drug concept identifier
            dose_value: Dose value
            unit_concept_id: Unit concept identifier
            era_start: Era start date
            era_end: Era end date
            exposures: List of exposures in this era

        Returns:
            Dose era record dictionary
        """
        return {
            'person_id': person_id,
            'drug_concept_id': drug_concept_id,
            'unit_concept_id': unit_concept_id,
            'dose_value': dose_value,
            'dose_era_start_date': era_start.date(),
            'dose_era_end_date': era_end.date()
        }

    def _create_empty_dose_era_table(self) -> pd.DataFrame:
        """Create empty DOSE_ERA table with proper schema."""
        return pd.DataFrame(columns=[
            'dose_era_id',
            'person_id',
            'drug_concept_id',
            'unit_concept_id',
            'dose_value',
            'dose_era_start_date',
            'dose_era_end_date'
        ])

    def _finalize_dose_era_table(self, dose_era_df: pd.DataFrame) -> pd.DataFrame:
        """
        Finalize dose era table with proper formatting and IDs.

        Args:
            dose_era_df: Raw dose era DataFrame

        Returns:
            Finalized DOSE_ERA table
        """
        df = dose_era_df.copy()

        # Generate sequential dose_era_id
        df['dose_era_id'] = range(1, len(df) + 1)

        # Ensure proper column types
        df['person_id'] = df['person_id'].astype('Int64')
        df['drug_concept_id'] = df['drug_concept_id'].astype('Int64')
        df['unit_concept_id'] = df['unit_concept_id'].astype('Int64')
        df['dose_value'] = df['dose_value'].astype(float)

        # Convert dates to strings in YYYY-MM-DD format
        df['dose_era_start_date'] = pd.to_datetime(df['dose_era_start_date']).dt.strftime('%Y-%m-%d')
        df['dose_era_end_date'] = pd.to_datetime(df['dose_era_end_date']).dt.strftime('%Y-%m-%d')

        # Reorder columns
        column_order = [
            'dose_era_id',
            'person_id',
            'drug_concept_id',
            'unit_concept_id',
            'dose_value',
            'dose_era_start_date',
            'dose_era_end_date'
        ]

        df = df[column_order]

        # Sort by person, drug, dose, and start date
        df = df.sort_values([
            'person_id', 'drug_concept_id', 'dose_value', 'dose_era_start_date'
        ]).reset_index(drop=True)

        return df

    def _update_builder_statistics(self, dose_era_df: pd.DataFrame, prepared_df: pd.DataFrame) -> None:
        """Update builder statistics."""
        self.builder_stats['end_time'] = datetime.now()
        self.builder_stats['output_eras'] = len(dose_era_df)

        if not dose_era_df.empty:
            self.builder_stats['unique_patients'] = dose_era_df['person_id'].nunique()

            # Count unique drug-dose combinations
            unique_combinations = dose_era_df[['drug_concept_id', 'dose_value', 'unit_concept_id']].drop_duplicates()
            self.builder_stats['unique_drug_doses'] = len(unique_combinations)

            # Calculate average era length
            start_dates = pd.to_datetime(dose_era_df['dose_era_start_date'])
            end_dates = pd.to_datetime(dose_era_df['dose_era_end_date'])
            era_lengths = (end_dates - start_dates).dt.days + 1
            self.builder_stats['avg_era_length'] = era_lengths.mean()

    def get_builder_statistics(self) -> Dict[str, Any]:
        """Get comprehensive builder statistics."""
        stats = self.builder_stats.copy()

        if stats['start_time'] and stats['end_time']:
            execution_time = (stats['end_time'] - stats['start_time']).total_seconds()
            stats['execution_time_seconds'] = execution_time

            if stats['exposures_with_dose'] > 0:
                stats['processing_rate_per_second'] = stats['exposures_with_dose'] / execution_time
                stats['compression_ratio'] = stats['output_eras'] / stats['exposures_with_dose']

            if stats['input_exposures'] > 0:
                stats['dose_coverage_percentage'] = (stats['exposures_with_dose'] / stats['input_exposures']) * 100

        return stats

    def validate_dose_era_output(self, dose_era_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate generated dose era table.

        Args:
            dose_era_df: Generated DOSE_ERA table

        Returns:
            Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }

        if dose_era_df.empty:
            validation_results['warnings'].append("Empty DOSE_ERA table generated")
            return validation_results

        # Check for required columns
        required_columns = [
            'dose_era_id', 'person_id', 'drug_concept_id',
            'unit_concept_id', 'dose_value',
            'dose_era_start_date', 'dose_era_end_date'
        ]

        missing_columns = [col for col in required_columns if col not in dose_era_df.columns]
        if missing_columns:
            validation_results['errors'].append(f"Missing required columns: {missing_columns}")
            validation_results['is_valid'] = False

        # Check for null values in required fields
        for col in required_columns:
            if col in dose_era_df.columns:
                null_count = dose_era_df[col].isnull().sum()
                if null_count > 0:
                    validation_results['errors'].append(f"Null values in {col}: {null_count}")
                    validation_results['is_valid'] = False

        # Validate dose values
        if 'dose_value' in dose_era_df.columns:
            invalid_doses = (dose_era_df['dose_value'] <= 0).sum()
            if invalid_doses > 0:
                validation_results['errors'].append(f"Invalid dose values (≤0): {invalid_doses}")
                validation_results['is_valid'] = False

        # Validate date logic
        if 'dose_era_start_date' in dose_era_df.columns and 'dose_era_end_date' in dose_era_df.columns:
            start_dates = pd.to_datetime(dose_era_df['dose_era_start_date'])
            end_dates = pd.to_datetime(dose_era_df['dose_era_end_date'])

            invalid_dates = (start_dates > end_dates).sum()
            if invalid_dates > 0:
                validation_results['errors'].append(f"Invalid date ranges: {invalid_dates} eras")
                validation_results['is_valid'] = False

        # Statistics
        validation_results['statistics'] = {
            'total_eras': len(dose_era_df),
            'unique_patients': dose_era_df['person_id'].nunique() if 'person_id' in dose_era_df.columns else 0,
            'unique_drugs': dose_era_df['drug_concept_id'].nunique() if 'drug_concept_id' in dose_era_df.columns else 0,
            'unique_drug_dose_combinations': len(dose_era_df[['drug_concept_id', 'dose_value', 'unit_concept_id']].drop_duplicates()) if all(col in dose_era_df.columns for col in ['drug_concept_id', 'dose_value', 'unit_concept_id']) else 0
        }

        return validation_results