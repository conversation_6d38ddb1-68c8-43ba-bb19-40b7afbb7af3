"""
Comprehensive data validation for medication normalization pipeline.
"""

import pandas as pd
import numpy as np
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import json

from ..utils.logger import LoggerMixin
from ..utils.constants import (
    DATA_VALIDATION,
    IMMUNOSUPPRESSIVE_MEDICATIONS,
    STANDARD_DOSE_UNITS,
    ValidationSeverity
)
from ..utils.exceptions import ValidationError


class ValidationRule:
    """Individual validation rule definition."""

    def __init__(
        self,
        name: str,
        description: str,
        severity: ValidationSeverity,
        column: str,
        condition_func: callable,
        error_message_func: callable = None
    ):
        self.name = name
        self.description = description
        self.severity = severity
        self.column = column
        self.condition_func = condition_func
        self.error_message_func = error_message_func or (lambda x: f"Validation failed for {name}")


class ValidationResult:
    """Validation result for a single rule."""

    def __init__(
        self,
        rule_name: str,
        severity: ValidationSeverity,
        passed: bool,
        failed_count: int = 0,
        total_count: int = 0,
        failed_records: List[Dict] = None,
        message: str = ""
    ):
        self.rule_name = rule_name
        self.severity = severity
        self.passed = passed
        self.failed_count = failed_count
        self.total_count = total_count
        self.failed_records = failed_records or []
        self.message = message
        self.pass_rate = ((total_count - failed_count) / total_count * 100) if total_count > 0 else 100


class DataValidator(LoggerMixin):
    """
    Comprehensive data validation for medication data pipeline.

    Validates data quality, completeness, and business rules compliance
    across extraction, normalization, and transformation stages.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize data validator.

        Args:
            config: Validation configuration
        """
        self.config = config
        self.quality_config = config.get('quality', {})
        self.validation_rules = self._initialize_validation_rules()
        self.validation_history = []

    def _initialize_validation_rules(self) -> List[ValidationRule]:
        """Initialize standard validation rules."""
        rules = []

        # Required field validations
        rules.extend([
            ValidationRule(
                name="source_system_required",
                description="Source system must be specified",
                severity=ValidationSeverity.CRITICAL,
                column="source_system",
                condition_func=lambda df: df['source_system'].notna() & (df['source_system'] != '')
            ),
            ValidationRule(
                name="patient_id_required",
                description="Patient ID must be specified",
                severity=ValidationSeverity.CRITICAL,
                column="patient_id",
                condition_func=lambda df: df['patient_id'].notna() & (df['patient_id'] != '')
            ),
            ValidationRule(
                name="medication_text_required",
                description="Medication text must be specified",
                severity=ValidationSeverity.CRITICAL,
                column="medication_text",
                condition_func=lambda df: df['medication_text'].notna() & (df['medication_text'] != '')
            )
        ])

        # Data format validations
        rules.extend([
            ValidationRule(
                name="medication_text_length",
                description="Medication text must be within reasonable length",
                severity=ValidationSeverity.ERROR,
                column="medication_text",
                condition_func=lambda df: (
                    df['medication_text'].str.len() >= DATA_VALIDATION['min_medication_text_length']
                ) & (
                    df['medication_text'].str.len() <= DATA_VALIDATION['max_medication_text_length']
                )
            ),
            ValidationRule(
                name="patient_id_format",
                description="Patient ID must follow valid format",
                severity=ValidationSeverity.ERROR,
                column="patient_id",
                condition_func=lambda df: df['patient_id'].astype(str).str.match(
                    DATA_VALIDATION['required_patient_id_pattern']
                )
            ),
            ValidationRule(
                name="dose_value_range",
                description="Dose values must be within reasonable range",
                severity=ValidationSeverity.WARNING,
                column="dose_value",
                condition_func=lambda df: (
                    df['dose_value'].isna() |
                    ((df['dose_value'] >= DATA_VALIDATION['valid_dose_range']['min']) &
                     (df['dose_value'] <= DATA_VALIDATION['valid_dose_range']['max']))
                )
            )
        ])

        # Date validations
        rules.extend([
            ValidationRule(
                name="start_date_validity",
                description="Start dates must be valid and within reasonable range",
                severity=ValidationSeverity.ERROR,
                column="start_date",
                condition_func=lambda df: self._validate_date_range(df, 'start_date')
            ),
            ValidationRule(
                name="end_date_validity",
                description="End dates must be valid and within reasonable range",
                severity=ValidationSeverity.WARNING,
                column="end_date",
                condition_func=lambda df: self._validate_date_range(df, 'end_date')
            ),
            ValidationRule(
                name="date_logic_consistency",
                description="End date should not be before start date",
                severity=ValidationSeverity.WARNING,
                column="start_date",
                condition_func=lambda df: self._validate_date_logic(df)
            )
        ])

        # Normalization quality validations
        rules.extend([
            ValidationRule(
                name="mapping_success_rate",
                description="Adequate percentage of medications should be mapped to RxNorm",
                severity=ValidationSeverity.WARNING,
                column="final_rxcui",
                condition_func=lambda df: self._validate_mapping_rate(df, min_rate=0.7)
            ),
            ValidationRule(
                name="transplant_medication_coverage",
                description="Known transplant medications should be properly identified",
                severity=ValidationSeverity.INFO,
                column="medication_text",
                condition_func=lambda df: self._validate_transplant_coverage(df)
            ),
            ValidationRule(
                name="dose_normalization_completeness",
                description="Dose information should be normalized when available",
                severity=ValidationSeverity.INFO,
                column="normalized_dose_value",
                condition_func=lambda df: self._validate_dose_completeness(df)
            )
        ])

        # Business logic validations
        rules.extend([
            ValidationRule(
                name="duplicate_detection",
                description="Check for potential duplicate medication records",
                severity=ValidationSeverity.WARNING,
                column="patient_id",
                condition_func=lambda df: self._validate_duplicates(df)
            ),
            ValidationRule(
                name="source_consistency",
                description="Data should be consistent within each source system",
                severity=ValidationSeverity.INFO,
                column="source_system",
                condition_func=lambda df: self._validate_source_consistency(df)
            )
        ])

        return rules

    def _validate_date_range(self, df: pd.DataFrame, date_column: str) -> pd.Series:
        """Validate date range reasonableness."""
        if date_column not in df.columns:
            return pd.Series([True] * len(df))

        date_series = pd.to_datetime(df[date_column], errors='coerce')
        min_year = DATA_VALIDATION['valid_year_range']['min']
        max_year = DATA_VALIDATION['valid_year_range']['max']

        return (
            date_series.isna() |
            ((date_series.dt.year >= min_year) & (date_series.dt.year <= max_year))
        )

    def _validate_date_logic(self, df: pd.DataFrame) -> pd.Series:
        """Validate date logic consistency."""
        if 'start_date' not in df.columns or 'end_date' not in df.columns:
            return pd.Series([True] * len(df))

        start_dates = pd.to_datetime(df['start_date'], errors='coerce')
        end_dates = pd.to_datetime(df['end_date'], errors='coerce')

        return (
            start_dates.isna() | end_dates.isna() | (end_dates >= start_dates)
        )

    def _validate_mapping_rate(self, df: pd.DataFrame, min_rate: float = 0.7) -> pd.Series:
        """Validate overall mapping success rate."""
        if df.empty:
            return pd.Series([True])

        mapped_count = df['final_rxcui'].notna().sum()
        total_count = len(df)
        mapping_rate = mapped_count / total_count if total_count > 0 else 0

        # Return series indicating if mapping rate is acceptable
        return pd.Series([mapping_rate >= min_rate] * len(df))

    def _validate_transplant_coverage(self, df: pd.DataFrame) -> pd.Series:
        """Validate transplant medication identification."""
        if df.empty or 'medication_text' not in df.columns:
            return pd.Series([True] * len(df))

        # Get all transplant medication names
        transplant_meds = []
        for category, meds in IMMUNOSUPPRESSIVE_MEDICATIONS.items():
            transplant_meds.extend([med.lower() for med in meds])

        # Check if known transplant medications are flagged
        results = []
        for _, row in df.iterrows():
            med_text = str(row.get('medication_text', '')).lower()
            is_known_transplant = any(med in med_text for med in transplant_meds)
            is_flagged = row.get('is_transplant_medication', False)

            # Pass if not a transplant med, or if properly flagged
            results.append(not is_known_transplant or is_flagged)

        return pd.Series(results)

    def _validate_dose_completeness(self, df: pd.DataFrame) -> pd.Series:
        """Validate dose normalization completeness."""
        if df.empty:
            return pd.Series([True])

        # Check if records with dose info were normalized
        has_original_dose = df['dose_value'].notna() | df['dose_unit'].notna()
        has_normalized_dose = df.get('normalized_dose_value', pd.Series()).notna()

        # Pass if no original dose, or if normalized when dose exists
        return ~has_original_dose | has_normalized_dose

    def _validate_duplicates(self, df: pd.DataFrame) -> pd.Series:
        """Detect potential duplicate records."""
        if df.empty:
            return pd.Series([True])

        # Define duplicate criteria
        duplicate_cols = ['patient_id', 'medication_text', 'start_date']
        available_cols = [col for col in duplicate_cols if col in df.columns]

        if len(available_cols) < 2:
            return pd.Series([True] * len(df))

        # Mark duplicates
        is_duplicate = df.duplicated(subset=available_cols, keep=False)

        # Return inverted (True = not duplicate)
        return ~is_duplicate

    def _validate_source_consistency(self, df: pd.DataFrame) -> pd.Series:
        """Validate data consistency within source systems."""
        # This is a placeholder for source-specific consistency checks
        # Could be expanded based on specific business rules
        return pd.Series([True] * len(df))

    def validate_dataframe(
        self,
        df: pd.DataFrame,
        validation_stage: str = "unknown",
        severity_threshold: ValidationSeverity = ValidationSeverity.WARNING
    ) -> Dict[str, Any]:
        """
        Validate a DataFrame against all applicable rules.

        Args:
            df: DataFrame to validate
            validation_stage: Stage of validation (extraction, normalization, transformation)
            severity_threshold: Minimum severity level to report

        Returns:
            Comprehensive validation report
        """
        if df.empty:
            return {
                'status': 'skipped',
                'message': 'Empty DataFrame - no validation performed',
                'summary': {},
                'details': []
            }

        self.logger.info(f"Starting {validation_stage} validation on {len(df)} records")

        validation_results = []
        overall_status = 'passed'
        critical_failures = 0
        error_failures = 0
        warning_failures = 0

        # Run each validation rule
        for rule in self.validation_rules:
            try:
                # Check if rule applies to this DataFrame
                if rule.column not in df.columns and rule.column != 'patient_id':
                    continue

                # Apply validation condition
                valid_mask = rule.condition_func(df)
                failed_mask = ~valid_mask

                failed_count = failed_mask.sum()
                total_count = len(df)

                # Create result
                result = ValidationResult(
                    rule_name=rule.name,
                    severity=rule.severity,
                    passed=failed_count == 0,
                    failed_count=failed_count,
                    total_count=total_count,
                    message=rule.description
                )

                # Collect sample failed records
                if failed_count > 0:
                    failed_df = df[failed_mask].head(10)  # Limit sample size
                    result.failed_records = failed_df.to_dict('records')

                    # Update overall status
                    if rule.severity == ValidationSeverity.CRITICAL:
                        overall_status = 'failed'
                        critical_failures += 1
                    elif rule.severity == ValidationSeverity.ERROR:
                        if overall_status == 'passed':
                            overall_status = 'warnings'
                        error_failures += 1
                    elif rule.severity == ValidationSeverity.WARNING:
                        if overall_status == 'passed':
                            overall_status = 'warnings'
                        warning_failures += 1

                validation_results.append(result)

                # Log significant issues
                if failed_count > 0 and rule.severity.value <= severity_threshold.value:
                    self.logger.warning(
                        f"Validation rule '{rule.name}' failed for {failed_count}/{total_count} "
                        f"records ({failed_count/total_count*100:.1f}%) - {rule.description}"
                    )

            except Exception as e:
                self.logger.error(f"Validation rule '{rule.name}' encountered error: {str(e)}")
                validation_results.append(ValidationResult(
                    rule_name=rule.name,
                    severity=ValidationSeverity.ERROR,
                    passed=False,
                    failed_count=len(df),
                    total_count=len(df),
                    message=f"Validation error: {str(e)}"
                ))
                error_failures += 1
                overall_status = 'failed'

        # Generate summary
        total_rules = len(validation_results)
        passed_rules = sum(1 for r in validation_results if r.passed)

        summary = {
            'validation_stage': validation_stage,
            'overall_status': overall_status,
            'total_records': len(df),
            'total_rules': total_rules,
            'passed_rules': passed_rules,
            'failed_rules': total_rules - passed_rules,
            'critical_failures': critical_failures,
            'error_failures': error_failures,
            'warning_failures': warning_failures,
            'validation_timestamp': datetime.now().isoformat()
        }

        # Store validation history
        self.validation_history.append({
            'timestamp': datetime.now(),
            'stage': validation_stage,
            'status': overall_status,
            'record_count': len(df),
            'rule_failures': {
                'critical': critical_failures,
                'error': error_failures,
                'warning': warning_failures
            }
        })

        report = {
            'status': overall_status,
            'summary': summary,
            'rule_results': [
                {
                    'rule_name': r.rule_name,
                    'severity': r.severity.value,
                    'passed': r.passed,
                    'failed_count': r.failed_count,
                    'total_count': r.total_count,
                    'pass_rate': r.pass_rate,
                    'message': r.message,
                    'sample_failures': r.failed_records[:5] if r.failed_records else []
                }
                for r in validation_results
                if r.severity.value <= severity_threshold.value
            ]
        }

        self.logger.info(
            f"Validation complete: {overall_status.upper()} "
            f"({passed_rules}/{total_rules} rules passed)"
        )

        return report

    def validate_extraction_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate extracted raw medication data."""
        return self.validate_dataframe(df, "extraction", ValidationSeverity.ERROR)

    def validate_normalized_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate normalized medication data."""
        return self.validate_dataframe(df, "normalization", ValidationSeverity.WARNING)

    def validate_omop_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate OMOP-transformed data."""
        return self.validate_dataframe(df, "omop_transformation", ValidationSeverity.ERROR)

    def generate_validation_report(
        self,
        validation_results: List[Dict[str, Any]],
        output_path: Optional[str] = None
    ) -> str:
        """
        Generate comprehensive validation report.

        Args:
            validation_results: List of validation result dictionaries
            output_path: Optional path to save report

        Returns:
            Report text
        """
        report_lines = [
            "# Data Validation Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ""
        ]

        # Executive summary
        total_records = sum(r['summary']['total_records'] for r in validation_results)
        stages = [r['summary']['validation_stage'] for r in validation_results]

        report_lines.extend([
            "## Executive Summary",
            f"- Total Records Validated: {total_records:,}",
            f"- Validation Stages: {', '.join(stages)}",
            f"- Overall Status: {self._get_overall_status(validation_results)}",
            ""
        ])

        # Stage-by-stage results
        for result in validation_results:
            summary = result['summary']

            report_lines.extend([
                f"## {summary['validation_stage'].title()} Validation",
                f"- Records: {summary['total_records']:,}",
                f"- Status: {summary['overall_status'].upper()}",
                f"- Rules Passed: {summary['passed_rules']}/{summary['total_rules']}",
                f"- Critical Failures: {summary['critical_failures']}",
                f"- Error Failures: {summary['error_failures']}",
                f"- Warning Failures: {summary['warning_failures']}",
                ""
            ])

            # Rule details
            if result.get('rule_results'):
                report_lines.append("### Rule Details")
                for rule in result['rule_results']:
                    if not rule['passed']:
                        report_lines.append(
                            f"- **{rule['rule_name']}** ({rule['severity']}): "
                            f"{rule['failed_count']:,} failures ({100-rule['pass_rate']:.1f}%)"
                        )
                        report_lines.append(f"  - {rule['message']}")

                report_lines.append("")

        # Recommendations
        report_lines.extend([
            "## Recommendations",
            self._generate_recommendations(validation_results),
            ""
        ])

        # Validation history
        if self.validation_history:
            report_lines.extend([
                "## Validation History",
                f"- Total Validations: {len(self.validation_history)}",
            ])

            for hist in self.validation_history[-5:]:  # Last 5 validations
                report_lines.append(
                    f"- {hist['timestamp'].strftime('%Y-%m-%d %H:%M')} - "
                    f"{hist['stage']}: {hist['status']} "
                    f"({hist['record_count']:,} records)"
                )

        report_text = "\n".join(report_lines)

        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            self.logger.info(f"Validation report saved to {output_path}")

        return report_text

    def _get_overall_status(self, validation_results: List[Dict[str, Any]]) -> str:
        """Determine overall validation status."""
        statuses = [r['summary']['overall_status'] for r in validation_results]

        if 'failed' in statuses:
            return 'FAILED'
        elif 'warnings' in statuses:
            return 'WARNINGS'
        else:
            return 'PASSED'

    def _generate_recommendations(self, validation_results: List[Dict[str, Any]]) -> str:
        """Generate recommendations based on validation results."""
        recommendations = []

        for result in validation_results:
            if result['summary']['critical_failures'] > 0:
                recommendations.append(
                    "- **CRITICAL**: Address critical validation failures before proceeding to production"
                )

            if result['summary']['error_failures'] > 0:
                recommendations.append(
                    "- **ERROR**: Review and fix data quality issues in source systems"
                )

            # Check mapping rates
            for rule in result.get('rule_results', []):
                if rule['rule_name'] == 'mapping_success_rate' and not rule['passed']:
                    recommendations.append(
                        "- **MAPPING**: Consider expanding reference data or improving fuzzy matching parameters"
                    )

                if rule['rule_name'] == 'transplant_medication_coverage' and not rule['passed']:
                    recommendations.append(
                        "- **TRANSPLANT**: Review transplant medication identification logic"
                    )

        if not recommendations:
            recommendations.append("- All validations passed - data quality appears good")

        return "\n".join(recommendations)

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation statistics and configuration info."""
        return {
            'total_rules': len(self.validation_rules),
            'rule_categories': {
                'critical': len([r for r in self.validation_rules if r.severity == ValidationSeverity.CRITICAL]),
                'error': len([r for r in self.validation_rules if r.severity == ValidationSeverity.ERROR]),
                'warning': len([r for r in self.validation_rules if r.severity == ValidationSeverity.WARNING]),
                'info': len([r for r in self.validation_rules if r.severity == ValidationSeverity.INFO])
            },
            'validation_history_count': len(self.validation_history),
            'configuration': {
                'dose_range': DATA_VALIDATION['valid_dose_range'],
                'year_range': DATA_VALIDATION['valid_year_range'],
                'text_length_limits': {
                    'min': DATA_VALIDATION['min_medication_text_length'],
                    'max': DATA_VALIDATION['max_medication_text_length']
                }
            }
        }