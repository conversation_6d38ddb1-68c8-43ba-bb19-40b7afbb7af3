# How to Run the Medication Normalization Pipeline

This guide provides complete step-by-step instructions for running the medication normalization pipeline that transforms transplant patient data into OMOP CDM format.

## Table of Contents
- [Quick Start (5 minutes)](#quick-start-5-minutes)
- [Detailed Setup Guide](#detailed-setup-guide)
- [Running Options](#running-options)
- [Expected Output](#expected-output)
- [Troubleshooting](#troubleshooting)
- [Production Deployment](#production-deployment)

## Quick Start (5 minutes)

### 1. Set Up Environment
```bash
# Navigate to the project directory
cd /mnt/c/Users/<USER>/OneDrive\ -\ CareDx,\ Inc/Documents/Work/Scripts/Medication_Normalization

# Install Python dependencies
pip install pandas numpy sqlalchemy psycopg2-binary requests pyyaml rapidfuzz
```

### 2. Configure Database Connections
Create a `.env` file with your database credentials:
```bash
# Create .env file
cat > .env << 'EOF'
# MAP Pro Web Database
MAP_PRO_WEB_CONNECTION_STRING="postgresql://username:password@host:port/database"

# MAP Pro Mobile Database
MAP_PRO_MOBILE_CONNECTION_STRING="postgresql://username:password@host:port/database"

# PioneerRx Database
PIONEER_RX_CONNECTION_STRING="postgresql://username:password@host:port/database"

# AlloCare Database
ALLOCARE_CONNECTION_STRING="postgresql://username:password@host:port/database"

# Data Warehouse (optional)
DATA_WAREHOUSE_CONNECTION_STRING="postgresql://username:password@host:port/warehouse_db"

# Security
PERSON_ID_SALT="your_secure_random_salt_string"
EOF
```

### 3. Test Connections
```bash
python curtis_integration/test_connections.py --config config/pipeline_config.yaml
```

### 4. Run the Pipeline
```bash
# Run complete pipeline for all sources
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/

# Or run for specific sources only
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --sources map_pro pioneer_rx \
  --output-dir output/
```

---

## Detailed Setup Guide

### Prerequisites

#### System Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows, Linux, or macOS
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 1GB free space for output files

#### Database Access
- **Read access** to source databases (MAP Pro, PioneerRx, AlloCare)
- **Write access** to data warehouse (for loading OMOP tables)
- **Network connectivity** to RxNorm API (optional but recommended)

### Step 1: Environment Setup

#### Option A: Virtual Environment (Recommended)
```bash
# Create virtual environment
python3 -m venv medication_pipeline_env

# Activate virtual environment
# On Linux/Mac:
source medication_pipeline_env/bin/activate
# On Windows:
medication_pipeline_env\Scripts\activate

# Install required packages
pip install --upgrade pip
pip install pandas>=1.5.0 numpy>=1.21.0 sqlalchemy>=1.4.0 psycopg2-binary>=2.9.0 requests>=2.28.0 pyyaml>=6.0 rapidfuzz>=2.0.0
```

#### Option B: System-wide Installation
```bash
# Install required packages system-wide
pip install pandas numpy sqlalchemy psycopg2-binary requests pyyaml rapidfuzz
```

#### Create Requirements File (Optional)
```bash
# Create requirements.txt for reproducible installs
cat > requirements.txt << 'EOF'
pandas>=1.5.0
numpy>=1.21.0
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.0
requests>=2.28.0
pyyaml>=6.0
rapidfuzz>=2.0.0
EOF

# Install from requirements file
pip install -r requirements.txt
```

### Step 2: Configuration Setup

#### Option A: Environment Variables (.env file)
```bash
# Create .env file in project root
nano .env

# Add your database connection strings:
MAP_PRO_WEB_CONNECTION_STRING="********************************/mapro_web"
MAP_PRO_MOBILE_CONNECTION_STRING="********************************/mapro_mobile"
PIONEER_RX_CONNECTION_STRING="********************************/pioneer_rx"
ALLOCARE_CONNECTION_STRING="********************************/allocare"
DATA_WAREHOUSE_CONNECTION_STRING="********************************/warehouse"
PERSON_ID_SALT="medication_normalization_secure_salt_2024"
RXNORM_API_KEY="your_api_key_if_required"
WEBHOOK_URL="https://your-notification-webhook.com"
```

#### Option B: Direct Configuration Edit
```bash
# Edit source configuration file
nano config/source_configs.yaml

# Update connection details:
databases:
  map_pro_web:
    host: "your_host"
    port: 5432
    database: "mapro_web"
    username: "your_username"
    password: "your_password"
  # ... repeat for other sources
```

#### Option C: Mixed Approach (Recommended)
Use environment variables for sensitive data and config files for settings:
```bash
# In .env file (sensitive data)
DB_PASSWORD="your_secure_password"
DB_USERNAME="your_username"

# In config files (non-sensitive settings)
host: "${DB_HOST:-localhost}"
username: "${DB_USERNAME}"
password: "${DB_PASSWORD}"
```

### Step 3: Validate Setup

#### Test Database Connections
```bash
# Test all configured connections
python curtis_integration/test_connections.py

# Expected output:
# ✅ MAP Pro Web: Connection successful
# ✅ MAP Pro Mobile: Connection successful
# ✅ PioneerRx: Connection successful
# ✅ AlloCare: Connection successful
```

#### Test RxNorm API (Optional)
```bash
# Test RxNorm API connectivity
python curtis_integration/test_connections.py --test-rxnorm

# Expected output:
# ✅ RxNorm API: Connection successful
```

#### Generate Connection Report
```bash
# Generate detailed connection report
python curtis_integration/test_connections.py \
  --generate-report connection_test_report.json

# View report
cat connection_test_report.json
```

---

## Running Options

### Basic Pipeline Execution

#### Run All Sources
```bash
# Process all four data sources
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/$(date +%Y%m%d_%H%M%S)
```

#### Run Specific Sources
```bash
# Process only MAP Pro sources
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --sources map_pro \
  --output-dir output/mapro_only

# Process MAP Pro and PioneerRx
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --sources map_pro pioneer_rx \
  --output-dir output/mapro_pioneer
```

#### Development/Testing Options
```bash
# Skip RxNorm normalization (faster for testing)
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --skip-normalization \
  --output-dir output/extraction_only

# Skip OMOP transformation (test normalization only)
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --skip-transformation \
  --output-dir output/normalization_only

# Validation only (on existing output)
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/existing_run \
  --validate-only
```

#### Verbose Execution
```bash
# Run with detailed logging
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/debug_run \
  --verbose
```

### Daily Production Workflow

#### Automated Daily ETL
```bash
# Make script executable
chmod +x curtis_integration/daily_medication_etl.sh

# Run complete daily workflow
./curtis_integration/daily_medication_etl.sh

# The script will:
# 1. Test database connections
# 2. Run full pipeline
# 3. Validate output
# 4. Load to data warehouse
# 5. Generate quality report
# 6. Archive old runs
```

#### Manual Daily Steps
```bash
# 1. Run pipeline
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/daily_$(date +%Y%m%d)

# 2. Load to data warehouse
python curtis_integration/data_warehouse_loader.py \
  --input-dir output/daily_$(date +%Y%m%d) \
  --target-schema omop_cdm

# 3. Generate quality report
python curtis_integration/quality_monitor.py \
  --output-dir output/daily_$(date +%Y%m%d)
```

### Data Warehouse Loading

#### Incremental Loading (Recommended)
```bash
python curtis_integration/data_warehouse_loader.py \
  --input-dir output/daily_20241201_120000 \
  --target-schema omop_cdm \
  --load-strategy incremental \
  --validate-before-load \
  --create-indexes
```

#### Full Replacement Loading
```bash
python curtis_integration/data_warehouse_loader.py \
  --input-dir output/daily_20241201_120000 \
  --target-schema omop_cdm \
  --load-strategy full \
  --validate-before-load
```

#### Custom Loading Options
```bash
# Load to staging schema first
python curtis_integration/data_warehouse_loader.py \
  --input-dir output/daily_20241201_120000 \
  --target-schema staging_omop \
  --load-strategy incremental

# Load without validation (faster)
python curtis_integration/data_warehouse_loader.py \
  --input-dir output/daily_20241201_120000 \
  --target-schema omop_cdm \
  --load-strategy incremental \
  --no-validate-before-load
```

### Quality Monitoring

#### Basic Quality Assessment
```bash
python curtis_integration/quality_monitor.py \
  --output-dir output/daily_20241201_120000 \
  --generate-report
```

#### Custom Quality Thresholds
```bash
# Create custom thresholds file
cat > custom_thresholds.json << 'EOF'
{
  "min_mapping_coverage": 0.85,
  "transplant_medication_coverage": 0.98,
  "confidence_threshold": 0.75
}
EOF

# Run with custom thresholds
python curtis_integration/quality_monitor.py \
  --output-dir output/daily_20241201_120000 \
  --threshold-override custom_thresholds.json
```

#### Trend Analysis
```bash
# Include trend analysis (requires historical data)
python curtis_integration/quality_monitor.py \
  --output-dir output/daily_20241201_120000 \
  --include-trends \
  --generate-report
```

---

## Expected Output

### Successful Pipeline Execution
```
==============================================================
MEDICATION NORMALIZATION PIPELINE RESULTS
==============================================================
Status: SUCCESS
Input Records: 15,423
Output Records: 18,901
Execution Time: 127.45 seconds

Generated OMOP Tables:
  DRUG_EXPOSURE: 15,423 records
  DRUG_ERA: 2,876 records
  DOSE_ERA: 1,902 records
  CONCEPT: 1,247 records
  CONCEPT_RELATIONSHIP: 854 records
  VOCABULARY: 5 records

✅ Pipeline completed successfully!
```

### Generated Files Structure
```
output/daily_20241201_120000/
├── DRUG_EXPOSURE.parquet          # Primary medication records
├── DRUG_ERA.parquet               # Medication treatment periods
├── DOSE_ERA.parquet               # Dose-specific treatment periods
├── CONCEPT.parquet                # Vocabulary concepts
├── CONCEPT_RELATIONSHIP.parquet   # Concept mappings
├── VOCABULARY.parquet             # Vocabulary metadata
├── execution_summary.json         # Detailed execution statistics
├── medication_mappings.csv        # Source→RxNorm mappings
└── quality_report_20241201_120000.md  # Quality assessment report
```

### Quality Assessment Output
```
==============================================================
QUALITY ASSESSMENT RESULTS
==============================================================
Overall Quality Score: 87.3%
Data Quality: 92.1%
Mapping Quality: 84.2%
OMOP Compliance: 95.8%

Recommendations: 2
  HIGH - Mapping Quality: Mapping coverage is 84%
  MEDIUM - OMOP Compliance: OMOP compliance score is 95.8%

Detailed report: output/daily_20241201_120000/quality_report_20241201_120000.md

✅ Quality assessment PASSED - Good quality with room for improvement
```

### Data Warehouse Loading Output
```
==============================================================
DATA WAREHOUSE LOADING RESULTS
==============================================================
Status: SUCCESS
Tables Processed: 6
Records Loaded: 18,901
Execution Time: 23.67 seconds

Table Loading Results:
  ✅ DRUG_EXPOSURE: 15,423 records
  ✅ DRUG_ERA: 2,876 records
  ✅ DOSE_ERA: 1,902 records
  ✅ CONCEPT: 1,247 records
  ✅ CONCEPT_RELATIONSHIP: 854 records
  ✅ VOCABULARY: 5 records

✅ Data warehouse loading completed successfully!
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Database Connection Failures

**Problem**: `Connection failed - could not connect to server`

**Solutions**:
```bash
# Test connection manually
python -c "
import os
from sqlalchemy import create_engine
engine = create_engine(os.getenv('MAP_PRO_WEB_CONNECTION_STRING'))
with engine.connect() as conn:
    print('Connection successful!')
"

# Check environment variables
echo $MAP_PRO_WEB_CONNECTION_STRING

# Verify database server is running
ping your_database_host

# Check firewall and port access
telnet your_database_host 5432
```

#### 2. Permission Issues

**Problem**: `Permission denied` or `Access denied`

**Solutions**:
```bash
# Make scripts executable
chmod +x curtis_integration/*.sh
chmod +x medication_normalization_pipeline.py

# Check database user permissions
psql -h your_host -U your_user -d your_db -c "SELECT current_user;"

# Verify read access to source tables
psql -h your_host -U your_user -d your_db -c "SELECT count(*) FROM medication_table;"
```

#### 3. Missing Dependencies

**Problem**: `ModuleNotFoundError: No module named 'pandas'`

**Solutions**:
```bash
# Install missing packages
pip install --upgrade pip
pip install pandas numpy sqlalchemy psycopg2-binary requests pyyaml rapidfuzz

# Check Python path
python -c "import sys; print(sys.path)"

# Verify virtual environment activation
which python
```

#### 4. RxNorm API Issues

**Problem**: `RxNorm API connection failed`

**Solutions**:
```bash
# Test RxNorm API manually
curl "https://rxnav.nlm.nih.gov/REST/rxcui.json?name=aspirin"

# Check proxy settings
export https_proxy=your_proxy_if_needed

# Use cached mappings only (disable API)
# Edit config/rxnorm_config.yaml:
# api:
#   enabled: false
```

#### 5. Memory Issues

**Problem**: `MemoryError` or slow performance

**Solutions**:
```bash
# Reduce batch size in config/pipeline_config.yaml:
# pipeline:
#   processing:
#     batch_size: 500  # Reduce from 1000

# Monitor memory usage
top -p $(pgrep -f medication_normalization_pipeline)

# Process sources separately
python medication_normalization_pipeline.py --sources map_pro --output-dir output/mapro
python medication_normalization_pipeline.py --sources pioneer_rx --output-dir output/pioneer
```

#### 6. Validation Failures

**Problem**: `Validation failed` or low quality scores

**Solutions**:
```bash
# Run validation with detailed output
python curtis_integration/quality_monitor.py \
  --output-dir output/failed_run \
  --generate-report

# Check specific validation errors
cat output/failed_run/quality_report_*.md

# Lower quality thresholds temporarily
echo '{"min_mapping_coverage": 0.60}' > lower_thresholds.json
python curtis_integration/quality_monitor.py \
  --output-dir output/failed_run \
  --threshold-override lower_thresholds.json
```

### Debug Mode

#### Enable Verbose Logging
```bash
# Run with maximum verbosity
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/debug \
  --verbose

# Check log files
tail -f logs/medication_pipeline_*.log

# View structured JSON logs
cat logs/medication_pipeline_*.log | jq '.'
```

#### Component-Level Debugging
```bash
# Test individual components
python -c "
from src.extractors import MAPProExtractor
import yaml
with open('config/pipeline_config.yaml') as f:
    config = yaml.safe_load(f)
extractor = MAPProExtractor(config)
print('Extractor initialized successfully')
"
```

### Performance Optimization

#### Improve Processing Speed
```bash
# Increase parallel workers (if you have more CPU cores)
# Edit config/pipeline_config.yaml:
# pipeline:
#   processing:
#     max_workers: 8  # Increase from 4

# Enable more aggressive caching
# Edit config/rxnorm_config.yaml:
# cache:
#   enabled: true
#   duration_hours: 72  # Increase from 24
```

#### Reduce Memory Usage
```bash
# Process in smaller batches
# Edit config/pipeline_config.yaml:
# pipeline:
#   processing:
#     batch_size: 500  # Reduce from 1000

# Skip vocabulary generation for testing
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/no_vocab \
  # (modify config to set include_vocabulary: false)
```

---

## Production Deployment

### Scheduling for Production

#### Daily Cron Job
```bash
# Edit crontab
crontab -e

# Add daily run at 2 AM
0 2 * * * cd /path/to/medication_normalization && ./curtis_integration/daily_medication_etl.sh >> logs/cron.log 2>&1

# Add weekly cleanup at 3 AM on Sundays
0 3 * * 0 cd /path/to/medication_normalization && find archive/ -name "*.tar.gz" -mtime +90 -delete
```

#### Systemd Service (Linux)
```bash
# Create service file
sudo nano /etc/systemd/system/medication-etl.service

# Add content:
[Unit]
Description=Medication Normalization ETL
After=network.target

[Service]
Type=oneshot
User=your_user
WorkingDirectory=/path/to/medication_normalization
ExecStart=/path/to/medication_normalization/curtis_integration/daily_medication_etl.sh
StandardOutput=journal
StandardError=journal

# Create timer file
sudo nano /etc/systemd/system/medication-etl.timer

# Add content:
[Unit]
Description=Run Medication ETL Daily
Requires=medication-etl.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target

# Enable and start
sudo systemctl enable medication-etl.timer
sudo systemctl start medication-etl.timer
```

### Monitoring and Alerting

#### Webhook Notifications
```bash
# Set webhook URL for notifications
export WEBHOOK_URL="https://hooks.slack.com/your/webhook/url"

# Test webhook
curl -X POST $WEBHOOK_URL \
  -H 'Content-Type: application/json' \
  -d '{"text":"Test notification from medication pipeline"}'
```

#### Log Monitoring
```bash
# Monitor logs in real-time
tail -f logs/daily_etl_*.log

# Set up log rotation
echo '/path/to/medication_normalization/logs/*.log {
    daily
    rotate 30
    compress
    missingok
    notifempty
    create 644 your_user your_group
}' | sudo tee /etc/logrotate.d/medication-pipeline
```

#### Health Checks
```bash
# Create health check script
cat > health_check.sh << 'EOF'
#!/bin/bash
# Health check for medication normalization pipeline

# Check last successful run
LAST_SUCCESS=$(find output/ -name "daily_*" -type d -mtime -1 | wc -l)
if [ $LAST_SUCCESS -eq 0 ]; then
    echo "ERROR: No successful runs in last 24 hours"
    exit 1
fi

# Check quality scores
LATEST_DIR=$(find output/ -name "daily_*" -type d | sort | tail -1)
if [ -d "$LATEST_DIR" ]; then
    QUALITY_SCORE=$(grep "Overall Quality Score" "$LATEST_DIR"/quality_report_*.md | grep -o '[0-9.]*%' | head -1)
    echo "Latest quality score: $QUALITY_SCORE"
fi

echo "Pipeline health check passed"
EOF

chmod +x health_check.sh
```

### Backup and Recovery

#### Data Backup
```bash
# Backup configuration
tar -czf backup/config_$(date +%Y%m%d).tar.gz config/

# Backup recent outputs
tar -czf backup/output_$(date +%Y%m%d).tar.gz output/

# Backup logs
tar -czf backup/logs_$(date +%Y%m%d).tar.gz logs/
```

#### Recovery Procedures
```bash
# Restore from backup
tar -xzf backup/config_20241201.tar.gz

# Rerun failed pipeline
python medication_normalization_pipeline.py \
  --config config/pipeline_config.yaml \
  --output-dir output/recovery_$(date +%Y%m%d_%H%M%S)

# Validate recovery
python curtis_integration/quality_monitor.py \
  --output-dir output/recovery_*
```

### Security Considerations

#### Secure Credential Management
```bash
# Use environment variables for sensitive data
export DB_PASSWORD="$(cat /secure/path/db_password.txt)"

# Set proper file permissions
chmod 600 .env
chmod 700 logs/
chmod 755 curtis_integration/

# Rotate credentials regularly
# Update .env file with new credentials
# Test connections after credential changes
```

#### Network Security
```bash
# Use SSL connections where possible
# In connection strings:
# ********************************/db?sslmode=require

# Restrict database access by IP
# Configure firewall rules
# Use VPN or private networks where possible
```

This comprehensive guide should help you successfully deploy and run the medication normalization pipeline in any environment, from development testing to production deployment.