# Medication Normalization Pipeline

A comprehensive medication data normalization system for transplant patient data, converting multiple source systems to OMOP CDM format with RxNorm standardization.

## Overview

This pipeline extracts medication data from four transplant patient sources, normalizes medications using RxNorm, and transforms the data into OMOP CDM format for data warehouse integration.

### Supported Data Sources

1. **MedActionPlan Pro Web** - Web-based medication adherence platform
2. **MedActionPlan Mobile (MMS)** - Mobile medication adherence app
3. **PioneerRx** - Pharmacy management system
4. **AlloCare** - FHIR-compliant patient monitoring system

## Quick Start

### Installation

```bash
# Clone or copy the project
cd medication_normalization

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials
```

### Basic Usage

```bash
# Run full pipeline
python scripts/run_full_pipeline.py

# Run specific source only
python scripts/run_extraction_only.py --sources map_pro,pioneer_rx

# Custom configuration
python scripts/run_full_pipeline.py --config config/production.yaml
```

### Programmatic Usage

```python
from src.pipeline.medication_pipeline import MedicationPipeline

# Initialize pipeline
pipeline = MedicationPipeline(config_path="config/source_configs.yaml")

# Run full normalization
results = pipeline.run_full_pipeline()

# Run specific steps
extracted_data = pipeline.run_extraction()
normalized_data = pipeline.run_normalization(extracted_data)
omop_data = pipeline.run_transformation(normalized_data)
```

## Architecture

### Data Flow

```
Sources → Extraction → Normalization → OMOP Transform → Output
  ↓           ↓            ↓              ↓            ↓
4 DBs    Unified     RxNorm      DRUG_EXPOSURE   CSV/Parquet
        Schema      Mapping        Tables        /Database
```

### Key Components

- **Extractors**: Source-specific data extraction modules
- **Normalizers**: RxNorm mapping and dose standardization
- **Transformers**: OMOP CDM table generation
- **Validators**: Data quality assurance

## Configuration

### Environment Variables

Create a `.env` file with your database credentials:

```bash
# MAP Pro Database
MAP_PRO_HOST=your-host
MAP_PRO_PORT=1433
MAP_PRO_USER=your-username
MAP_PRO_PASSWORD=your-password

# PioneerRx Database
PIONEER_RX_HOST=your-host
PIONEER_RX_PORT=1433
PIONEER_RX_USER=your-username
PIONEER_RX_PASSWORD=your-password

# AlloCare FHIR Server
ALLOCARE_FHIR_URL=https://your-fhir-server.com/fhir
ALLOCARE_TOKEN=your-bearer-token
```

### Configuration Files

- `config/source_configs.yaml` - Database connections and extraction settings
- `config/rxnorm_config.yaml` - RxNorm API and mapping configuration
- `config/omop_config.yaml` - OMOP CDM transformation settings

## Output

### OMOP CDM Tables

The pipeline generates the following OMOP CDM tables:

- **DRUG_EXPOSURE** - Primary medication records
- **DRUG_ERA** - Continuous medication periods
- **DOSE_ERA** - Dose-specific periods
- **Updated vocabulary tables** - Custom concept mappings

### Output Formats

- **CSV** - For easy integration with ETL tools
- **Parquet** - For big data workflows
- **Direct Database** - Insert directly into OMOP CDM database

### Quality Reports

- **Extraction Summary** - Records extracted per source
- **Mapping Coverage** - RxNorm mapping success rates
- **Validation Results** - Data quality metrics
- **Unmapped Medications** - List for manual review

## Data Quality

### Validation Features

- **Source data completeness** checks
- **RxNorm mapping coverage** analysis
- **OMOP CDM compliance** validation
- **Cross-source consistency** verification
- **Transplant medication coverage** monitoring

### Quality Metrics

- Mapping success rates by source
- Data completeness percentages
- Duplicate detection and handling
- Date range validation
- Dose value reasonableness checks

## Development

### Project Structure

```
medication_normalization/
├── src/                    # Source code
│   ├── extractors/         # Data extraction modules
│   ├── normalizers/        # RxNorm mapping logic
│   ├── transformers/       # OMOP transformation
│   ├── validators/         # Quality assurance
│   └── utils/              # Shared utilities
├── config/                 # Configuration files
├── tests/                  # Unit and integration tests
├── scripts/                # Execution scripts
└── output/                 # Generated output files
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src tests/

# Run specific test module
pytest tests/test_extractors/
```

### Code Quality

```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## Integration with Data Warehouse

### For Curtis's Workflow

The pipeline is designed for easy integration into data warehouse workflows:

1. **Standalone Execution**: Use provided scripts for batch processing
2. **API Integration**: Import pipeline modules programmatically
3. **Configurable Outputs**: Specify custom output directories and formats
4. **Error Handling**: Comprehensive logging and error reporting

### Example Integration

```python
# In Curtis's ETL workflow
from medication_normalization import MedicationPipeline

def process_medication_data():
    pipeline = MedicationPipeline(
        config_path="config/production.yaml",
        output_dir="/warehouse/staging/medications"
    )

    results = pipeline.run_full_pipeline()

    # Check for errors
    if results['status'] == 'success':
        load_to_warehouse(results['output_files'])
    else:
        handle_pipeline_errors(results['errors'])
```

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   - Check credentials in `.env` file
   - Verify network connectivity
   - Ensure database drivers are installed

2. **RxNorm API Issues**
   - Check internet connectivity
   - Verify API rate limits
   - Review API response logs

3. **Memory Issues with Large Datasets**
   - Adjust batch sizes in configuration
   - Enable parallel processing
   - Consider data filtering options

### Logging

Logs are written to:
- `logs/medication_normalization.log` - Main application log
- `logs/errors.log` - Error-specific log
- `logs/performance.log` - Performance metrics

Set log level in configuration:
```yaml
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

## Contributing

1. Follow the established code structure
2. Add tests for new functionality
3. Update documentation
4. Run code quality checks before committing

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Contact the development team