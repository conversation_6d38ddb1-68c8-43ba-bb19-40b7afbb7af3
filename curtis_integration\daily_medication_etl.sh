#!/bin/bash

# Daily Medication ETL Workflow Script
# This script runs the complete medication normalization pipeline daily
# for <PERSON>'s data warehouse integration

set -euo pipefail  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_DIR/config/pipeline_config.yaml"
OUTPUT_BASE_DIR="$PROJECT_DIR/output"
LOG_DIR="$PROJECT_DIR/logs"
ARCHIVE_DIR="$PROJECT_DIR/archive"

# Create timestamp for this run
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="$OUTPUT_BASE_DIR/daily_$TIMESTAMP"
LOG_FILE="$LOG_DIR/daily_etl_$TIMESTAMP.log"

# Ensure directories exist
mkdir -p "$OUTPUT_DIR" "$LOG_DIR" "$ARCHIVE_DIR"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    send_notification "FAILED" "$1"
    exit 1
}

# Success notification
success_notification() {
    log "SUCCESS: Daily medication ETL completed successfully"
    send_notification "SUCCESS" "Daily ETL completed. Output: $OUTPUT_DIR"
}

# Send notifications (webhook or email)
send_notification() {
    local status="$1"
    local message="$2"

    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"status\": \"$status\", \"message\": \"$message\", \"timestamp\": \"$(date -Iseconds)\"}" \
            --silent --show-error || log "Warning: Failed to send webhook notification"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    # Check Python environment
    if ! command -v python3 &> /dev/null; then
        error_exit "Python 3 is not installed or not in PATH"
    fi

    # Check configuration file
    if [[ ! -f "$CONFIG_FILE" ]]; then
        error_exit "Configuration file not found: $CONFIG_FILE"
    fi

    # Check database connectivity
    log "Testing database connections..."
    python3 "$SCRIPT_DIR/test_connections.py" || error_exit "Database connection test failed"

    log "Prerequisites check passed"
}

# Run the main pipeline
run_pipeline() {
    log "Starting medication normalization pipeline..."
    log "Output directory: $OUTPUT_DIR"

    # Set environment variables for this run
    export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"

    # Run the main pipeline
    python3 "$PROJECT_DIR/medication_normalization_pipeline.py" \
        --config "$CONFIG_FILE" \
        --output-dir "$OUTPUT_DIR" \
        --verbose 2>&1 | tee -a "$LOG_FILE"

    # Check if pipeline succeeded
    if [[ ${PIPESTATUS[0]} -ne 0 ]]; then
        error_exit "Medication normalization pipeline failed"
    fi

    log "Pipeline execution completed successfully"
}

# Validate output
validate_output() {
    log "Validating pipeline output..."

    # Check if output files exist
    local required_files=("DRUG_EXPOSURE.parquet" "execution_summary.json")

    for file in "${required_files[@]}"; do
        if [[ ! -f "$OUTPUT_DIR/$file" ]]; then
            error_exit "Required output file missing: $file"
        fi
    done

    # Run detailed validation
    python3 "$PROJECT_DIR/medication_normalization_pipeline.py" \
        --config "$CONFIG_FILE" \
        --output-dir "$OUTPUT_DIR" \
        --validate-only 2>&1 | tee -a "$LOG_FILE"

    if [[ ${PIPESTATUS[0]} -ne 0 ]]; then
        error_exit "Output validation failed"
    fi

    log "Output validation passed"
}

# Load to data warehouse
load_to_warehouse() {
    log "Loading OMOP tables to data warehouse..."

    python3 "$SCRIPT_DIR/data_warehouse_loader.py" \
        --input-dir "$OUTPUT_DIR" \
        --load-strategy incremental \
        --validate-before-load 2>&1 | tee -a "$LOG_FILE"

    if [[ ${PIPESTATUS[0]} -ne 0 ]]; then
        error_exit "Data warehouse loading failed"
    fi

    log "Data warehouse loading completed"
}

# Generate quality report
generate_quality_report() {
    log "Generating quality report..."

    python3 "$SCRIPT_DIR/quality_monitor.py" \
        --output-dir "$OUTPUT_DIR" \
        --generate-report 2>&1 | tee -a "$LOG_FILE"

    if [[ ${PIPESTATUS[0]} -ne 0 ]]; then
        log "Warning: Quality report generation failed"
    else
        log "Quality report generated successfully"
    fi
}

# Archive previous runs
archive_old_runs() {
    log "Archiving old runs..."

    # Keep last 7 days of daily runs, archive older ones
    find "$OUTPUT_BASE_DIR" -name "daily_*" -type d -mtime +7 -exec mv {} "$ARCHIVE_DIR/" \; 2>/dev/null || true

    # Compress archives older than 30 days
    find "$ARCHIVE_DIR" -name "daily_*" -type d -mtime +30 -exec tar -czf {}.tar.gz {} \; -exec rm -rf {} \; 2>/dev/null || true

    log "Archiving completed"
}

# Cleanup on exit
cleanup() {
    log "Cleaning up temporary files..."
    # Add any cleanup tasks here
}

# Main execution
main() {
    log "=== Starting Daily Medication ETL Workflow ==="
    log "Timestamp: $TIMESTAMP"
    log "Output directory: $OUTPUT_DIR"

    # Set trap for cleanup
    trap cleanup EXIT

    # Execute workflow steps
    check_prerequisites
    run_pipeline
    validate_output
    load_to_warehouse
    generate_quality_report
    archive_old_runs

    success_notification
    log "=== Daily Medication ETL Workflow Completed Successfully ==="
}

# Handle command line arguments
case "${1:-run}" in
    "run")
        main
        ;;
    "validate-only")
        log "Running validation only..."
        check_prerequisites
        if [[ -z "${2:-}" ]]; then
            error_exit "Output directory required for validation-only mode"
        fi
        OUTPUT_DIR="$2"
        validate_output
        log "Validation completed"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [run|validate-only <output_dir>|help]"
        echo ""
        echo "Commands:"
        echo "  run              Run complete daily ETL workflow (default)"
        echo "  validate-only    Validate existing output directory"
        echo "  help            Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  WEBHOOK_URL     Optional webhook for notifications"
        echo ""
        echo "Examples:"
        echo "  $0                                    # Run daily ETL"
        echo "  $0 validate-only output/daily_20241201_120000  # Validate specific run"
        exit 0
        ;;
    *)
        error_exit "Unknown command: $1. Use '$0 help' for usage information."
        ;;
esac