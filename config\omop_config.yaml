# OMOP CDM Configuration

# OMOP CDM version and vocabulary settings
cdm:
  version: "6.0"
  vocabulary_version: "v5.0"

# Standard concept domains and vocabularies
vocabularies:
  drug_concepts:
    primary: "RxNorm"
    secondary: ["NDC", "RxNorm Extension"]

  units:
    primary: "UCUM"
    fallback: "Unit"

  routes:
    primary: "SNOMED"
    fallback: "Route"

  domains:
    drug: "Drug"
    unit: "Unit"
    route: "Route"

# OMOP table generation settings
tables:
  drug_exposure:
    # Required fields mapping
    required_fields:
      drug_exposure_id: "auto_increment"
      person_id: "from_source_patient_id"
      drug_concept_id: "from_rxnorm_mapping"
      drug_exposure_start_date: "from_source_start_date"
      drug_type_concept_id: 38000177  # "Prescription written"

    # Optional fields with defaults
    optional_fields:
      drug_exposure_end_date: "from_source_end_date_or_calculate"
      dose_value: "from_source_dose"
      dose_unit_concept_id: "from_unit_mapping"
      route_concept_id: "from_route_mapping"
      lot_number: null
      provider_id: "from_source_provider"
      visit_occurrence_id: "from_source_visit"
      drug_source_value: "from_source_medication_text"
      drug_source_concept_id: "from_source_code"
      route_source_value: "from_source_route"
      dose_unit_source_value: "from_source_dose_unit"

  drug_era:
    gap_days: 30  # Days gap to consider as same era
    minimum_era_length: 1  # Minimum days for an era

  dose_era:
    gap_days: 30
    dose_tolerance: 0.1  # 10% tolerance for considering same dose

# Data quality rules
quality:
  required_fields:
    - "source_system"
    - "patient_id"
    - "medication_text"
    - "start_date"

  validation_rules:
    # Date validation
    valid_date_range:
      min_year: 2010
      max_year: 2030

    # Dose validation
    dose_limits:
      min_dose: 0.001
      max_dose: 10000

    # Text validation
    medication_text:
      min_length: 2
      max_length: 500
      required_pattern: ".*[a-zA-Z].*"  # Must contain at least one letter

# Standard concept mappings for common values
standard_concepts:
  drug_types:
    prescription: 38000177
    dispensed: 38000175
    administered: 38000180
    self_reported: 45754907

  routes:
    oral: 4128794
    intravenous: 4171047
    intramuscular: 4302612
    subcutaneous: 4142048
    topical: 4262099
    inhalation: 4186831

  units:
    mg: 8576
    g: 8504
    mcg: 9655
    ml: 8587
    l: 8519
    percent: 8554
    units: 8510

# Output formatting
output:
  file_format: "parquet"  # "csv", "parquet"
  compression: "snappy"
  partition_by: ["source_system"]
  include_metadata: true

  # Column naming convention
  naming_convention: "snake_case"  # "snake_case", "camelCase"

  # Export options
  export_vocabulary_tables: true
  export_era_tables: true
  export_summary_stats: true