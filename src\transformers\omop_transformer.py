"""
Main OMOP CDM transformer for medication data.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path

from .drug_exposure_builder import DrugExposureBuilder
from .drug_era_builder import DrugEraBuilder
from .dose_era_builder import DoseEraBuilder
from .vocabulary_builder import VocabularyBuilder
from ..utils.logger import LoggerMixin
from ..utils.exceptions import TransformationError
from ..utils.constants import OMOP_STANDARD_CONCEPTS


class OMOPTransformer(LoggerMixin):
    """
    Main OMOP CDM transformer for medication normalization data.

    Transforms normalized medication data into OMOP CDM format,
    generating DRUG_EXPOSURE, DRUG_ERA, and DOSE_ERA tables.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize OMOP transformer.

        Args:
            config: OMOP transformation configuration
        """
        self.config = config
        self.omop_config = config.get('omop', {})

        # Initialize builders
        self.drug_exposure_builder = DrugExposureBuilder(config)
        self.drug_era_builder = DrugEraBuilder(config)
        self.dose_era_builder = DoseEraBuilder(config)
        self.vocabulary_builder = VocabularyBuilder(config)

        # Transformation statistics
        self.transformation_stats = {
            'start_time': None,
            'end_time': None,
            'input_records': 0,
            'drug_exposure_records': 0,
            'drug_era_records': 0,
            'dose_era_records': 0,
            'vocabulary_records': 0,
            'error_count': 0,
            'warnings': []
        }

    def transform_to_omop(
        self,
        normalized_medications_df: pd.DataFrame,
        generate_eras: bool = True,
        include_vocabulary: bool = True,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Transform normalized medications to OMOP CDM format.

        Args:
            normalized_medications_df: Normalized medication data
            generate_eras: Whether to generate era tables
            include_vocabulary: Whether to include vocabulary tables
            output_dir: Directory to save OMOP tables

        Returns:
            Dictionary with OMOP tables and statistics
        """
        if normalized_medications_df.empty:
            return {
                'status': 'skipped',
                'message': 'Empty input data - no transformation performed',
                'tables': {}
            }

        self.transformation_stats['start_time'] = datetime.now()
        self.transformation_stats['input_records'] = len(normalized_medications_df)

        self.logger.info(f"Starting OMOP transformation of {len(normalized_medications_df)} medication records")

        try:
            omop_tables = {}

            # Step 1: Generate DRUG_EXPOSURE table
            self.logger.info("Generating DRUG_EXPOSURE table...")
            drug_exposure_df = self.drug_exposure_builder.build_drug_exposure_table(
                normalized_medications_df
            )
            omop_tables['DRUG_EXPOSURE'] = drug_exposure_df
            self.transformation_stats['drug_exposure_records'] = len(drug_exposure_df)

            # Step 2: Generate era tables if requested
            if generate_eras and not drug_exposure_df.empty:
                self.logger.info("Generating DRUG_ERA table...")
                drug_era_df = self.drug_era_builder.build_drug_era_table(drug_exposure_df)
                omop_tables['DRUG_ERA'] = drug_era_df
                self.transformation_stats['drug_era_records'] = len(drug_era_df)

                self.logger.info("Generating DOSE_ERA table...")
                dose_era_df = self.dose_era_builder.build_dose_era_table(drug_exposure_df)
                omop_tables['DOSE_ERA'] = dose_era_df
                self.transformation_stats['dose_era_records'] = len(dose_era_df)

            # Step 3: Generate vocabulary tables if requested
            if include_vocabulary:
                self.logger.info("Generating vocabulary tables...")
                vocabulary_tables = self.vocabulary_builder.build_vocabulary_tables(
                    normalized_medications_df, drug_exposure_df
                )
                omop_tables.update(vocabulary_tables)

                total_vocab_records = sum(len(df) for df in vocabulary_tables.values())
                self.transformation_stats['vocabulary_records'] = total_vocab_records

            # Step 4: Save outputs if directory specified
            if output_dir:
                saved_files = self._save_omop_tables(omop_tables, output_dir)
            else:
                saved_files = {}

            # Update statistics
            self.transformation_stats['end_time'] = datetime.now()

            # Log completion
            self._log_transformation_summary()

            return {
                'status': 'success',
                'tables': omop_tables,
                'statistics': self.transformation_stats.copy(),
                'saved_files': saved_files,
                'summary': self._generate_transformation_summary()
            }

        except Exception as e:
            self.transformation_stats['error_count'] += 1
            self.transformation_stats['end_time'] = datetime.now()

            self.logger.error(f"OMOP transformation failed: {str(e)}")
            raise TransformationError(
                f"OMOP transformation failed: {str(e)}",
                omop_table="transformation_pipeline"
            )

    def transform_drug_exposure_only(
        self,
        normalized_medications_df: pd.DataFrame,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Transform to DRUG_EXPOSURE table only (faster option).

        Args:
            normalized_medications_df: Normalized medication data
            output_dir: Directory to save output

        Returns:
            DRUG_EXPOSURE table and statistics
        """
        self.logger.info("Generating DRUG_EXPOSURE table only...")

        try:
            drug_exposure_df = self.drug_exposure_builder.build_drug_exposure_table(
                normalized_medications_df
            )

            result = {
                'status': 'success',
                'DRUG_EXPOSURE': drug_exposure_df,
                'record_count': len(drug_exposure_df)
            }

            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)

                file_path = output_path / "DRUG_EXPOSURE.parquet"
                drug_exposure_df.to_parquet(file_path, index=False)
                result['saved_file'] = str(file_path)

            return result

        except Exception as e:
            self.logger.error(f"DRUG_EXPOSURE transformation failed: {str(e)}")
            raise TransformationError(
                f"DRUG_EXPOSURE transformation failed: {str(e)}",
                omop_table="DRUG_EXPOSURE"
            )

    def _save_omop_tables(
        self,
        omop_tables: Dict[str, pd.DataFrame],
        output_dir: str
    ) -> Dict[str, str]:
        """
        Save OMOP tables to files.

        Args:
            omop_tables: Dictionary of OMOP table DataFrames
            output_dir: Output directory

        Returns:
            Dictionary mapping table names to file paths
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        saved_files = {}
        output_format = self.omop_config.get('output', {}).get('file_format', 'parquet')

        for table_name, df in omop_tables.items():
            if df.empty:
                self.logger.warning(f"Skipping empty table: {table_name}")
                continue

            try:
                if output_format == 'parquet':
                    file_path = output_path / f"{table_name}.parquet"
                    df.to_parquet(file_path, index=False, compression='snappy')
                elif output_format == 'csv':
                    file_path = output_path / f"{table_name}.csv"
                    df.to_csv(file_path, index=False)
                else:
                    raise ValueError(f"Unsupported output format: {output_format}")

                saved_files[table_name] = str(file_path)
                self.logger.info(f"Saved {table_name}: {len(df)} records to {file_path}")

            except Exception as e:
                self.logger.error(f"Failed to save {table_name}: {str(e)}")
                self.transformation_stats['error_count'] += 1

        return saved_files

    def _log_transformation_summary(self) -> None:
        """Log transformation summary."""
        stats = self.transformation_stats

        if not stats['start_time'] or not stats['end_time']:
            return

        execution_time = (stats['end_time'] - stats['start_time']).total_seconds()

        self.logger.info(
            f"OMOP Transformation Summary:\n"
            f"  Input records: {stats['input_records']:,}\n"
            f"  DRUG_EXPOSURE records: {stats['drug_exposure_records']:,}\n"
            f"  DRUG_ERA records: {stats['drug_era_records']:,}\n"
            f"  DOSE_ERA records: {stats['dose_era_records']:,}\n"
            f"  Vocabulary records: {stats['vocabulary_records']:,}\n"
            f"  Execution time: {execution_time:.2f} seconds\n"
            f"  Errors: {stats['error_count']}"
        )

    def _generate_transformation_summary(self) -> Dict[str, Any]:
        """Generate transformation summary for reporting."""
        stats = self.transformation_stats

        summary = {
            'transformation_timestamp': stats['end_time'].isoformat() if stats['end_time'] else None,
            'input_records': stats['input_records'],
            'output_tables': {
                'DRUG_EXPOSURE': stats['drug_exposure_records'],
                'DRUG_ERA': stats['drug_era_records'],
                'DOSE_ERA': stats['dose_era_records'],
                'vocabulary_tables': stats['vocabulary_records']
            },
            'total_output_records': (
                stats['drug_exposure_records'] +
                stats['drug_era_records'] +
                stats['dose_era_records'] +
                stats['vocabulary_records']
            ),
            'transformation_rate': (
                stats['drug_exposure_records'] / stats['input_records']
                if stats['input_records'] > 0 else 0
            ),
            'execution_time_seconds': (
                (stats['end_time'] - stats['start_time']).total_seconds()
                if stats['start_time'] and stats['end_time'] else 0
            ),
            'error_count': stats['error_count'],
            'warnings': stats['warnings']
        }

        return summary

    def generate_omop_report(
        self,
        omop_tables: Dict[str, pd.DataFrame],
        output_path: Optional[str] = None
    ) -> str:
        """
        Generate comprehensive OMOP transformation report.

        Args:
            omop_tables: Generated OMOP tables
            output_path: Path to save report

        Returns:
            Report text
        """
        summary = self._generate_transformation_summary()

        report_lines = [
            "# OMOP CDM Transformation Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## Transformation Summary",
            f"- Input Records: {summary['input_records']:,}",
            f"- Execution Time: {summary['execution_time_seconds']:.2f} seconds",
            f"- Transformation Rate: {summary['transformation_rate']:.1%}",
            f"- Errors: {summary['error_count']}",
            "",
            "## Generated Tables"
        ]

        # Table details
        for table_name, df in omop_tables.items():
            if df.empty:
                continue

            report_lines.extend([
                f"### {table_name}",
                f"- Records: {len(df):,}",
                f"- Columns: {len(df.columns)}",
                f"- Memory Usage: {df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB"
            ])

            # Add column summary for key tables
            if table_name == 'DRUG_EXPOSURE':
                report_lines.extend(self._generate_drug_exposure_stats(df))
            elif table_name == 'DRUG_ERA':
                report_lines.extend(self._generate_drug_era_stats(df))

            report_lines.append("")

        # Data quality summary
        report_lines.extend([
            "## Data Quality Summary",
            self._generate_data_quality_summary(omop_tables),
            ""
        ])

        # Configuration used
        report_lines.extend([
            "## Configuration",
            f"- CDM Version: {self.omop_config.get('cdm', {}).get('version', '6.0')}",
            f"- Output Format: {self.omop_config.get('output', {}).get('file_format', 'parquet')}",
            f"- Era Generation: Enabled",
            f"- Vocabulary Tables: Included"
        ])

        report_text = "\n".join(report_lines)

        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            self.logger.info(f"OMOP transformation report saved to {output_path}")

        return report_text

    def _generate_drug_exposure_stats(self, drug_exposure_df: pd.DataFrame) -> List[str]:
        """Generate DRUG_EXPOSURE specific statistics."""
        stats = []

        # Unique patients and drugs
        unique_patients = drug_exposure_df['person_id'].nunique()
        unique_drugs = drug_exposure_df['drug_concept_id'].nunique()

        stats.extend([
            f"- Unique Patients: {unique_patients:,}",
            f"- Unique Drugs: {unique_drugs:,}"
        ])

        # Dose information coverage
        has_dose = drug_exposure_df['dose_value'].notna().sum()
        dose_coverage = has_dose / len(drug_exposure_df) * 100

        stats.append(f"- Dose Information: {has_dose:,} records ({dose_coverage:.1f}%)")

        # Date range
        if 'drug_exposure_start_date' in drug_exposure_df.columns:
            start_dates = pd.to_datetime(drug_exposure_df['drug_exposure_start_date'], errors='coerce')
            date_range = f"{start_dates.min():%Y-%m-%d} to {start_dates.max():%Y-%m-%d}"
            stats.append(f"- Date Range: {date_range}")

        return stats

    def _generate_drug_era_stats(self, drug_era_df: pd.DataFrame) -> List[str]:
        """Generate DRUG_ERA specific statistics."""
        stats = []

        # Era duration statistics
        if 'drug_era_start_date' in drug_era_df.columns and 'drug_era_end_date' in drug_era_df.columns:
            start_dates = pd.to_datetime(drug_era_df['drug_era_start_date'], errors='coerce')
            end_dates = pd.to_datetime(drug_era_df['drug_era_end_date'], errors='coerce')

            durations = (end_dates - start_dates).dt.days
            avg_duration = durations.mean()
            median_duration = durations.median()

            stats.extend([
                f"- Average Era Duration: {avg_duration:.1f} days",
                f"- Median Era Duration: {median_duration:.1f} days"
            ])

        return stats

    def _generate_data_quality_summary(self, omop_tables: Dict[str, pd.DataFrame]) -> str:
        """Generate data quality summary across all tables."""
        quality_lines = []

        for table_name, df in omop_tables.items():
            if df.empty:
                continue

            # Check for required fields completeness
            required_fields = self._get_required_fields(table_name)
            completeness_issues = []

            for field in required_fields:
                if field in df.columns:
                    null_count = df[field].isnull().sum()
                    if null_count > 0:
                        completeness_issues.append(f"{field}: {null_count} nulls")

            if completeness_issues:
                quality_lines.append(f"- **{table_name}**: {', '.join(completeness_issues)}")
            else:
                quality_lines.append(f"- **{table_name}**: All required fields complete")

        return "\n".join(quality_lines) if quality_lines else "- All tables pass basic quality checks"

    def _get_required_fields(self, table_name: str) -> List[str]:
        """Get required fields for OMOP table."""
        required_fields_map = {
            'DRUG_EXPOSURE': [
                'drug_exposure_id', 'person_id', 'drug_concept_id',
                'drug_exposure_start_date', 'drug_type_concept_id'
            ],
            'DRUG_ERA': [
                'drug_era_id', 'person_id', 'drug_concept_id',
                'drug_era_start_date', 'drug_era_end_date'
            ],
            'DOSE_ERA': [
                'dose_era_id', 'person_id', 'drug_concept_id',
                'unit_concept_id', 'dose_value',
                'dose_era_start_date', 'dose_era_end_date'
            ]
        }

        return required_fields_map.get(table_name, [])

    def get_transformation_statistics(self) -> Dict[str, Any]:
        """Get comprehensive transformation statistics."""
        return {
            'transformation_stats': self.transformation_stats.copy(),
            'builder_stats': {
                'drug_exposure': self.drug_exposure_builder.get_builder_statistics(),
                'drug_era': self.drug_era_builder.get_builder_statistics(),
                'dose_era': self.dose_era_builder.get_builder_statistics(),
                'vocabulary': self.vocabulary_builder.get_builder_statistics()
            },
            'configuration': {
                'cdm_version': self.omop_config.get('cdm', {}).get('version', '6.0'),
                'output_format': self.omop_config.get('output', {}).get('file_format', 'parquet'),
                'include_era_tables': True,
                'include_vocabulary': True
            }
        }