# Medication Normalization Project - Discussion Notes

## Project Overview
- **Goal**: Populate OMOP CDM drug and dose tables using medication data from four transplant patient sources
- **Domain**: Healthcare/Pharmaceutical data normalization - Organ Transplant
- **Data Sources**: Four schemas with medication information for transplant patients
- **Target**: OMOP CDM drug/dose tables with RxNorm standardization
- **Use Case**: Build transplant patient-centric data warehouse based on OMOP CDM
- **Started**: September 22, 2025

## Discussion Log

### Initial Setup
- Project directory established: `/mnt/c/Users/<USER>/OneDrive - CareDx, Inc/Documents/Work/Scripts/Medication_Normalization`
- Documentation approach decided: Use PROJECT_NOTES.md for detailed discussion, then summarize key points in CLAUDE.md

### Clinical NLP Context Discussion
- Confirmed familiarity with Clinical NLP field
- Key relevant areas: medical entity recognition, clinical concept normalization, medication mapping
- Common challenges: terminology variability, abbreviations, standardization to vocabularies (RxNorm, NDC, UMLS)
- Likely involves mapping medication mentions to standardized drug databases

### Medication Normalization/Standardization Discussion
- Core process: mapping diverse medication representations to standardized forms
- Key challenges:
  - Brand vs generic name variations
  - Dosage form and strength differences
  - Route of administration variations
  - Combination drugs
  - Data entry errors and inconsistencies
- Standard vocabularies: RxNorm, NDC, ATC codes
- Common approaches: rule-based matching, ML models, API integration, hybrid methods
- Use cases: clinical decision support, drug interaction checking, analytics, research

### RxNorm Detailed Overview

**Purpose & Integration:**
- Normalized naming system for clinical drugs (NLM)
- Links to major drug vocabularies: First Databank, Micromedex, Multum, Gold Standard, VANDF, SNOMED CT, NDF-RT
- Includes USP Compendial Nomenclature (Active Pharmaceutical Ingredients)
- Mediates between systems using different vocabularies

**Core Structure:**
- **RxCUI**: Unique identifier for each concept
- **Hierarchical Term Types (TTY)**:

**1. Ingredients:**
- IN (Ingredient): base substance (e.g., Acetaminophen)
- PIN (Precise Ingredient): specific form/salt
- MIN (Multiple Ingredients): combinations

**2. Clinical Drug Forms:**
- SCDC (Semantic Clinical Drug Component): Ingredient + Strength + Dose Form (single component)
- SCD (Semantic Clinical Drug): full clinical drug (can be multi-component)
- SCDG (Semantic Clinical Drug Group): groups SCDs by ingredient + form (different strengths)
- GPCK (Generic Pack): package of generic drugs

**3. Branded Drugs:**
- BN (Brand Name): trade name (e.g., Tylenol)
- SBD (Semantic Branded Drug): Brand + Strength + Dose Form
- SBDG (Semantic Branded Drug Group): groups SBDs by brand + form
- BPCK (Branded Pack): package of branded products

**4. Dose Form Group:**
- DFG: groups similar dose forms (e.g., "Oral Tablet Group")

**Key Relationships:**
- has_ingredient: links products to active ingredients
- tradename_of: links branded to generic equivalents
- consists_of: links packs to contents
- form_of: links dose forms or ingredient variants
- isa: hierarchical relationships

**Example Hierarchy (Tylenol 500mg):**
```
IN: Acetaminophen (RxCUI=161)
 ↓ has_ingredient
SCD: Acetaminophen 500 mg Oral Tablet (RxCUI=198440)
 ↓ tradename_of
BN: Tylenol (RxCUI=203130)
 ↓ tradename_of
SBD: Tylenol 500 mg Oral Tablet (RxCUI=209459)
 ↓ consists_of
BPCK: Tylenol 500 mg Oral Tablet [24 count] (RxCUI=617314)
```

**Use Cases for Normalization:**
- Map free-text medication names to RxCUIs
- Normalize dosages and strengths
- Resolve brand/generic equivalencies
- Extract active ingredients
- Standardize routes of administration

**Key Challenges:**
- Ambiguous abbreviations and misspellings
- Non-standard dosage formats
- Compound medications

### Project Scope Discussion
- **Data Sources**: Four schemas containing medication information for organ transplant patients
- **Approach**: Collect medication columns from different sources → normalize using RxNorm
- **Domain-Specific Considerations**:
  - Immunosuppressive medications (tacrolimus, cyclosporine, mycophenolate)
  - Anti-rejection drugs
  - Complex medication regimens
  - Drug interactions critical in transplant care

## Data Sources Analysis

### Source 1: MedActionPlan Pro Web (MAP Pro Web)
- **Purpose**: Web-based patient medication adherence and education platform
- **Users**: Transplant pharmacists, coordinators, patients
- **Scale**: 148,592 web patients across transplant centers
- **Database**: map_pro_sql (238 tables on TDP)
- **Data Integration**: FHIR-based EMR integration

### Source 2: MedActionPlan Mobile (MAP MMS Mobile)
- **Purpose**: Mobile app for medication adherence and education
- **Scale**: 49,662 mobile patients
- **Database**: Same map_pro_sql database but different patient tables
- **Key Difference**: Mobile vs web platform, different user workflows

**Key Medication Tables:**
- **dbo__emr_med**: Core medication data from EMRs
  - Medication fields: ccd_name, ccd_ndc, ccd_dose_value, ccd_dose_unit, ccd_route
  - Frequency: map_intraday_freq, map_interday_freq
  - Links to: map__map_meds (via map_med_id)

- **map__map_meds**: Standardized medication reference
  - Fields: brand, generic, rxcui, scd, display_strength, rxnorm_form_id
  - Additional: drug_class, sideeffect, map_form
  - **Note**: Already contains RxCUI mappings!

- **dbo__medication**: FHIR staging table from EHRs
- **dbo__event**: Patient medication adherence tracking

**Data Quality Notes:**
- FHIR-based import from multiple EMR systems
- Date standardization to UTC (ccd_start_date vs ccd_start_date_orig)
- Raw FHIR files stored in Azure (dbo__import_ccd)

### Source 3: PioneerRx (Transplant Pharmacy System)
- **Purpose**: Pharmacy management system for transplant pharmacy operations
- **Source**: The Transplant Pharmacy (may include Alfonso pharmacy)
- **Scale**: 5,833 patients, 153,957 prescriptions, 657,705 medication transactions
- **Focus**: Actual prescription dispensing and pharmacy operations

**Key Medication Tables:**
- **prescription__rx**: Core prescription data
  - Prescription tracking: RxID, RxNumber, RxStatusTypeID
  - Patient linking: PatientID
  - Item reference: PrescribedItemID → item__item.ItemID
  - Prescriber info: PrescriberID, DateWritten
  - Supply tracking: Quantity, NumberOfRefillsAllowed, NumberOfRefillsFilled
  - Timeline: ExpirationDate, DaysSupplyEndsOn, AvailableForFillDate

- **item__item**: Medication/product reference table
  - Links to: ItemAdministrationID, DosageFormID
  - Core medication identifier (ItemID)

- **prescription__rxtransaction**: Detailed dispensing records
  - Transaction tracking: RxTransactionID, RefillNumber
  - Links: RxID → prescription__rx, DispensedItemID → item__item
  - Dispensing details: DispensedQuantity, DaysSupply, DateFilled, CompletedDate
  - Financial: costs, pricing, patient payments
  - Status tracking: RxTransactionStatusTypeID

**Data Characteristics:**
- Pharmacy-centric view (dispensing vs prescribing)
- Detailed transaction-level data with refill tracking
- Financial and operational data (costs, payment methods)
- Status tracking throughout dispensing workflow

### Source 4: AlloCare (FHIR v6.0.0-ballot3)
- **Purpose**: Patient monitoring and care management system
- **Scale**: 23,286 patients
- **Data Sources**: Labs from providers, vitals from devices
- **Standard**: FHIR v6.0.0-ballot3 compliant

**Key Medication Tables:**
- **MedicationStatement**: Patient-reported or clinician-documented medication usage
  - FHIR-compliant structure
  - Key fields: id, coding
  - Represents medication history and current usage

- **MedicationAdministration**: Records of medication actually given to patients
  - FHIR-compliant structure
  - Key fields: id, coding
  - Represents actual administration events

- **Observation**: Multi-purpose clinical data including medication compliance
  - Medication compliance data: code.coding.display = 'History of Medication use Narrative'
  - Compliance metrics: valueDenominator (total prescribed), valueNumerator (total taken)
  - Key fields: id, coding

**Data Characteristics:**
- **FHIR-native structure** - standardized healthcare data exchange format
- **Compliance tracking** - medication adherence monitoring
- **Multi-modal data** - medications + labs + vitals integration
- **Patient monitoring focus** - care management vs pharmacy operations

## OMOP CDM Integration Strategy

### Target OMOP Tables
**Primary Clinical Tables:**
- **DRUG_EXPOSURE**: Core medication records (exposures/administrations)
  - drug_concept_id (RxNorm standard concepts)
  - dose_value, dose_unit_concept_id, route_concept_id
  - drug_exposure_start_date, drug_exposure_end_date
  - drug_source_value, drug_source_concept_id

- **DRUG_ERA**: Aggregated continuous medication periods
- **DOSE_ERA**: Aggregated dose periods for specific dosing analysis

**Supporting Vocabulary:**
- **CONCEPT**: Standard concepts (RxNorm for drugs)
- **CONCEPT_RELATIONSHIP**: Source-to-standard mappings
- **CONCEPT_ANCESTOR**: Drug hierarchies (ingredient → clinical drug)

### Source-to-OMOP Mapping Strategy
- **MAP Pro**: Use existing RxCUI → drug_concept_id (bootstrap)
- **PioneerRx**: Map ItemID → RxNorm → drug_concept_id
- **AlloCare**: Extract FHIR coding → RxNorm → drug_concept_id
- **All Sources**: Preserve source values for traceability

## Development Strategy

### Collaboration Context
- **Curtis**: Building overall data warehouse workflow
- **Your Role**: Develop medication normalization scripts for direct application
- **Deliverable**: Reusable scripts that Curtis can integrate into warehouse pipeline

### Recommended Development Approach

**Architecture Design Completed**: See ARCHITECTURE.md for detailed system design

**Key Architecture Decisions**:
1. **Modular Design**: Separate extractors, normalizers, transformers, validators
2. **Unified Intermediate Schema**: Standardized format between extraction and normalization
3. **Configuration-Driven**: Easy integration into Curtis's workflow
4. **Multiple Output Formats**: CSV, Parquet, direct DB inserts
5. **Comprehensive Validation**: Data quality and OMOP compliance checking

**Integration Strategy for Curtis**:
- Standalone scripts for pipeline execution
- Programmatic API for workflow integration
- Configurable input/output paths
- Detailed quality reports and error handling

---

## Next Steps
1. Define project goals and requirements
2. Discuss current challenges with medication data
3. Plan technical approach
4. Define success criteria

---

*This file will be updated throughout our discussion to capture all planning details.*