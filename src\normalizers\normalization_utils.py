"""
Normalization orchestration and utility functions.
"""

import pandas as pd
import yaml
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import os

from .rxnorm_mapper import RxNormMapper
from .medication_matcher import MedicationMatcher
from .dose_normalizer import DoseNormalizer
from ..extractors.map_pro_extractor import MapProExtractor
from ..utils.logger import LoggerMixin
from ..utils.exceptions import NormalizationError, ConfigurationError
from ..utils.constants import SourceSystem


class NormalizationOrchestrator(LoggerMixin):
    """
    Orchestrates the complete medication normalization process.

    Combines RxNorm mapping, reference matching, and dose normalization
    to provide comprehensive medication standardization.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize normalization orchestrator.

        Args:
            config_path: Path to RxNorm configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()

        # Initialize components
        self.rxnorm_mapper = RxNormMapper(self.config)
        self.medication_matcher = MedicationMatcher(self.config)
        self.dose_normalizer = DoseNormalizer(self.config)

        # Reference data from MAP Pro
        self.map_pro_mappings = pd.DataFrame()
        self.reference_loaded = False

        # Normalization statistics
        self.normalization_stats = {
            'start_time': None,
            'end_time': None,
            'total_medications': 0,
            'rxnorm_api_mapped': 0,
            'reference_mapped': 0,
            'dose_normalized': 0,
            'unmapped_count': 0,
            'error_count': 0
        }

    def _load_config(self) -> Dict[str, Any]:
        """Load RxNorm configuration."""
        if not self.config_path:
            # Use default config path
            current_dir = Path(__file__).parent.parent.parent
            self.config_path = current_dir / "config" / "rxnorm_config.yaml"

        if not os.path.exists(self.config_path):
            raise ConfigurationError(
                f"RxNorm configuration file not found: {self.config_path}",
                config_file=str(self.config_path)
            )

        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            self.logger.info(f"Loaded RxNorm configuration from {self.config_path}")
            return config

        except Exception as e:
            raise ConfigurationError(
                f"Failed to load RxNorm configuration: {str(e)}",
                config_file=str(self.config_path)
            )

    def load_map_pro_reference_data(self, source_config_path: Optional[str] = None) -> None:
        """
        Load MAP Pro reference mappings for use in normalization.

        Args:
            source_config_path: Path to source configuration file
        """
        try:
            self.logger.info("Loading MAP Pro reference mappings...")

            # Create MAP Pro extractor to get reference data
            if not source_config_path:
                current_dir = Path(__file__).parent.parent.parent
                source_config_path = current_dir / "config" / "source_configs.yaml"

            # Load source config
            with open(source_config_path, 'r') as f:
                source_config = yaml.safe_load(f)

            map_pro_config = source_config.get('map_pro', {})

            if not map_pro_config.get('enabled', True):
                self.logger.warning("MAP Pro is disabled, skipping reference data loading")
                return

            # Create extractor and get reference mappings
            map_pro_extractor = MapProExtractor(map_pro_config)
            self.map_pro_mappings = map_pro_extractor.get_rxnorm_mappings()

            # Load mappings into medication matcher
            self.medication_matcher.load_reference_mappings(self.map_pro_mappings)

            self.reference_loaded = True
            self.logger.info(f"Loaded {len(self.map_pro_mappings)} MAP Pro reference mappings")

        except Exception as e:
            self.logger.error(f"Failed to load MAP Pro reference data: {str(e)}")
            # Continue without reference data
            self.reference_loaded = False

    def normalize_medications(
        self,
        medications_df: pd.DataFrame,
        text_column: str = 'medication_text',
        use_reference_matching: bool = True,
        use_rxnorm_api: bool = True,
        normalize_doses: bool = True,
        batch_size: int = 100
    ) -> pd.DataFrame:
        """
        Normalize medications using all available strategies.

        Args:
            medications_df: DataFrame with medication data
            text_column: Column containing medication text
            use_reference_matching: Whether to use MAP Pro reference matching
            use_rxnorm_api: Whether to use RxNorm API for unmapped medications
            normalize_doses: Whether to normalize dose information
            batch_size: Batch size for API calls

        Returns:
            DataFrame with normalized medication data
        """
        if medications_df.empty:
            return medications_df

        self.normalization_stats['start_time'] = datetime.now()
        self.normalization_stats['total_medications'] = len(medications_df)

        self.logger.info(f"Starting normalization of {len(medications_df)} medications")

        # Copy input DataFrame
        result_df = medications_df.copy()

        # Step 1: Reference-based matching (if enabled and available)
        if use_reference_matching and self.reference_loaded:
            result_df = self._apply_reference_matching(result_df, text_column)

        # Step 2: RxNorm API mapping for unmapped medications (if enabled)
        if use_rxnorm_api:
            result_df = self._apply_rxnorm_api_mapping(result_df, text_column, batch_size)

        # Step 3: Dose normalization (if enabled)
        if normalize_doses:
            result_df = self._apply_dose_normalization(result_df)

        # Step 4: Create final normalized medication identifiers
        result_df = self._create_final_identifiers(result_df)

        # Update statistics
        self.normalization_stats['end_time'] = datetime.now()
        self._calculate_final_statistics(result_df)

        # Log summary
        self._log_normalization_summary()

        return result_df

    def _apply_reference_matching(self, df: pd.DataFrame, text_column: str) -> pd.DataFrame:
        """Apply reference-based matching using MAP Pro data."""
        self.logger.info("Applying reference-based matching...")

        try:
            result_df = self.medication_matcher.batch_match_medications(
                df, text_column, prioritize_transplant=True
            )

            matched_count = result_df.get('has_reference_match', pd.Series(False)).sum()
            self.normalization_stats['reference_mapped'] = matched_count

            self.logger.info(f"Reference matching: {matched_count} medications matched")
            return result_df

        except Exception as e:
            self.logger.error(f"Reference matching failed: {str(e)}")
            self.normalization_stats['error_count'] += 1
            return df

    def _apply_rxnorm_api_mapping(
        self,
        df: pd.DataFrame,
        text_column: str,
        batch_size: int
    ) -> pd.DataFrame:
        """Apply RxNorm API mapping for unmapped medications."""
        self.logger.info("Applying RxNorm API mapping...")

        try:
            # Filter to unmapped medications
            if 'has_reference_match' in df.columns:
                unmapped_df = df[~df['has_reference_match']].copy()
                mapped_df = df[df['has_reference_match']].copy()
            else:
                unmapped_df = df.copy()
                mapped_df = pd.DataFrame()

            if unmapped_df.empty:
                self.logger.info("No unmapped medications for RxNorm API mapping")
                return df

            self.logger.info(f"Mapping {len(unmapped_df)} unmapped medications via RxNorm API")

            # Apply RxNorm API mapping
            rxnorm_mapped_df = self.rxnorm_mapper.batch_map_medications(
                unmapped_df, text_column, batch_size
            )

            # Combine results
            if not mapped_df.empty:
                # Ensure both DataFrames have the same columns
                all_columns = set(mapped_df.columns) | set(rxnorm_mapped_df.columns)
                for col in all_columns:
                    if col not in mapped_df.columns:
                        mapped_df[col] = None
                    if col not in rxnorm_mapped_df.columns:
                        rxnorm_mapped_df[col] = None

                result_df = pd.concat([mapped_df, rxnorm_mapped_df], ignore_index=True)
            else:
                result_df = rxnorm_mapped_df

            api_mapped_count = rxnorm_mapped_df.get('is_mapped', pd.Series(False)).sum()
            self.normalization_stats['rxnorm_api_mapped'] = api_mapped_count

            self.logger.info(f"RxNorm API mapping: {api_mapped_count} medications mapped")
            return result_df

        except Exception as e:
            self.logger.error(f"RxNorm API mapping failed: {str(e)}")
            self.normalization_stats['error_count'] += 1
            return df

    def _apply_dose_normalization(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply dose normalization."""
        self.logger.info("Applying dose normalization...")

        try:
            result_df = self.dose_normalizer.normalize_medication_dosing(df)

            normalized_count = (result_df.get('dose_normalization_status', '') == 'completed').sum()
            self.normalization_stats['dose_normalized'] = normalized_count

            self.logger.info(f"Dose normalization: {normalized_count} medications normalized")
            return result_df

        except Exception as e:
            self.logger.error(f"Dose normalization failed: {str(e)}")
            self.normalization_stats['error_count'] += 1
            return df

    def _create_final_identifiers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create final normalized medication identifiers."""
        self.logger.info("Creating final medication identifiers...")

        result_df = df.copy()

        # Add final normalization columns
        result_df['final_rxcui'] = None
        result_df['final_medication_name'] = None
        result_df['final_generic_name'] = None
        result_df['final_brand_name'] = None
        result_df['normalization_method'] = None
        result_df['normalization_confidence'] = None
        result_df['is_fully_normalized'] = False

        for idx, row in result_df.iterrows():
            # Determine best available RxCUI
            rxcui = None
            method = None
            confidence = 0.0

            # Priority 1: Reference match
            if row.get('has_reference_match', False) and row.get('reference_rxcui'):
                rxcui = row['reference_rxcui']
                method = 'reference_mapping'
                confidence = row.get('reference_match_score', 0.9)

            # Priority 2: RxNorm API match
            elif row.get('is_mapped', False) and row.get('rxcui'):
                rxcui = row['rxcui']
                method = 'rxnorm_api'
                confidence = row.get('match_score', 0.8)

            # Set final values
            if rxcui:
                result_df.at[idx, 'final_rxcui'] = rxcui
                result_df.at[idx, 'normalization_method'] = method
                result_df.at[idx, 'normalization_confidence'] = confidence

                # Set medication names
                if method == 'reference_mapping':
                    result_df.at[idx, 'final_medication_name'] = row.get('reference_generic_name') or row.get('reference_brand_name')
                    result_df.at[idx, 'final_generic_name'] = row.get('reference_generic_name')
                    result_df.at[idx, 'final_brand_name'] = row.get('reference_brand_name')
                elif method == 'rxnorm_api':
                    result_df.at[idx, 'final_medication_name'] = row.get('rxnorm_name')
                    # Note: API doesn't always distinguish generic vs brand

                # Check if fully normalized
                has_rxcui = bool(rxcui)
                has_dose = pd.notna(row.get('normalized_dose_value'))
                has_unit = pd.notna(row.get('normalized_dose_unit'))

                result_df.at[idx, 'is_fully_normalized'] = has_rxcui and (has_dose or has_unit)

        return result_df

    def _calculate_final_statistics(self, df: pd.DataFrame) -> None:
        """Calculate final normalization statistics."""
        total = len(df)

        # Count unmapped medications
        has_reference = df.get('has_reference_match', pd.Series(False)).sum()
        has_rxnorm = df.get('is_mapped', pd.Series(False)).sum()
        total_mapped = len(df[pd.notna(df.get('final_rxcui'))])

        self.normalization_stats['unmapped_count'] = total - total_mapped

        # Update mapped counts to avoid double counting
        self.normalization_stats['reference_mapped'] = has_reference
        self.normalization_stats['rxnorm_api_mapped'] = has_rxnorm - has_reference  # Only count pure API mappings

    def _log_normalization_summary(self) -> None:
        """Log normalization summary."""
        stats = self.normalization_stats
        total = stats['total_medications']

        if total == 0:
            return

        execution_time = (stats['end_time'] - stats['start_time']).total_seconds()

        total_mapped = stats['reference_mapped'] + stats['rxnorm_api_mapped']
        mapping_rate = (total_mapped / total) * 100

        self.logger.info(
            f"Normalization Summary:\n"
            f"  Total medications: {total:,}\n"
            f"  Reference mapped: {stats['reference_mapped']:,}\n"
            f"  RxNorm API mapped: {stats['rxnorm_api_mapped']:,}\n"
            f"  Dose normalized: {stats['dose_normalized']:,}\n"
            f"  Unmapped: {stats['unmapped_count']:,}\n"
            f"  Mapping rate: {mapping_rate:.1f}%\n"
            f"  Execution time: {execution_time:.2f} seconds\n"
            f"  Errors: {stats['error_count']}"
        )

    def get_normalization_statistics(self) -> Dict[str, Any]:
        """Get comprehensive normalization statistics."""
        stats = self.normalization_stats.copy()

        # Add component statistics
        stats['rxnorm_mapper_stats'] = self.rxnorm_mapper.get_mapping_statistics()
        stats['medication_matcher_stats'] = self.medication_matcher.get_matching_statistics()

        # Add configuration info
        stats['configuration'] = {
            'reference_data_loaded': self.reference_loaded,
            'map_pro_mappings_count': len(self.map_pro_mappings),
            'config_file': str(self.config_path)
        }

        return stats

    def generate_normalization_report(
        self,
        medications_df: pd.DataFrame,
        output_path: Optional[str] = None
    ) -> str:
        """
        Generate comprehensive normalization report.

        Args:
            medications_df: Normalized medications DataFrame
            output_path: Path to save report (optional)

        Returns:
            Report text
        """
        stats = self.get_normalization_statistics()

        # Generate component reports
        matcher_report = self.medication_matcher.generate_matching_report(medications_df)
        dose_report = self.dose_normalizer.generate_dose_normalization_report(medications_df)

        report_lines = [
            "# Medication Normalization Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## Executive Summary",
            f"- Total Medications: {stats['total_medications']:,}",
            f"- Successfully Mapped: {(stats['reference_mapped'] + stats['rxnorm_api_mapped']):,}",
            f"- Mapping Rate: {((stats['reference_mapped'] + stats['rxnorm_api_mapped']) / stats['total_medications'] * 100):.1f}%",
            f"- Dose Normalized: {stats['dose_normalized']:,}",
            f"- Transplant Medications: {matcher_report.get('summary', {}).get('transplant_medications', 0):,}",
            "",
            "## Mapping Methods",
            f"- Reference Mapping (MAP Pro): {stats['reference_mapped']:,}",
            f"- RxNorm API Mapping: {stats['rxnorm_api_mapped']:,}",
            f"- Unmapped: {stats['unmapped_count']:,}",
            "",
            "## Reference Matching Details",
        ]

        # Add reference matching details
        if matcher_report:
            match_types = matcher_report.get('match_types', {})
            for match_type, count in match_types.items():
                report_lines.append(f"- {match_type}: {count:,}")

            report_lines.extend([
                "",
                "## Transplant Medications by Category"
            ])

            transplant_categories = matcher_report.get('transplant_categories', {})
            for category, count in transplant_categories.items():
                report_lines.append(f"- {category}: {count:,}")

        # Add dose normalization details
        if dose_report:
            report_lines.extend([
                "",
                "## Dose Normalization Summary",
                f"- Dose Value Coverage: {dose_report.get('summary', {}).get('dose_value_coverage', 0):.1f}%",
                f"- Unit Coverage: {dose_report.get('summary', {}).get('unit_coverage', 0):.1f}%",
                "",
                "## Most Common Units"
            ])

            unit_dist = dose_report.get('unit_distribution', {})
            for unit, count in list(unit_dist.items())[:10]:
                report_lines.append(f"- {unit}: {count:,}")

        # Add API usage statistics
        if 'rxnorm_mapper_stats' in stats:
            api_stats = stats['rxnorm_mapper_stats']['api_usage']
            cache_stats = stats['rxnorm_mapper_stats']['cache_stats']

            report_lines.extend([
                "",
                "## RxNorm API Usage",
                f"- Total API Requests: {api_stats['total_requests']:,}",
                f"- Cache Hit Rate: {cache_stats['hit_rate']:.1f}%",
                f"- Failed Requests: {api_stats['failed_requests']:,}",
                f"- Rate Limited Delays: {api_stats['rate_limited_delays']:,}"
            ])

        # Add top unmapped medications
        if matcher_report and 'top_unmapped_medications' in matcher_report:
            report_lines.extend([
                "",
                "## Top Unmapped Medications",
                "*(Candidates for manual review)*"
            ])

            unmapped = matcher_report['top_unmapped_medications']
            for medication, count in list(unmapped.items())[:15]:
                report_lines.append(f"- {medication}: {count:,} occurrences")

        # Add configuration summary
        report_lines.extend([
            "",
            "## Configuration",
            f"- Reference Data Loaded: {stats['configuration']['reference_data_loaded']}",
            f"- MAP Pro Mappings: {stats['configuration']['map_pro_mappings_count']:,}",
            f"- Fuzzy Matching Library: {stats['medication_matcher_stats']['configuration']['fuzzy_library']}"
        ])

        report_text = "\n".join(report_lines)

        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            self.logger.info(f"Normalization report saved to {output_path}")

        return report_text


def run_normalization_pipeline(
    medications_df: pd.DataFrame,
    config_path: Optional[str] = None,
    source_config_path: Optional[str] = None,
    text_column: str = 'medication_text',
    use_reference_matching: bool = True,
    use_rxnorm_api: bool = True,
    normalize_doses: bool = True,
    output_dir: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function to run the complete normalization pipeline.

    Args:
        medications_df: DataFrame with extracted medication data
        config_path: Path to RxNorm configuration file
        source_config_path: Path to source configuration file
        text_column: Column containing medication text
        use_reference_matching: Whether to use MAP Pro reference matching
        use_rxnorm_api: Whether to use RxNorm API
        normalize_doses: Whether to normalize dosing information
        output_dir: Directory to save output files

    Returns:
        Dictionary with normalization results and statistics
    """
    # Initialize orchestrator
    orchestrator = NormalizationOrchestrator(config_path)

    # Load reference data if enabled
    if use_reference_matching:
        orchestrator.load_map_pro_reference_data(source_config_path)

    # Run normalization
    normalized_df = orchestrator.normalize_medications(
        medications_df=medications_df,
        text_column=text_column,
        use_reference_matching=use_reference_matching,
        use_rxnorm_api=use_rxnorm_api,
        normalize_doses=normalize_doses
    )

    # Get statistics
    stats = orchestrator.get_normalization_statistics()

    results = {
        'status': 'success' if stats['error_count'] == 0 else 'partial',
        'normalized_data': normalized_df,
        'statistics': stats
    }

    # Save outputs if directory specified
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save normalized data
        normalized_file = output_path / "normalized_medications.parquet"
        normalized_df.to_parquet(normalized_file, index=False)
        results['normalized_file'] = str(normalized_file)

        # Save normalization report
        report_file = output_path / "normalization_report.md"
        orchestrator.generate_normalization_report(normalized_df, str(report_file))
        results['report_file'] = str(report_file)

        # Save statistics as JSON
        import json
        stats_file = output_path / "normalization_statistics.json"
        with open(stats_file, 'w') as f:
            # Convert datetime objects to strings for JSON serialization
            json_stats = stats.copy()
            if json_stats.get('start_time'):
                json_stats['start_time'] = json_stats['start_time'].isoformat()
            if json_stats.get('end_time'):
                json_stats['end_time'] = json_stats['end_time'].isoformat()

            json.dump(json_stats, f, indent=2, default=str)

        results['statistics_file'] = str(stats_file)

    return results