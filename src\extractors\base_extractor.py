"""
Base extractor class for medication data extraction.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import pandas as pd
from datetime import datetime, timedelta

from ..utils.logger import LoggerMixin
from ..utils.database_connector import DatabaseConnector
from ..utils.exceptions import ExtractionError
from ..utils.constants import SourceSystem, ProcessingStatus


class BaseExtractor(ABC, LoggerMixin):
    """
    Abstract base class for medication data extractors.

    All source-specific extractors should inherit from this class
    and implement the required abstract methods.
    """

    def __init__(self, config: Dict[str, Any], source_system: SourceSystem):
        """
        Initialize base extractor.

        Args:
            config: Source-specific configuration
            source_system: Source system enum
        """
        self.config = config
        self.source_system = source_system
        self.db_connector = None
        self.extraction_stats = {
            'start_time': None,
            'end_time': None,
            'status': ProcessingStatus.PENDING,
            'extracted_count': 0,
            'error_count': 0,
            'errors': []
        }

        self._validate_config()

    def _validate_config(self) -> None:
        """Validate configuration for this extractor."""
        if not self.config.get('enabled', True):
            self.logger.info(f"{self.source_system.value} extraction is disabled")
            return

        required_keys = ['database']
        missing_keys = [key for key in required_keys if key not in self.config]
        if missing_keys:
            raise ExtractionError(
                f"Missing required configuration keys: {missing_keys}",
                source_system=self.source_system.value
            )

    def connect_database(self) -> DatabaseConnector:
        """
        Establish database connection.

        Returns:
            Database connector instance
        """
        if self.db_connector is None:
            try:
                self.db_connector = DatabaseConnector(self.config['database'])
                self.logger.info(f"Connected to {self.source_system.value} database")
            except Exception as e:
                raise ExtractionError(
                    f"Failed to connect to database: {str(e)}",
                    source_system=self.source_system.value
                )

        return self.db_connector

    def get_unified_schema(self) -> List[str]:
        """
        Get the unified medication schema columns.

        Returns:
            List of column names for unified schema
        """
        return [
            'source_system',
            'patient_id',
            'medication_text',
            'medication_code',
            'dose_value',
            'dose_unit',
            'route',
            'frequency',
            'start_date',
            'end_date',
            'source_table',
            'source_record_id',
            'extraction_timestamp'
        ]

    def create_unified_record(
        self,
        patient_id: str,
        medication_text: str,
        source_table: str,
        source_record_id: str,
        medication_code: Optional[str] = None,
        dose_value: Optional[float] = None,
        dose_unit: Optional[str] = None,
        route: Optional[str] = None,
        frequency: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Create a unified medication record.

        Args:
            patient_id: Patient identifier
            medication_text: Raw medication text/name
            source_table: Source table name
            source_record_id: Source record identifier
            medication_code: Source medication code (NDC, ItemID, etc.)
            dose_value: Numeric dose value
            dose_unit: Dose unit
            route: Administration route
            frequency: Dosing frequency
            start_date: Start date
            end_date: End date

        Returns:
            Unified medication record dictionary
        """
        return {
            'source_system': self.source_system.value,
            'patient_id': str(patient_id),
            'medication_text': str(medication_text) if medication_text else None,
            'medication_code': str(medication_code) if medication_code else None,
            'dose_value': float(dose_value) if dose_value is not None else None,
            'dose_unit': str(dose_unit) if dose_unit else None,
            'route': str(route) if route else None,
            'frequency': str(frequency) if frequency else None,
            'start_date': start_date,
            'end_date': end_date,
            'source_table': source_table,
            'source_record_id': str(source_record_id),
            'extraction_timestamp': datetime.now()
        }

    def apply_date_filter(self, query: str, date_column: str) -> str:
        """
        Apply date filter to SQL query if configured.

        Args:
            query: Base SQL query
            date_column: Date column name for filtering

        Returns:
            Modified query with date filter
        """
        lookback_days = self.config.get('extraction', {}).get('lookback_days')
        if not lookback_days:
            return query

        cutoff_date = datetime.now() - timedelta(days=lookback_days)

        # Add WHERE clause or AND condition
        if 'WHERE' in query.upper():
            date_filter = f" AND {date_column} >= '{cutoff_date.strftime('%Y-%m-%d')}'"
        else:
            date_filter = f" WHERE {date_column} >= '{cutoff_date.strftime('%Y-%m-%d')}'"

        return query + date_filter

    def apply_record_limit(self, query: str) -> str:
        """
        Apply record limit to SQL query if configured.

        Args:
            query: Base SQL query

        Returns:
            Modified query with record limit
        """
        max_records = self.config.get('extraction', {}).get('max_records')
        if not max_records:
            return query

        # Add TOP clause for SQL Server or LIMIT for others
        db_type = self.config.get('database', {}).get('type', '').lower()
        if db_type == 'sql_server':
            # Insert TOP after SELECT
            query = query.replace('SELECT', f'SELECT TOP {max_records}', 1)
        else:
            # Add LIMIT at the end
            query += f' LIMIT {max_records}'

        return query

    @abstractmethod
    def extract_medications(self) -> pd.DataFrame:
        """
        Extract medication data from the source system.

        Returns:
            DataFrame with medication data in unified schema

        Raises:
            ExtractionError: If extraction fails
        """
        pass

    @abstractmethod
    def extract_patients(self) -> pd.DataFrame:
        """
        Extract patient data from the source system.

        Returns:
            DataFrame with patient data

        Raises:
            ExtractionError: If extraction fails
        """
        pass

    def validate_extraction(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate extracted data quality.

        Args:
            df: Extracted DataFrame

        Returns:
            Validation report dictionary
        """
        validation_report = {
            'total_records': len(df),
            'validation_errors': [],
            'warnings': [],
            'statistics': {}
        }

        if df.empty:
            validation_report['validation_errors'].append("No records extracted")
            return validation_report

        # Check required columns
        required_columns = ['patient_id', 'medication_text', 'source_table', 'source_record_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            validation_report['validation_errors'].append(f"Missing required columns: {missing_columns}")

        # Check for null values in required fields
        for col in required_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    validation_report['warnings'].append(f"{col}: {null_count} null values")

        # Generate statistics
        validation_report['statistics'] = {
            'unique_patients': df['patient_id'].nunique() if 'patient_id' in df.columns else 0,
            'unique_medications': df['medication_text'].nunique() if 'medication_text' in df.columns else 0,
            'records_with_dose': df['dose_value'].notna().sum() if 'dose_value' in df.columns else 0,
            'records_with_route': df['route'].notna().sum() if 'route' in df.columns else 0,
            'date_range': {
                'min_date': df['start_date'].min() if 'start_date' in df.columns else None,
                'max_date': df['start_date'].max() if 'start_date' in df.columns else None
            }
        }

        return validation_report

    def run_extraction(self) -> pd.DataFrame:
        """
        Run the complete extraction process with error handling and logging.

        Returns:
            Extracted and validated DataFrame

        Raises:
            ExtractionError: If extraction fails
        """
        self.extraction_stats['start_time'] = datetime.now()
        self.extraction_stats['status'] = ProcessingStatus.IN_PROGRESS

        try:
            self.logger.info(f"Starting extraction from {self.source_system.value}")

            # Check if extraction is enabled
            if not self.config.get('enabled', True):
                self.logger.info(f"Extraction from {self.source_system.value} is disabled")
                self.extraction_stats['status'] = ProcessingStatus.SKIPPED
                return pd.DataFrame()

            # Extract data
            df = self.extract_medications()

            # Validate extraction
            validation_report = self.validate_extraction(df)

            # Update statistics
            self.extraction_stats['extracted_count'] = len(df)
            self.extraction_stats['end_time'] = datetime.now()
            self.extraction_stats['status'] = ProcessingStatus.COMPLETED

            # Log results
            execution_time = (self.extraction_stats['end_time'] - self.extraction_stats['start_time']).total_seconds()
            self.log_extraction_metrics(
                source_system=self.source_system.value,
                table_name="multiple",
                extracted_count=len(df),
                execution_time=execution_time
            )

            # Log validation results
            if validation_report['validation_errors']:
                self.logger.error(f"Validation errors: {validation_report['validation_errors']}")
                raise ExtractionError(
                    f"Data validation failed: {validation_report['validation_errors']}",
                    source_system=self.source_system.value
                )

            if validation_report['warnings']:
                self.logger.warning(f"Validation warnings: {validation_report['warnings']}")

            self.logger.info(f"Successfully extracted {len(df)} records from {self.source_system.value}")
            return df

        except Exception as e:
            self.extraction_stats['status'] = ProcessingStatus.FAILED
            self.extraction_stats['end_time'] = datetime.now()
            self.extraction_stats['error_count'] += 1
            self.extraction_stats['errors'].append(str(e))

            self.logger.error(f"Extraction from {self.source_system.value} failed: {str(e)}")
            raise ExtractionError(
                f"Extraction failed: {str(e)}",
                source_system=self.source_system.value
            )

        finally:
            # Close database connection
            if self.db_connector:
                self.db_connector.close()

    def get_extraction_stats(self) -> Dict[str, Any]:
        """
        Get extraction statistics.

        Returns:
            Dictionary with extraction statistics
        """
        return self.extraction_stats.copy()