"""
RxNorm API integration and medication mapping functionality.
"""

import pandas as pd
import requests
import time
import json
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import hashlib

from ..utils.logger import LoggerMixin
from ..utils.exceptions import RxNorm<PERSON>IError, NormalizationError
from ..utils.constants import RXNORM_TERM_TYPE_PREFERENCE


class RxNormMapper(LoggerMixin):
    """
    RxNorm API integration for medication normalization.

    Provides comprehensive medication mapping using the RxNorm REST API,
    with caching, rate limiting, and fuzzy matching capabilities.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize RxNorm mapper.

        Args:
            config: RxNorm configuration dictionary
        """
        self.config = config
        self.api_config = config.get('api', {})
        self.mapping_config = config.get('mapping', {})
        self.cache_config = config.get('cache', {})

        self.base_url = self.api_config.get('base_url', 'https://rxnav.nlm.nih.gov/REST')
        self.timeout = self.api_config.get('timeout', 30)
        self.rate_limit = self.api_config.get('rate_limit', 20)  # requests per second
        self.retry_attempts = self.api_config.get('retry_attempts', 3)
        self.retry_delay = self.api_config.get('retry_delay', 1)

        # Initialize session with rate limiting
        self.session = requests.Session()
        self.last_request_time = 0

        # Initialize cache
        self.cache = self._initialize_cache()

        # Track API usage
        self.api_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'failed_requests': 0,
            'rate_limited_delays': 0
        }

    def _initialize_cache(self) -> Dict[str, Any]:
        """Initialize medication mapping cache."""
        if not self.cache_config.get('enabled', True):
            return {}

        cache_type = self.cache_config.get('type', 'file')

        if cache_type == 'file':
            return self._initialize_file_cache()
        elif cache_type == 'memory':
            return {}
        else:
            self.logger.warning(f"Unsupported cache type: {cache_type}, using memory cache")
            return {}

    def _initialize_file_cache(self) -> Dict[str, Any]:
        """Initialize file-based cache."""
        cache_dir = Path(self.cache_config.get('cache_directory', 'cache/rxnorm'))
        cache_dir.mkdir(parents=True, exist_ok=True)

        cache_file = cache_dir / 'rxnorm_mappings.json'

        if cache_file.exists():
            try:
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)

                # Check cache TTL
                ttl_hours = self.cache_config.get('ttl_hours', 24)
                cache_time = datetime.fromisoformat(cache_data.get('timestamp', '1970-01-01'))

                if datetime.now() - cache_time < timedelta(hours=ttl_hours):
                    self.logger.info(f"Loaded RxNorm cache with {len(cache_data.get('mappings', {}))} entries")
                    return cache_data.get('mappings', {})
                else:
                    self.logger.info("RxNorm cache expired, starting fresh")

            except Exception as e:
                self.logger.error(f"Failed to load cache: {str(e)}")

        return {}

    def _save_cache(self) -> None:
        """Save cache to file."""
        if (self.cache_config.get('type', 'file') != 'file' or
            not self.cache_config.get('enabled', True)):
            return

        try:
            cache_dir = Path(self.cache_config.get('cache_directory', 'cache/rxnorm'))
            cache_file = cache_dir / 'rxnorm_mappings.json'

            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'mappings': self.cache
            }

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            self.logger.info(f"Saved RxNorm cache with {len(self.cache)} entries")

        except Exception as e:
            self.logger.error(f"Failed to save cache: {str(e)}")

    def _get_cache_key(self, medication_text: str, search_type: str = 'standard') -> str:
        """Generate cache key for medication text."""
        # Normalize text for consistent caching
        normalized_text = self._normalize_medication_text(medication_text)
        key_string = f"{normalized_text}:{search_type}"
        return hashlib.md5(key_string.encode()).hexdigest()

    def _rate_limit(self) -> None:
        """Implement rate limiting for API requests."""
        if self.rate_limit <= 0:
            return

        time_since_last = time.time() - self.last_request_time
        min_interval = 1.0 / self.rate_limit

        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            time.sleep(sleep_time)
            self.api_stats['rate_limited_delays'] += 1

        self.last_request_time = time.time()

    def _make_api_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make rate-limited API request to RxNorm.

        Args:
            endpoint: API endpoint (relative to base URL)
            params: Query parameters

        Returns:
            JSON response data

        Raises:
            RxNormAPIError: If API request fails
        """
        url = f"{self.base_url}/{endpoint}"

        for attempt in range(self.retry_attempts):
            try:
                self._rate_limit()

                response = self.session.get(
                    url,
                    params=params,
                    timeout=self.timeout
                )

                self.api_stats['total_requests'] += 1
                response.raise_for_status()

                return response.json()

            except requests.exceptions.RequestException as e:
                self.api_stats['failed_requests'] += 1

                if attempt == self.retry_attempts - 1:
                    raise RxNormAPIError(
                        f"RxNorm API request failed after {self.retry_attempts} attempts: {str(e)}",
                        api_endpoint=endpoint,
                        status_code=getattr(e.response, 'status_code', None),
                        response_text=getattr(e.response, 'text', None)
                    )

                # Wait before retry
                time.sleep(self.retry_delay * (attempt + 1))

    def search_medication(self, medication_text: str, search_type: str = 'standard') -> List[Dict[str, Any]]:
        """
        Search for medication in RxNorm.

        Args:
            medication_text: Medication name or description
            search_type: Type of search ('standard', 'approximate', 'fuzzy')

        Returns:
            List of RxNorm concept dictionaries
        """
        if not medication_text or not medication_text.strip():
            return []

        # Check cache first
        cache_key = self._get_cache_key(medication_text, search_type)
        if cache_key in self.cache:
            self.api_stats['cache_hits'] += 1
            return self.cache[cache_key]

        self.api_stats['cache_misses'] += 1

        try:
            # Clean and normalize medication text
            normalized_text = self._normalize_medication_text(medication_text)

            if search_type == 'standard':
                results = self._search_exact(normalized_text)
            elif search_type == 'approximate':
                results = self._search_approximate(normalized_text)
            elif search_type == 'fuzzy':
                results = self._search_fuzzy(normalized_text)
            else:
                raise ValueError(f"Unknown search type: {search_type}")

            # Cache results
            self.cache[cache_key] = results

            # Limit cache size
            max_entries = self.cache_config.get('max_entries', 10000)
            if len(self.cache) > max_entries:
                # Remove oldest entries (simple FIFO)
                oldest_keys = list(self.cache.keys())[:len(self.cache) - max_entries + 1000]
                for key in oldest_keys:
                    del self.cache[key]

            return results

        except Exception as e:
            self.logger.error(f"Failed to search medication '{medication_text}': {str(e)}")
            return []

    def _search_exact(self, medication_text: str) -> List[Dict[str, Any]]:
        """Search for exact medication matches."""
        try:
            response = self._make_api_request('drugs.json', {'name': medication_text})

            drugs_group = response.get('drugGroup', {})
            concepts = drugs_group.get('conceptGroup', [])

            results = []
            for concept_group in concepts:
                if concept_group.get('tty') in self.mapping_config.get('preferred_term_types', ['SCD', 'SBD']):
                    concept_properties = concept_group.get('conceptProperties', [])
                    for concept in concept_properties:
                        results.append(self._format_concept_result(concept, 'exact'))

            return results

        except Exception as e:
            self.logger.error(f"Exact search failed for '{medication_text}': {str(e)}")
            return []

    def _search_approximate(self, medication_text: str) -> List[Dict[str, Any]]:
        """Search for approximate medication matches."""
        try:
            response = self._make_api_request('aproximateTerm.json', {'term': medication_text})

            approximate_group = response.get('approximateGroup', {})
            candidates = approximate_group.get('candidate', [])

            results = []
            for candidate in candidates[:10]:  # Limit results
                rxcui = candidate.get('rxcui')
                if rxcui:
                    concept_details = self.get_concept_details(rxcui)
                    if concept_details:
                        concept_details['match_type'] = 'approximate'
                        concept_details['match_score'] = candidate.get('score', 0)
                        results.append(concept_details)

            return results

        except Exception as e:
            self.logger.error(f"Approximate search failed for '{medication_text}': {str(e)}")
            return []

    def _search_fuzzy(self, medication_text: str) -> List[Dict[str, Any]]:
        """Search using fuzzy string matching with RxNorm spelling suggestions."""
        try:
            # Try spelling suggestions first
            response = self._make_api_request('spellingsuggestions.json', {'name': medication_text})

            suggestions_group = response.get('suggestionGroup', {})
            suggestions = suggestions_group.get('suggestionList', {}).get('suggestion', [])

            results = []

            # Search each suggestion
            for suggestion in suggestions[:5]:  # Limit suggestions
                suggestion_results = self._search_exact(suggestion)
                for result in suggestion_results:
                    result['match_type'] = 'fuzzy'
                    result['suggested_term'] = suggestion
                    results.append(result)

            return results

        except Exception as e:
            self.logger.error(f"Fuzzy search failed for '{medication_text}': {str(e)}")
            return []

    def get_concept_details(self, rxcui: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information for an RxCUI.

        Args:
            rxcui: RxNorm concept unique identifier

        Returns:
            Dictionary with concept details or None if not found
        """
        try:
            # Get basic concept information
            response = self._make_api_request(f'rxcui/{rxcui}/properties.json')

            properties = response.get('properties', {})
            if not properties:
                return None

            concept_details = self._format_concept_result(properties, 'direct')

            # Get additional concept relationships
            relationships = self.get_concept_relationships(rxcui)
            concept_details['relationships'] = relationships

            # Get ingredients
            ingredients = self.get_concept_ingredients(rxcui)
            concept_details['ingredients'] = ingredients

            return concept_details

        except Exception as e:
            self.logger.error(f"Failed to get concept details for RxCUI {rxcui}: {str(e)}")
            return None

    def get_concept_relationships(self, rxcui: str) -> Dict[str, List[Dict]]:
        """Get concept relationships (generic, brand, etc.)."""
        try:
            response = self._make_api_request(f'rxcui/{rxcui}/related.json')

            related_group = response.get('relatedGroup', {})
            concept_groups = related_group.get('conceptGroup', [])

            relationships = {}

            for group in concept_groups:
                rel_type = group.get('tty')
                if rel_type:
                    relationships[rel_type] = []

                    concept_properties = group.get('conceptProperties', [])
                    for concept in concept_properties:
                        relationships[rel_type].append({
                            'rxcui': concept.get('rxcui'),
                            'name': concept.get('name'),
                            'synonym': concept.get('synonym')
                        })

            return relationships

        except Exception as e:
            self.logger.error(f"Failed to get relationships for RxCUI {rxcui}: {str(e)}")
            return {}

    def get_concept_ingredients(self, rxcui: str) -> List[Dict[str, Any]]:
        """Get active ingredients for a concept."""
        try:
            response = self._make_api_request(f'rxcui/{rxcui}/allrelated.json')

            related_group = response.get('allRelatedGroup', {})
            concept_groups = related_group.get('conceptGroup', [])

            ingredients = []

            for group in concept_groups:
                if group.get('tty') in ['IN', 'PIN', 'MIN']:  # Ingredient types
                    concept_properties = group.get('conceptProperties', [])
                    for concept in concept_properties:
                        ingredients.append({
                            'rxcui': concept.get('rxcui'),
                            'name': concept.get('name'),
                            'type': group.get('tty')
                        })

            return ingredients

        except Exception as e:
            self.logger.error(f"Failed to get ingredients for RxCUI {rxcui}: {str(e)}")
            return []

    def _format_concept_result(self, concept: Dict[str, Any], match_type: str = 'exact') -> Dict[str, Any]:
        """Format RxNorm concept data into standardized result."""
        return {
            'rxcui': concept.get('rxcui'),
            'name': concept.get('name'),
            'synonym': concept.get('synonym'),
            'tty': concept.get('tty'),  # Term type
            'language': concept.get('language', 'ENG'),
            'suppress': concept.get('suppress', 'N'),
            'umlscui': concept.get('umlscui'),
            'match_type': match_type,
            'match_score': concept.get('score', 1.0),
            'search_timestamp': datetime.now().isoformat()
        }

    def _normalize_medication_text(self, text: str) -> str:
        """
        Normalize medication text for consistent searching.

        Args:
            text: Raw medication text

        Returns:
            Normalized text
        """
        if not text:
            return ""

        # Convert to lowercase
        normalized = text.lower().strip()

        # Remove common medication suffixes/prefixes that might interfere
        suffixes_to_remove = [
            r'\s+tab\b', r'\s+tablet\b', r'\s+cap\b', r'\s+capsule\b',
            r'\s+mg\b', r'\s+mcg\b', r'\s+g\b', r'\s+ml\b',
            r'\s+oral\b', r'\s+po\b', r'\s+iv\b', r'\s+im\b'
        ]

        for suffix in suffixes_to_remove:
            normalized = re.sub(suffix, '', normalized)

        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        # Remove special characters but keep hyphens and spaces
        normalized = re.sub(r'[^\w\s\-]', '', normalized)

        return normalized

    def find_best_match(
        self,
        medication_text: str,
        preferred_tty: List[str] = None,
        min_score: float = 0.8
    ) -> Optional[Dict[str, Any]]:
        """
        Find the best RxNorm match for a medication.

        Args:
            medication_text: Medication text to search
            preferred_tty: Preferred term types in order of preference
            min_score: Minimum match score threshold

        Returns:
            Best matching concept or None
        """
        if preferred_tty is None:
            preferred_tty = self.mapping_config.get('preferred_term_types', RXNORM_TERM_TYPE_PREFERENCE)

        # Try different search strategies in order
        search_strategies = ['standard', 'approximate', 'fuzzy']

        all_results = []

        for strategy in search_strategies:
            results = self.search_medication(medication_text, strategy)
            all_results.extend(results)

            # If we have good exact matches, use those
            if strategy == 'standard' and results:
                break

        if not all_results:
            return None

        # Score and rank results
        scored_results = []

        for result in all_results:
            score = self._calculate_match_score(result, medication_text, preferred_tty)
            if score >= min_score:
                result['final_score'] = score
                scored_results.append(result)

        if not scored_results:
            return None

        # Return best match
        best_match = max(scored_results, key=lambda x: x['final_score'])

        self.logger.debug(
            f"Best match for '{medication_text}': {best_match['name']} "
            f"(RxCUI: {best_match['rxcui']}, Score: {best_match['final_score']:.3f})"
        )

        return best_match

    def _calculate_match_score(
        self,
        result: Dict[str, Any],
        original_text: str,
        preferred_tty: List[str]
    ) -> float:
        """Calculate overall match score for a result."""
        score = 0.0

        # Base score from match type
        match_type_scores = {
            'exact': 1.0,
            'approximate': 0.8,
            'fuzzy': 0.6,
            'direct': 1.0
        }

        score += match_type_scores.get(result.get('match_type', 'exact'), 0.5)

        # Bonus for preferred term types
        tty = result.get('tty', '')
        if tty in preferred_tty:
            tty_bonus = (len(preferred_tty) - preferred_tty.index(tty)) / len(preferred_tty) * 0.3
            score += tty_bonus

        # API-provided match score
        api_score = result.get('match_score', 1.0)
        if isinstance(api_score, (int, float)):
            score += api_score * 0.2

        # Text similarity bonus
        result_name = result.get('name', '').lower()
        original_lower = original_text.lower()

        if result_name in original_lower or original_lower in result_name:
            score += 0.2

        # Penalize suppressed concepts
        if result.get('suppress', 'N') != 'N':
            score -= 0.3

        return min(score, 1.0)  # Cap at 1.0

    def batch_map_medications(
        self,
        medications_df: pd.DataFrame,
        text_column: str = 'medication_text',
        batch_size: int = 100
    ) -> pd.DataFrame:
        """
        Map multiple medications to RxNorm in batches.

        Args:
            medications_df: DataFrame with medication data
            text_column: Column containing medication text
            batch_size: Number of medications to process per batch

        Returns:
            DataFrame with RxNorm mapping results
        """
        if medications_df.empty or text_column not in medications_df.columns:
            return medications_df

        self.logger.info(f"Starting batch mapping of {len(medications_df)} medications")

        # Get unique medication texts for efficiency
        unique_medications = medications_df[text_column].dropna().unique()
        self.logger.info(f"Found {len(unique_medications)} unique medication texts")

        # Create mapping dictionary
        medication_mappings = {}

        # Process in batches
        for i in range(0, len(unique_medications), batch_size):
            batch = unique_medications[i:i + batch_size]
            self.logger.info(f"Processing batch {i//batch_size + 1}/{(len(unique_medications)-1)//batch_size + 1}")

            for medication_text in batch:
                try:
                    best_match = self.find_best_match(medication_text)
                    medication_mappings[medication_text] = best_match

                except Exception as e:
                    self.logger.error(f"Failed to map medication '{medication_text}': {str(e)}")
                    medication_mappings[medication_text] = None

        # Add mapping results to DataFrame
        result_df = medications_df.copy()

        # Add RxNorm mapping columns
        rxnorm_columns = {
            'rxcui': None,
            'rxnorm_name': None,
            'rxnorm_tty': None,
            'match_type': None,
            'match_score': None,
            'is_mapped': False
        }

        for col, default in rxnorm_columns.items():
            result_df[col] = default

        # Populate mapping results
        for idx, row in result_df.iterrows():
            medication_text = row[text_column]
            mapping = medication_mappings.get(medication_text)

            if mapping:
                result_df.at[idx, 'rxcui'] = mapping.get('rxcui')
                result_df.at[idx, 'rxnorm_name'] = mapping.get('name')
                result_df.at[idx, 'rxnorm_tty'] = mapping.get('tty')
                result_df.at[idx, 'match_type'] = mapping.get('match_type')
                result_df.at[idx, 'match_score'] = mapping.get('final_score')
                result_df.at[idx, 'is_mapped'] = True

        # Calculate mapping statistics
        total_medications = len(result_df)
        mapped_count = result_df['is_mapped'].sum()
        mapping_rate = (mapped_count / total_medications) * 100 if total_medications > 0 else 0

        self.logger.info(
            f"Mapping complete: {mapped_count}/{total_medications} "
            f"({mapping_rate:.1f}%) successfully mapped"
        )

        # Save cache
        self._save_cache()

        return result_df

    def get_mapping_statistics(self) -> Dict[str, Any]:
        """Get comprehensive mapping and API usage statistics."""
        cache_size = len(self.cache)

        return {
            'api_usage': self.api_stats.copy(),
            'cache_stats': {
                'size': cache_size,
                'hit_rate': (
                    self.api_stats['cache_hits'] /
                    (self.api_stats['cache_hits'] + self.api_stats['cache_misses'])
                    * 100
                ) if (self.api_stats['cache_hits'] + self.api_stats['cache_misses']) > 0 else 0,
                'enabled': self.cache_config.get('enabled', True),
                'type': self.cache_config.get('type', 'file')
            },
            'configuration': {
                'rate_limit': self.rate_limit,
                'preferred_term_types': self.mapping_config.get('preferred_term_types', []),
                'fuzzy_matching_enabled': self.mapping_config.get('fuzzy_matching', {}).get('enabled', True)
            }
        }

    def __del__(self):
        """Cleanup: save cache on object destruction."""
        try:
            self._save_cache()
        except:
            pass