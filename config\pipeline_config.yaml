# Medication Normalization Pipeline Configuration
# This file configures the complete medication normalization workflow

# Pipeline settings
pipeline:
  name: "Transplant Medication Normalization"
  version: "1.0"
  description: "Normalize transplant medication data to OMOP CDM format"

  # Processing options
  processing:
    batch_size: 1000
    max_workers: 4
    timeout_seconds: 3600
    retry_attempts: 3

# Source system configurations
# These reference the detailed configurations in source_configs.yaml
sources:
  map_pro:
    enabled: true
    config_file: "config/source_configs.yaml"
    priority: 1  # Highest priority for reference mappings

  pioneer_rx:
    enabled: true
    config_file: "config/source_configs.yaml"
    priority: 2

  allocare:
    enabled: true
    config_file: "config/source_configs.yaml"
    priority: 3

# RxNorm normalization configuration
rxnorm:
  config_file: "config/rxnorm_config.yaml"

  # Mapping strategy
  mapping:
    use_reference_mappings: true
    fuzzy_matching_enabled: true
    fuzzy_threshold: 80
    max_api_calls_per_minute: 300
    cache_enabled: true
    cache_duration_hours: 24

  # Transplant medication focus
  transplant_focus:
    enabled: true
    immunosuppressive_priority: true
    drug_classes:
      - "immunosuppressive"
      - "anti-rejection"
      - "corticosteroid"

# OMOP CDM transformation configuration
omop:
  # CDM version and settings
  cdm:
    version: "6.0"
    schema_validation: true

  # Drug exposure settings
  drug_exposure:
    person_id_strategy: "hash_based"  # hash_based or sequential
    person_id_salt: "${PERSON_ID_SALT:-medication_normalization_2024}"
    drug_type_concept_mapping:
      map_pro_web: 32817      # EHR
      map_pro_mobile: 32817   # EHR
      pioneer_rx: 32818       # Prescription dispensed in pharmacy
      allocare: 32817         # EHR

    # Dose handling
    dose_mapping:
      default_unit_concept_id: 8576  # milligram
      unit_standardization: true
      preserve_original_values: true

    # Route mapping
    route_mapping:
      oral: 4128792
      intravenous: 4112421
      intramuscular: 4142048
      subcutaneous: 4142047
      topical: 4262099
      default: 0  # No matching concept

  # Era generation settings
  drug_era:
    gap_days: 30
    min_era_length_days: 1
    stockpile_days: 180

  dose_era:
    gap_days: 30
    min_era_length_days: 1
    dose_tolerance: 0.1  # 10% tolerance for dose matching

  # Vocabulary generation
  vocabulary:
    include_standard_concepts: true
    include_source_concepts: true
    custom_vocabulary_id: "TransplantMed"
    generate_mappings_table: true

  # Output settings
  output:
    file_format: "parquet"  # parquet or csv
    compression: "snappy"
    include_statistics: true
    generate_report: true

# Validation configuration
validation:
  # Data validation rules
  data_validation:
    enabled: true
    severity_levels: ["CRITICAL", "ERROR", "WARNING"]
    fail_on_critical: true
    fail_on_error: false

    # Field validation
    required_fields:
      - "person_id"
      - "medication_name"
      - "source_system"

    # Date validation
    date_validation:
      min_date: "1900-01-01"
      max_date: "2030-12-31"
      future_date_warning: true

    # Business rules
    business_rules:
      max_dose_per_day: 1000  # mg
      max_medication_duration_days: 3650  # 10 years

  # Mapping validation
  mapping_validation:
    enabled: true
    min_mapping_coverage: 0.80  # 80% of medications should map
    transplant_medication_coverage: 0.95  # 95% for transplant meds
    confidence_threshold: 0.70

  # OMOP validation
  omop_validation:
    enabled: true
    schema_compliance: true
    foreign_key_validation: false  # Set to true if you have full OMOP vocab
    data_quality_checks: true

# Logging configuration
logging:
  level: "INFO"
  format: "json"
  include_performance_metrics: true

  # File logging
  file_logging:
    enabled: true
    log_directory: "logs"
    max_file_size_mb: 100
    backup_count: 5

  # Components to log
  components:
    extractors: true
    normalizers: true
    transformers: true
    validators: true

# Performance monitoring
monitoring:
  enabled: true
  collect_metrics: true

  # Performance thresholds
  thresholds:
    extraction_records_per_second: 1000
    normalization_records_per_second: 100
    transformation_records_per_second: 500

  # Memory monitoring
  memory:
    max_memory_usage_gb: 8
    gc_threshold_mb: 1000

# Error handling
error_handling:
  continue_on_extraction_error: true
  continue_on_normalization_error: true
  continue_on_transformation_error: false
  continue_on_validation_error: true

  # Retry settings
  retry:
    max_attempts: 3
    backoff_factor: 2
    retry_on_timeout: true

# Output configuration
output:
  # Base output directory
  base_directory: "output"

  # Directory structure
  structure:
    create_timestamped_dirs: true
    include_source_separation: false

  # File naming
  naming:
    include_timestamp: true
    include_source: false
    timestamp_format: "%Y%m%d_%H%M%S"

  # Generated files
  files:
    save_normalized_data: true
    save_omop_tables: true
    save_validation_reports: true
    save_execution_summary: true
    save_medication_mappings: true

# Environment-specific overrides
# These can be overridden by environment variables
environment:
  # Database connections
  database_connections:
    timeout_seconds: 30
    pool_size: 5
    max_overflow: 10

  # API settings
  api_settings:
    request_timeout: 30
    max_retries: 3

  # Resource limits
  resources:
    max_memory_gb: 8
    max_cpu_cores: 4

# Curtis integration settings
curtis_integration:
  # Workflow integration
  workflow:
    output_format: "parquet"
    staging_directory: "staging"
    archive_directory: "archive"

  # Data warehouse integration
  data_warehouse:
    target_schema: "omop_cdm"
    staging_schema: "staging"
    load_strategy: "incremental"  # full or incremental

  # Notification settings
  notifications:
    enabled: false
    email_on_success: false
    email_on_failure: true
    webhook_url: "${WEBHOOK_URL}"

  # Quality gates
  quality_gates:
    min_success_rate: 0.95
    max_error_rate: 0.05
    require_validation_pass: true