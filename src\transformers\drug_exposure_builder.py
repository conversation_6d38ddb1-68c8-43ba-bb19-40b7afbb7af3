"""
DRUG_EXPOSURE table builder for OMOP CDM.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import hashlib

from ..utils.logger import LoggerMixin
from ..utils.constants import OMOP_STANDARD_CONCEPTS
from ..utils.exceptions import TransformationError


class DrugExposureBuilder(LoggerMixin):
    """
    Builds OMOP CDM DRUG_EXPOSURE table from normalized medication data.

    Transforms normalized medication records into OMOP-compliant
    drug exposure records with proper concept mapping.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize drug exposure builder.

        Args:
            config: OMOP transformation configuration
        """
        self.config = config
        self.drug_exposure_config = config.get('omop', {}).get('tables', {}).get('drug_exposure', {})

        # Standard concepts for OMOP
        self.standard_concepts = OMOP_STANDARD_CONCEPTS

        # Builder statistics
        self.builder_stats = {
            'input_records': 0,
            'output_records': 0,
            'skipped_records': 0,
            'error_records': 0,
            'concept_mapping_stats': {},
            'source_system_stats': {}
        }

    def build_drug_exposure_table(self, normalized_medications_df: pd.DataFrame) -> pd.DataFrame:
        """
        Build DRUG_EXPOSURE table from normalized medication data.

        Args:
            normalized_medications_df: Normalized medication data

        Returns:
            OMOP DRUG_EXPOSURE DataFrame
        """
        if normalized_medications_df.empty:
            self.logger.warning("Empty input data for DRUG_EXPOSURE table")
            return pd.DataFrame()

        self.builder_stats['input_records'] = len(normalized_medications_df)

        self.logger.info(f"Building DRUG_EXPOSURE table from {len(normalized_medications_df)} records")

        try:
            # Prepare the input data
            prepared_df = self._prepare_input_data(normalized_medications_df)

            # Build core DRUG_EXPOSURE records
            drug_exposure_records = []

            for idx, row in prepared_df.iterrows():
                try:
                    exposure_record = self._create_drug_exposure_record(row, idx + 1)
                    if exposure_record:
                        drug_exposure_records.append(exposure_record)
                    else:
                        self.builder_stats['skipped_records'] += 1

                except Exception as e:
                    self.logger.error(f"Failed to create drug exposure record for row {idx}: {str(e)}")
                    self.builder_stats['error_records'] += 1

            # Convert to DataFrame
            if drug_exposure_records:
                drug_exposure_df = pd.DataFrame(drug_exposure_records)

                # Apply final validation and cleanup
                drug_exposure_df = self._apply_final_validation(drug_exposure_df)

                # Update statistics
                self.builder_stats['output_records'] = len(drug_exposure_df)
                self._update_concept_mapping_stats(drug_exposure_df)
                self._update_source_system_stats(prepared_df)

                self.logger.info(f"Successfully built DRUG_EXPOSURE table: {len(drug_exposure_df)} records")

                return drug_exposure_df

            else:
                self.logger.warning("No valid drug exposure records created")
                return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"Failed to build DRUG_EXPOSURE table: {str(e)}")
            raise TransformationError(
                f"DRUG_EXPOSURE table construction failed: {str(e)}",
                omop_table="DRUG_EXPOSURE"
            )

    def _prepare_input_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare and validate input data for transformation."""
        prepared_df = df.copy()

        # Ensure required columns exist
        required_columns = ['patient_id', 'medication_text', 'source_system']
        missing_columns = [col for col in required_columns if col not in prepared_df.columns]

        if missing_columns:
            raise TransformationError(
                f"Missing required columns for DRUG_EXPOSURE: {missing_columns}",
                omop_table="DRUG_EXPOSURE"
            )

        # Filter out records without essential information
        before_count = len(prepared_df)

        # Remove records without patient ID
        prepared_df = prepared_df[prepared_df['patient_id'].notna()]

        # Remove records without medication information
        prepared_df = prepared_df[
            prepared_df['medication_text'].notna() &
            (prepared_df['medication_text'] != '')
        ]

        after_count = len(prepared_df)
        filtered_count = before_count - after_count

        if filtered_count > 0:
            self.logger.info(f"Filtered out {filtered_count} records missing essential information")

        return prepared_df

    def _create_drug_exposure_record(self, row: pd.Series, record_id: int) -> Optional[Dict[str, Any]]:
        """
        Create a single DRUG_EXPOSURE record from normalized medication data.

        Args:
            row: Normalized medication record
            record_id: Unique record identifier

        Returns:
            DRUG_EXPOSURE record dictionary or None if invalid
        """
        try:
            # Core required fields
            exposure_record = {
                'drug_exposure_id': record_id,
                'person_id': self._generate_person_id(row['patient_id'], row['source_system']),
                'drug_concept_id': self._get_drug_concept_id(row),
                'drug_exposure_start_date': self._get_start_date(row),
                'drug_type_concept_id': self._get_drug_type_concept_id(row)
            }

            # Optional fields
            exposure_record.update({
                'drug_exposure_end_date': self._get_end_date(row),
                'verbatim_end_date': None,  # Not typically available
                'dose_value': self._get_dose_value(row),
                'dose_unit_concept_id': self._get_dose_unit_concept_id(row),
                'quantity': self._get_quantity(row),
                'days_supply': self._get_days_supply(row),
                'sig': self._get_sig(row),
                'route_concept_id': self._get_route_concept_id(row),
                'lot_number': None,  # Typically not available
                'provider_id': self._get_provider_id(row),
                'visit_occurrence_id': self._get_visit_occurrence_id(row),
                'visit_detail_id': None,  # Typically not available
                'drug_source_value': self._get_drug_source_value(row),
                'drug_source_concept_id': self._get_drug_source_concept_id(row),
                'route_source_value': self._get_route_source_value(row),
                'dose_unit_source_value': self._get_dose_unit_source_value(row)
            })

            # Validate required fields
            if not self._validate_required_fields(exposure_record):
                return None

            return exposure_record

        except Exception as e:
            self.logger.error(f"Error creating drug exposure record: {str(e)}")
            return None

    def _generate_person_id(self, patient_id: str, source_system: str) -> int:
        """
        Generate a consistent person_id from patient_id and source_system.

        Args:
            patient_id: Source patient identifier
            source_system: Source system name

        Returns:
            Integer person_id for OMOP
        """
        # Create a consistent hash-based person_id
        combined_id = f"{source_system}_{patient_id}"
        hash_value = int(hashlib.md5(combined_id.encode()).hexdigest()[:8], 16)

        # Ensure positive integer within reasonable range
        return abs(hash_value) % **********  # Max 32-bit signed integer

    def _get_drug_concept_id(self, row: pd.Series) -> int:
        """Get drug concept ID from RxNorm mapping."""
        # Priority order: final_rxcui, reference_rxcui, rxcui
        rxcui_columns = ['final_rxcui', 'reference_rxcui', 'rxcui']

        for col in rxcui_columns:
            if col in row and pd.notna(row[col]):
                try:
                    rxcui = int(float(row[col]))
                    if rxcui > 0:
                        return rxcui
                except (ValueError, TypeError):
                    continue

        # If no valid RxCUI, return 0 (No matching concept)
        return 0

    def _get_start_date(self, row: pd.Series) -> Optional[pd.Timestamp]:
        """Get drug exposure start date."""
        date_columns = ['start_date', 'date_filled', 'effective_datetime', 'extraction_timestamp']

        for col in date_columns:
            if col in row and pd.notna(row[col]):
                try:
                    return pd.to_datetime(row[col])
                except (ValueError, TypeError):
                    continue

        # If no date available, use current date as fallback
        self.logger.warning(f"No start date found for record, using current date")
        return pd.Timestamp.now().normalize()

    def _get_end_date(self, row: pd.Series) -> Optional[pd.Timestamp]:
        """Get drug exposure end date."""
        # Check for explicit end date
        if 'end_date' in row and pd.notna(row['end_date']):
            try:
                return pd.to_datetime(row['end_date'])
            except (ValueError, TypeError):
                pass

        # Calculate end date from start date and days supply
        start_date = self._get_start_date(row)
        days_supply = self._get_days_supply(row)

        if start_date and days_supply and days_supply > 0:
            return start_date + pd.Timedelta(days=days_supply)

        # For single administration events (like AlloCare), use same as start date
        if row.get('source_system') == 'allocare' and 'effective_datetime' in row:
            return self._get_start_date(row)

        return None

    def _get_drug_type_concept_id(self, row: pd.Series) -> int:
        """Get drug type concept ID based on source and record type."""
        source_system = row.get('source_system', '').lower()
        record_type = row.get('record_type', '').lower()

        # Map based on source system and record type
        if source_system == 'pioneer_rx':
            if record_type == 'dispensed':
                return self.standard_concepts['drug_types']['prescription_dispensed']
            else:
                return self.standard_concepts['drug_types']['prescription_written']

        elif source_system == 'allocare':
            if 'administration' in record_type:
                return self.standard_concepts['drug_types']['medication_administered']
            else:
                return self.standard_concepts['drug_types']['patient_reported']

        elif 'map_pro' in source_system:
            # MAP Pro is primarily prescription data
            return self.standard_concepts['drug_types']['prescription_written']

        else:
            # Default to prescription written
            return self.standard_concepts['drug_types']['prescription_written']

    def _get_dose_value(self, row: pd.Series) -> Optional[float]:
        """Get dose value."""
        dose_columns = ['standard_dose_value', 'normalized_dose_value', 'dose_value']

        for col in dose_columns:
            if col in row and pd.notna(row[col]):
                try:
                    dose = float(row[col])
                    if 0 < dose <= 10000:  # Reasonable dose range
                        return dose
                except (ValueError, TypeError):
                    continue

        return None

    def _get_dose_unit_concept_id(self, row: pd.Series) -> Optional[int]:
        """Get dose unit concept ID."""
        unit_columns = ['standard_dose_unit', 'normalized_dose_unit', 'dose_unit']

        for col in unit_columns:
            if col in row and pd.notna(row[col]):
                unit = str(row[col]).lower().strip()

                # Map to standard OMOP unit concepts
                if unit in self.standard_concepts['units']:
                    return self.standard_concepts['units'][unit]

                # Handle common variations
                unit_mappings = {
                    'milligram': 'mg',
                    'milligrams': 'mg',
                    'microgram': 'mcg',
                    'micrograms': 'mcg',
                    'gram': 'g',
                    'grams': 'g',
                    'milliliter': 'ml',
                    'milliliters': 'ml',
                    'liter': 'l',
                    'liters': 'l',
                    'percentage': 'percent'
                }

                normalized_unit = unit_mappings.get(unit, unit)
                if normalized_unit in self.standard_concepts['units']:
                    return self.standard_concepts['units'][normalized_unit]

        return None

    def _get_quantity(self, row: pd.Series) -> Optional[float]:
        """Get medication quantity."""
        quantity_columns = ['quantity_dispensed', 'quantity_prescribed', 'quantity']

        for col in quantity_columns:
            if col in row and pd.notna(row[col]):
                try:
                    quantity = float(row[col])
                    if quantity > 0:
                        return quantity
                except (ValueError, TypeError):
                    continue

        return None

    def _get_days_supply(self, row: pd.Series) -> Optional[int]:
        """Get days supply."""
        if 'days_supply' in row and pd.notna(row['days_supply']):
            try:
                days = int(float(row['days_supply']))
                if 0 < days <= 365:  # Reasonable range
                    return days
            except (ValueError, TypeError):
                pass

        # Estimate from quantity and frequency if available
        quantity = self._get_quantity(row)
        frequency = row.get('normalized_frequency', '')

        if quantity and frequency:
            return self._estimate_days_supply_from_frequency(quantity, frequency)

        return None

    def _estimate_days_supply_from_frequency(self, quantity: float, frequency: str) -> Optional[int]:
        """Estimate days supply from quantity and frequency."""
        if not frequency:
            return None

        frequency_lower = frequency.lower()

        # Extract daily dose count from frequency
        daily_doses = 1  # Default

        if 'twice' in frequency_lower or 'bid' in frequency_lower:
            daily_doses = 2
        elif 'three times' in frequency_lower or 'tid' in frequency_lower:
            daily_doses = 3
        elif 'four times' in frequency_lower or 'qid' in frequency_lower:
            daily_doses = 4
        elif 'once' in frequency_lower or 'daily' in frequency_lower:
            daily_doses = 1

        if daily_doses > 0:
            estimated_days = int(quantity / daily_doses)
            return max(1, min(estimated_days, 365))  # Cap at reasonable range

        return None

    def _get_sig(self, row: pd.Series) -> Optional[str]:
        """Get medication instructions (sig)."""
        sig_components = []

        # Add dose information
        dose_value = self._get_dose_value(row)
        dose_unit = row.get('normalized_dose_unit', '')
        if dose_value and dose_unit:
            sig_components.append(f"{dose_value} {dose_unit}")

        # Add frequency
        frequency = row.get('normalized_frequency', '')
        if frequency:
            sig_components.append(frequency)

        # Add route
        route = row.get('normalized_route', '')
        if route:
            sig_components.append(route)

        return ', '.join(sig_components) if sig_components else None

    def _get_route_concept_id(self, row: pd.Series) -> Optional[int]:
        """Get route concept ID."""
        route = row.get('normalized_route', '')
        if not route:
            route = row.get('route', '')

        if route:
            route_lower = route.lower().strip()

            # Direct mapping to standard concepts
            if route_lower in self.standard_concepts['routes']:
                return self.standard_concepts['routes'][route_lower]

            # Common route mappings
            route_mappings = {
                'by mouth': 'oral',
                'po': 'oral',
                'mouth': 'oral',
                'iv': 'intravenous',
                'i.v.': 'intravenous',
                'im': 'intramuscular',
                'i.m.': 'intramuscular',
                'sc': 'subcutaneous',
                'sq': 'subcutaneous',
                'subcut': 'subcutaneous'
            }

            normalized_route = route_mappings.get(route_lower, route_lower)
            if normalized_route in self.standard_concepts['routes']:
                return self.standard_concepts['routes'][normalized_route]

        return None

    def _get_provider_id(self, row: pd.Series) -> Optional[int]:
        """Get provider ID if available."""
        if 'prescriber_id' in row and pd.notna(row['prescriber_id']):
            try:
                return int(float(row['prescriber_id']))
            except (ValueError, TypeError):
                pass

        return None

    def _get_visit_occurrence_id(self, row: pd.Series) -> Optional[int]:
        """Get visit occurrence ID if available."""
        # This would typically come from linked visit data
        # For now, return None as visit linking is beyond scope
        return None

    def _get_drug_source_value(self, row: pd.Series) -> str:
        """Get original drug source value."""
        return str(row.get('medication_text', ''))

    def _get_drug_source_concept_id(self, row: pd.Series) -> Optional[int]:
        """Get drug source concept ID."""
        # Map source codes (NDC, ItemID, etc.) to concept IDs
        source_code = row.get('medication_code')
        if source_code and pd.notna(source_code):
            # For now, return 0 (no mapping concept)
            # In production, this would map to source vocabulary concepts
            return 0

        return None

    def _get_route_source_value(self, row: pd.Series) -> Optional[str]:
        """Get original route source value."""
        return row.get('route') if pd.notna(row.get('route')) else None

    def _get_dose_unit_source_value(self, row: pd.Series) -> Optional[str]:
        """Get original dose unit source value."""
        return row.get('dose_unit') if pd.notna(row.get('dose_unit')) else None

    def _validate_required_fields(self, record: Dict[str, Any]) -> bool:
        """Validate that required OMOP fields are present and valid."""
        required_fields = {
            'drug_exposure_id': lambda x: x is not None and x > 0,
            'person_id': lambda x: x is not None and x > 0,
            'drug_concept_id': lambda x: x is not None and x >= 0,  # 0 is valid (no mapping)
            'drug_exposure_start_date': lambda x: x is not None,
            'drug_type_concept_id': lambda x: x is not None and x > 0
        }

        for field, validator in required_fields.items():
            if not validator(record.get(field)):
                self.logger.warning(f"Invalid required field {field}: {record.get(field)}")
                return False

        return True

    def _apply_final_validation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply final validation and cleanup to DRUG_EXPOSURE table."""
        if df.empty:
            return df

        initial_count = len(df)

        # Remove records with invalid dates
        df['drug_exposure_start_date'] = pd.to_datetime(df['drug_exposure_start_date'], errors='coerce')
        df = df[df['drug_exposure_start_date'].notna()]

        # Remove records with invalid person_id
        df = df[df['person_id'] > 0]

        # Ensure drug_exposure_id is unique
        df = df.drop_duplicates(subset=['drug_exposure_id'])

        # Sort by person_id and start_date
        df = df.sort_values(['person_id', 'drug_exposure_start_date'])

        final_count = len(df)
        if final_count < initial_count:
            self.logger.info(f"Final validation removed {initial_count - final_count} invalid records")

        return df

    def _update_concept_mapping_stats(self, df: pd.DataFrame) -> None:
        """Update concept mapping statistics."""
        if df.empty:
            return

        # Drug concept mapping stats
        mapped_drugs = (df['drug_concept_id'] > 0).sum()
        unmapped_drugs = (df['drug_concept_id'] == 0).sum()

        self.builder_stats['concept_mapping_stats'] = {
            'drug_concepts': {
                'mapped': mapped_drugs,
                'unmapped': unmapped_drugs,
                'mapping_rate': mapped_drugs / len(df) if len(df) > 0 else 0
            },
            'dose_units': {
                'mapped': df['dose_unit_concept_id'].notna().sum(),
                'total_with_dose': df['dose_value'].notna().sum()
            },
            'routes': {
                'mapped': df['route_concept_id'].notna().sum(),
                'total_with_route': df['route_source_value'].notna().sum()
            }
        }

    def _update_source_system_stats(self, input_df: pd.DataFrame) -> None:
        """Update source system statistics."""
        if input_df.empty:
            return

        source_counts = input_df['source_system'].value_counts().to_dict()
        self.builder_stats['source_system_stats'] = source_counts

    def get_builder_statistics(self) -> Dict[str, Any]:
        """Get comprehensive builder statistics."""
        return {
            'processing_stats': {
                'input_records': self.builder_stats['input_records'],
                'output_records': self.builder_stats['output_records'],
                'skipped_records': self.builder_stats['skipped_records'],
                'error_records': self.builder_stats['error_records'],
                'success_rate': (
                    self.builder_stats['output_records'] / self.builder_stats['input_records']
                    if self.builder_stats['input_records'] > 0 else 0
                )
            },
            'concept_mapping_stats': self.builder_stats.get('concept_mapping_stats', {}),
            'source_system_stats': self.builder_stats.get('source_system_stats', {}),
            'configuration': {
                'standard_concepts_used': len(self.standard_concepts),
                'validation_enabled': True
            }
        }