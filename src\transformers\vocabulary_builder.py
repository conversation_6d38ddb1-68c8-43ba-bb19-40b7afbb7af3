"""
OMOP vocabulary table builder.

Creates vocabulary and concept mapping tables for OMOP CDM compliance.
Generates CONCEP<PERSON>, CONCEPT_RELATIONSHIP, and other vocabulary tables.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime

from ..utils.logger import LoggerMixin
from ..utils.exceptions import TransformationError
from ..utils.constants import OMOP_STANDARD_CONCEPTS


class VocabularyBuilder(LoggerMixin):
    """
    Builder for OMOP CDM vocabulary tables.

    Creates vocabulary tables including CONCEPT, CONCEPT_RELATIONSHIP,
    and other vocabulary-related tables for proper OMOP compliance.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize vocabulary builder.

        Args:
            config: Configuration dictionary containing vocabulary settings
        """
        self.config = config
        self.vocab_config = config.get('omop', {}).get('vocabulary', {})

        # Vocabulary settings
        self.include_standard_concepts = self.vocab_config.get('include_standard_concepts', True)
        self.include_source_concepts = self.vocab_config.get('include_source_concepts', True)
        self.custom_vocabulary_id = self.vocab_config.get('custom_vocabulary_id', 'Custom')

        # Builder statistics
        self.builder_stats = {
            'start_time': None,
            'end_time': None,
            'input_records': 0,
            'concept_records': 0,
            'concept_relationship_records': 0,
            'vocabulary_records': 0,
            'concept_synonym_records': 0,
            'processing_errors': 0
        }

        # Track concepts and relationships
        self.concepts = {}
        self.relationships = []
        self.vocabularies = {}
        self.concept_synonyms = []

    def build_vocabulary_tables(
        self,
        normalized_medications_df: pd.DataFrame,
        drug_exposure_df: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """
        Build vocabulary tables from normalized medication data.

        Args:
            normalized_medications_df: Normalized medication data
            drug_exposure_df: Generated DRUG_EXPOSURE table

        Returns:
            Dictionary of vocabulary tables
        """
        self.builder_stats['start_time'] = datetime.now()
        self.builder_stats['input_records'] = len(normalized_medications_df)

        self.logger.info("Building OMOP vocabulary tables")

        try:
            # Clear previous data
            self.concepts = {}
            self.relationships = []
            self.vocabularies = {}
            self.concept_synonyms = []

            # Extract concepts from normalized data
            self._extract_standard_concepts(drug_exposure_df)
            self._extract_source_concepts(normalized_medications_df)
            self._extract_custom_concepts(normalized_medications_df)

            # Build relationships
            self._build_concept_relationships(normalized_medications_df)

            # Create vocabulary entries
            self._create_vocabulary_records()

            # Generate final tables
            vocabulary_tables = self._generate_vocabulary_tables()

            # Update statistics
            self._update_builder_statistics(vocabulary_tables)

            self.logger.info(f"Generated {len(vocabulary_tables)} vocabulary tables")
            return vocabulary_tables

        except Exception as e:
            self.builder_stats['end_time'] = datetime.now()
            self.logger.error(f"Vocabulary building failed: {str(e)}")
            raise TransformationError(
                f"Failed to build vocabulary tables: {str(e)}",
                omop_table="VOCABULARY"
            )

    def _extract_standard_concepts(self, drug_exposure_df: pd.DataFrame) -> None:
        """Extract standard OMOP concepts from drug exposure data."""
        if not self.include_standard_concepts or drug_exposure_df.empty:
            return

        # Extract drug concepts
        drug_concepts = drug_exposure_df['drug_concept_id'].dropna().unique()
        for concept_id in drug_concepts:
            if concept_id > 0:  # Valid concept ID
                self._add_concept(
                    concept_id=int(concept_id),
                    concept_name=f"RxNorm Concept {concept_id}",
                    domain_id="Drug",
                    vocabulary_id="RxNorm",
                    concept_class_id="Clinical Drug",
                    standard_concept="S",
                    concept_code=str(concept_id)
                )

        # Extract unit concepts
        if 'dose_unit_concept_id' in drug_exposure_df.columns:
            unit_concepts = drug_exposure_df['dose_unit_concept_id'].dropna().unique()
            for concept_id in unit_concepts:
                if concept_id > 0:
                    self._add_concept(
                        concept_id=int(concept_id),
                        concept_name=f"UCUM Unit {concept_id}",
                        domain_id="Unit",
                        vocabulary_id="UCUM",
                        concept_class_id="Unit",
                        standard_concept="S",
                        concept_code=str(concept_id)
                    )

        # Extract route concepts
        if 'route_concept_id' in drug_exposure_df.columns:
            route_concepts = drug_exposure_df['route_concept_id'].dropna().unique()
            for concept_id in route_concepts:
                if concept_id > 0:
                    self._add_concept(
                        concept_id=int(concept_id),
                        concept_name=f"SNOMED Route {concept_id}",
                        domain_id="Route",
                        vocabulary_id="SNOMED",
                        concept_class_id="Qualifier Value",
                        standard_concept="S",
                        concept_code=str(concept_id)
                    )

        # Add standard type concepts
        self._add_standard_type_concepts()

    def _extract_source_concepts(self, normalized_medications_df: pd.DataFrame) -> None:
        """Extract source-specific concepts from normalized data."""
        if not self.include_source_concepts:
            return

        # Extract source medication names
        source_columns = ['medication_name', 'brand_name', 'generic_name']
        concept_id_counter = 2000000000  # Start with high ID for source concepts

        for column in source_columns:
            if column in normalized_medications_df.columns:
                unique_names = normalized_medications_df[column].dropna().unique()

                for name in unique_names:
                    if name and isinstance(name, str) and len(name.strip()) > 0:
                        self._add_concept(
                            concept_id=concept_id_counter,
                            concept_name=name.strip(),
                            domain_id="Drug",
                            vocabulary_id=self.custom_vocabulary_id,
                            concept_class_id="Ingredient",
                            standard_concept=None,
                            concept_code=f"SRC_{concept_id_counter}"
                        )
                        concept_id_counter += 1

    def _extract_custom_concepts(self, normalized_medications_df: pd.DataFrame) -> None:
        """Extract custom concepts for transplant medications."""
        # Add transplant-specific concept classifications
        transplant_drugs = {
            'Immunosuppressive': ['tacrolimus', 'cyclosporine', 'sirolimus', 'everolimus'],
            'Anti-rejection': ['mycophenolate', 'azathioprine', 'alemtuzumab'],
            'Corticosteroid': ['prednisone', 'prednisolone', 'methylprednisolone']
        }

        concept_id_counter = 3000000000

        for drug_class, drug_list in transplant_drugs.items():
            for drug in drug_list:
                self._add_concept(
                    concept_id=concept_id_counter,
                    concept_name=f"{drug.title()} ({drug_class})",
                    domain_id="Drug",
                    vocabulary_id="Transplant",
                    concept_class_id=drug_class,
                    standard_concept=None,
                    concept_code=f"TX_{drug.upper()}"
                )
                concept_id_counter += 1

    def _build_concept_relationships(self, normalized_medications_df: pd.DataFrame) -> None:
        """Build concept relationships between source and standard concepts."""
        # Map source medications to RxNorm concepts
        rxnorm_mappings = normalized_medications_df[
            ['medication_name', 'final_rxcui', 'rxcui', 'reference_rxcui']
        ].dropna(subset=['medication_name'])

        for _, row in rxnorm_mappings.iterrows():
            medication_name = row['medication_name']

            # Find source concept ID for this medication name
            source_concept_id = self._find_source_concept_id(medication_name)

            # Find target RxCUI
            target_rxcui = row['final_rxcui'] or row['rxcui'] or row['reference_rxcui']

            if source_concept_id and target_rxcui:
                self._add_relationship(
                    concept_id_1=source_concept_id,
                    concept_id_2=int(target_rxcui),
                    relationship_id="Maps to"
                )

    def _add_concept(
        self,
        concept_id: int,
        concept_name: str,
        domain_id: str,
        vocabulary_id: str,
        concept_class_id: str,
        standard_concept: Optional[str],
        concept_code: str,
        valid_start_date: str = "1970-01-01",
        valid_end_date: str = "2099-12-31",
        invalid_reason: Optional[str] = None
    ) -> None:
        """Add a concept to the concepts dictionary."""
        if concept_id not in self.concepts:
            self.concepts[concept_id] = {
                'concept_id': concept_id,
                'concept_name': concept_name,
                'domain_id': domain_id,
                'vocabulary_id': vocabulary_id,
                'concept_class_id': concept_class_id,
                'standard_concept': standard_concept,
                'concept_code': concept_code,
                'valid_start_date': valid_start_date,
                'valid_end_date': valid_end_date,
                'invalid_reason': invalid_reason
            }

    def _add_relationship(
        self,
        concept_id_1: int,
        concept_id_2: int,
        relationship_id: str,
        valid_start_date: str = "1970-01-01",
        valid_end_date: str = "2099-12-31",
        invalid_reason: Optional[str] = None
    ) -> None:
        """Add a concept relationship."""
        self.relationships.append({
            'concept_id_1': concept_id_1,
            'concept_id_2': concept_id_2,
            'relationship_id': relationship_id,
            'valid_start_date': valid_start_date,
            'valid_end_date': valid_end_date,
            'invalid_reason': invalid_reason
        })

    def _find_source_concept_id(self, medication_name: str) -> Optional[int]:
        """Find concept ID for a source medication name."""
        for concept_id, concept in self.concepts.items():
            if (concept['vocabulary_id'] == self.custom_vocabulary_id and
                concept['concept_name'].lower() == medication_name.lower()):
                return concept_id
        return None

    def _add_standard_type_concepts(self) -> None:
        """Add standard OMOP type concepts."""
        standard_types = OMOP_STANDARD_CONCEPTS.get('drug_type_concepts', {})

        for type_name, concept_id in standard_types.items():
            self._add_concept(
                concept_id=concept_id,
                concept_name=type_name,
                domain_id="Type Concept",
                vocabulary_id="Type Concept",
                concept_class_id="Type Concept",
                standard_concept="S",
                concept_code=str(concept_id)
            )

    def _create_vocabulary_records(self) -> None:
        """Create vocabulary records for all used vocabularies."""
        vocabulary_info = {
            'RxNorm': {
                'vocabulary_name': 'RxNorm',
                'vocabulary_reference': 'UMLS Metathesaurus RxNorm',
                'vocabulary_version': 'v2024',
                'vocabulary_concept_id': 8515
            },
            'UCUM': {
                'vocabulary_name': 'Unified Code for Units of Measure',
                'vocabulary_reference': 'UCUM',
                'vocabulary_version': 'v2024',
                'vocabulary_concept_id': 8587
            },
            'SNOMED': {
                'vocabulary_name': 'SNOMED Clinical Terms',
                'vocabulary_reference': 'IHTSDO SNOMED CT',
                'vocabulary_version': 'v2024',
                'vocabulary_concept_id': 8901
            },
            self.custom_vocabulary_id: {
                'vocabulary_name': 'Custom Source Vocabulary',
                'vocabulary_reference': 'Institution-specific medication names',
                'vocabulary_version': 'v1.0',
                'vocabulary_concept_id': 0
            },
            'Transplant': {
                'vocabulary_name': 'Transplant Medication Classification',
                'vocabulary_reference': 'Transplant-specific drug classes',
                'vocabulary_version': 'v1.0',
                'vocabulary_concept_id': 0
            },
            'Type Concept': {
                'vocabulary_name': 'OMOP Type Concepts',
                'vocabulary_reference': 'OMOP CDM Type Concepts',
                'vocabulary_version': 'v6.0',
                'vocabulary_concept_id': 44819
            }
        }

        # Find which vocabularies are actually used
        used_vocabularies = set()
        for concept in self.concepts.values():
            used_vocabularies.add(concept['vocabulary_id'])

        # Create vocabulary records for used vocabularies
        for vocab_id in used_vocabularies:
            if vocab_id in vocabulary_info:
                info = vocabulary_info[vocab_id]
                self.vocabularies[vocab_id] = {
                    'vocabulary_id': vocab_id,
                    'vocabulary_name': info['vocabulary_name'],
                    'vocabulary_reference': info['vocabulary_reference'],
                    'vocabulary_version': info['vocabulary_version'],
                    'vocabulary_concept_id': info['vocabulary_concept_id']
                }

    def _generate_vocabulary_tables(self) -> Dict[str, pd.DataFrame]:
        """Generate final vocabulary tables."""
        tables = {}

        # CONCEPT table
        if self.concepts:
            concept_df = pd.DataFrame(list(self.concepts.values()))
            tables['CONCEPT'] = concept_df

        # CONCEPT_RELATIONSHIP table
        if self.relationships:
            relationship_df = pd.DataFrame(self.relationships)
            tables['CONCEPT_RELATIONSHIP'] = relationship_df

        # VOCABULARY table
        if self.vocabularies:
            vocabulary_df = pd.DataFrame(list(self.vocabularies.values()))
            tables['VOCABULARY'] = vocabulary_df

        # CONCEPT_SYNONYM table (if any synonyms were created)
        if self.concept_synonyms:
            synonym_df = pd.DataFrame(self.concept_synonyms)
            tables['CONCEPT_SYNONYM'] = synonym_df

        return tables

    def _update_builder_statistics(self, vocabulary_tables: Dict[str, pd.DataFrame]) -> None:
        """Update builder statistics."""
        self.builder_stats['end_time'] = datetime.now()

        self.builder_stats['concept_records'] = len(vocabulary_tables.get('CONCEPT', []))
        self.builder_stats['concept_relationship_records'] = len(vocabulary_tables.get('CONCEPT_RELATIONSHIP', []))
        self.builder_stats['vocabulary_records'] = len(vocabulary_tables.get('VOCABULARY', []))
        self.builder_stats['concept_synonym_records'] = len(vocabulary_tables.get('CONCEPT_SYNONYM', []))

    def get_builder_statistics(self) -> Dict[str, Any]:
        """Get comprehensive builder statistics."""
        stats = self.builder_stats.copy()

        if stats['start_time'] and stats['end_time']:
            execution_time = (stats['end_time'] - stats['start_time']).total_seconds()
            stats['execution_time_seconds'] = execution_time

        # Add concept distribution
        if self.concepts:
            vocab_distribution = {}
            domain_distribution = {}

            for concept in self.concepts.values():
                vocab_id = concept['vocabulary_id']
                domain_id = concept['domain_id']

                vocab_distribution[vocab_id] = vocab_distribution.get(vocab_id, 0) + 1
                domain_distribution[domain_id] = domain_distribution.get(domain_id, 0) + 1

            stats['vocabulary_distribution'] = vocab_distribution
            stats['domain_distribution'] = domain_distribution

        return stats

    def validate_vocabulary_output(self, vocabulary_tables: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Validate generated vocabulary tables.

        Args:
            vocabulary_tables: Generated vocabulary tables

        Returns:
            Validation results
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }

        # Validate CONCEPT table
        if 'CONCEPT' in vocabulary_tables:
            concept_df = vocabulary_tables['CONCEPT']

            required_concept_columns = [
                'concept_id', 'concept_name', 'domain_id',
                'vocabulary_id', 'concept_class_id', 'concept_code'
            ]

            for col in required_concept_columns:
                if col not in concept_df.columns:
                    validation_results['errors'].append(f"Missing required CONCEPT column: {col}")
                    validation_results['is_valid'] = False
                elif concept_df[col].isnull().any():
                    null_count = concept_df[col].isnull().sum()
                    validation_results['errors'].append(f"Null values in CONCEPT.{col}: {null_count}")
                    validation_results['is_valid'] = False

            # Check for duplicate concept IDs
            duplicate_ids = concept_df['concept_id'].duplicated().sum()
            if duplicate_ids > 0:
                validation_results['errors'].append(f"Duplicate concept IDs: {duplicate_ids}")
                validation_results['is_valid'] = False

        # Validate CONCEPT_RELATIONSHIP table
        if 'CONCEPT_RELATIONSHIP' in vocabulary_tables:
            relationship_df = vocabulary_tables['CONCEPT_RELATIONSHIP']

            required_rel_columns = ['concept_id_1', 'concept_id_2', 'relationship_id']

            for col in required_rel_columns:
                if col not in relationship_df.columns:
                    validation_results['errors'].append(f"Missing required CONCEPT_RELATIONSHIP column: {col}")
                    validation_results['is_valid'] = False

        # Statistics
        validation_results['statistics'] = {
            'total_concepts': len(vocabulary_tables.get('CONCEPT', [])),
            'total_relationships': len(vocabulary_tables.get('CONCEPT_RELATIONSHIP', [])),
            'total_vocabularies': len(vocabulary_tables.get('VOCABULARY', [])),
            'total_synonyms': len(vocabulary_tables.get('CONCEPT_SYNONYM', []))
        }

        return validation_results

    def export_vocabulary_mappings(
        self,
        normalized_medications_df: pd.DataFrame,
        output_path: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Export medication name to concept ID mappings.

        Args:
            normalized_medications_df: Normalized medication data
            output_path: Path to save mapping file

        Returns:
            Mapping DataFrame
        """
        mappings = []

        for _, row in normalized_medications_df.iterrows():
            if pd.notna(row.get('medication_name')):
                mapping = {
                    'source_medication_name': row['medication_name'],
                    'source_system': row.get('source_system', 'Unknown'),
                    'rxcui': row.get('final_rxcui') or row.get('rxcui'),
                    'rxnorm_name': row.get('rxnorm_name'),
                    'mapping_confidence': row.get('mapping_confidence'),
                    'mapping_method': row.get('mapping_method')
                }
                mappings.append(mapping)

        mapping_df = pd.DataFrame(mappings)

        if output_path:
            mapping_df.to_csv(output_path, index=False)
            self.logger.info(f"Exported medication mappings to {output_path}")

        return mapping_df