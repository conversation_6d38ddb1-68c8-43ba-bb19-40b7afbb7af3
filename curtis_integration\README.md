# Curtis Integration Package

This package contains ready-to-use scripts for integrating the medication normalization pipeline into <PERSON>'s data warehouse workflow.

## Quick Start

### 1. Basic Pipeline Execution
```bash
# Run full pipeline for all sources
python ../medication_normalization_pipeline.py \
  --config ../config/pipeline_config.yaml \
  --output-dir ./output/

# Run for specific sources
python ../medication_normalization_pipeline.py \
  --config ../config/pipeline_config.yaml \
  --sources map_pro pioneer_rx \
  --output-dir ./output/
```

### 2. Workflow Integration Scripts

#### Daily ETL Workflow
```bash
# Run daily medication normalization
./daily_medication_etl.sh
```

#### Incremental Processing
```bash
# Process only new/changed records
python incremental_processor.py --since yesterday
```

#### Data Quality Monitoring
```bash
# Generate quality report
python quality_monitor.py --output-dir ./output/
```

## Integration Points

### 1. Input Data Sources
- **MAP Pro Web**: Database connection via `map_pro_web` config
- **MAP Pro Mobile**: Database connection via `map_pro_mobile` config
- **PioneerRx**: Database connection via `pioneer_rx` config
- **AlloCare**: FHIR API or database via `allocare` config

### 2. Output OMOP Tables
Generated in `output/` directory:
- `DRUG_EXPOSURE.parquet` - Primary medication records
- `DRUG_ERA.parquet` - Medication treatment periods
- `DOSE_ERA.parquet` - Dose-specific treatment periods
- `CONCEPT.parquet` - Vocabulary concepts
- `CONCEPT_RELATIONSHIP.parquet` - Concept mappings
- `VOCABULARY.parquet` - Vocabulary metadata

### 3. Data Warehouse Loading
Use `data_warehouse_loader.py` to load OMOP tables into your target schema:

```bash
python data_warehouse_loader.py \
  --input-dir ./output/ \
  --target-schema omop_cdm \
  --load-strategy incremental
```

## Configuration

### Environment Variables
Set these in your environment or `.env` file:

```bash
# Database connections
MAP_PRO_WEB_CONNECTION_STRING="postgresql://user:pass@host:port/db"
MAP_PRO_MOBILE_CONNECTION_STRING="postgresql://user:pass@host:port/db"
PIONEER_RX_CONNECTION_STRING="postgresql://user:pass@host:port/db"
ALLOCARE_CONNECTION_STRING="postgresql://user:pass@host:port/db"

# RxNorm API
RXNORM_API_KEY="your_api_key_if_required"

# Security
PERSON_ID_SALT="your_secure_salt_string"

# Notifications
WEBHOOK_URL="https://your-webhook-endpoint.com"
```

### Pipeline Configuration
Main configuration in `../config/pipeline_config.yaml`:

- **Sources**: Enable/disable data sources
- **Processing**: Batch sizes, parallelization
- **Validation**: Quality thresholds, business rules
- **Output**: File formats, directory structure

## Workflow Scripts

### 1. `daily_medication_etl.sh`
Complete daily ETL workflow:
- Extracts from all sources
- Normalizes using RxNorm
- Transforms to OMOP CDM
- Validates output quality
- Loads to data warehouse
- Generates quality reports

### 2. `incremental_processor.py`
Processes only new/changed records:
- Identifies changes since last run
- Processes delta records
- Merges with existing OMOP tables
- Updates data warehouse incrementally

### 3. `quality_monitor.py`
Monitors data quality:
- Validates OMOP compliance
- Checks mapping coverage
- Generates quality metrics
- Sends alerts on issues

### 4. `data_warehouse_loader.py`
Loads OMOP tables to data warehouse:
- Supports full and incremental loads
- Handles schema validation
- Manages data type conversions
- Provides load statistics

### 5. `validation_runner.py`
Standalone validation utility:
- Validates existing OMOP output
- Generates detailed quality reports
- Checks compliance with OMOP CDM v6.0
- Produces actionable recommendations

## Error Handling

### Retry Logic
Scripts automatically retry on transient failures:
- Database connection timeouts
- RxNorm API rate limits
- Network interruptions

### Error Reporting
Failures are logged and optionally reported via:
- Log files in `logs/` directory
- Webhook notifications
- Email alerts (if configured)

### Recovery Procedures
For partial failures:
1. Check logs for specific error details
2. Fix configuration or data issues
3. Resume from last successful checkpoint
4. Use `--validate-only` to verify fixes

## Performance Optimization

### Batch Processing
- Default batch size: 1000 records
- Adjustable via `pipeline.processing.batch_size`
- Monitor memory usage with large batches

### Parallel Processing
- Default workers: 4
- Adjustable via `pipeline.processing.max_workers`
- Scale based on available CPU cores

### Caching
- RxNorm API responses cached for 24 hours
- Reference mappings cached between runs
- Clear cache with `--clear-cache` flag

## Monitoring and Alerts

### Quality Metrics
Tracked automatically:
- Mapping coverage percentages
- Validation pass/fail rates
- Processing times and throughput
- Error counts by component

### Thresholds
Alert when metrics exceed thresholds:
- Mapping coverage < 80%
- Validation failure rate > 5%
- Processing time > 1 hour
- Error count > 10

### Dashboards
Consider integrating with monitoring tools:
- Grafana for metrics visualization
- Splunk for log analysis
- Datadog for infrastructure monitoring

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   - Verify connection strings
   - Check network connectivity
   - Validate credentials

2. **RxNorm API Errors**
   - Check API key configuration
   - Verify rate limit compliance
   - Review network proxy settings

3. **Validation Failures**
   - Review validation reports
   - Check data quality issues
   - Verify OMOP compliance

4. **Performance Issues**
   - Monitor resource usage
   - Adjust batch sizes
   - Check database query performance

### Support
For issues specific to your environment:
1. Check log files in `logs/` directory
2. Review configuration settings
3. Run validation diagnostics
4. Contact development team with details

## Customization

### Adding New Sources
To integrate additional data sources:
1. Create new extractor in `src/extractors/`
2. Add configuration to `config/source_configs.yaml`
3. Update pipeline configuration
4. Test with new source data

### Custom Validation Rules
To add business-specific validation:
1. Extend validators in `src/validators/`
2. Add rules to `config/pipeline_config.yaml`
3. Update quality thresholds
4. Test with sample data

### Integration with Existing ETL
To integrate with existing workflows:
1. Use provided Python modules directly
2. Call pipeline scripts from existing orchestrators
3. Consume OMOP output files
4. Adapt configuration as needed