"""
Advanced medication matching using existing mappings and fuzzy string matching.
"""

import pandas as pd
import re
from typing import Dict, Any, List, Optional, Tuple
from difflib import SequenceMatcher
import string

try:
    from rapidfuzz import fuzz, process
    HAS_RAPIDFUZZ = True
except ImportError:
    try:
        from fuzzywuzzy import fuzz, process
        HAS_RAPIDFUZZ = False
    except ImportError:
        fuzz = None
        process = None
        HAS_RAPIDFUZZ = False

from ..utils.logger import LoggerMixin
from ..utils.constants import IMMUNOSUPPRESSIVE_MEDICATIONS, MEDICATION_ROUTES


class MedicationMatcher(LoggerMixin):
    """
    Advanced medication matching using multiple strategies.

    Leverages existing RxNorm mappings from MAP Pro and provides
    fuzzy string matching for unmapped medications.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize medication matcher.

        Args:
            config: Fuzzy matching configuration
        """
        self.config = config
        self.fuzzy_config = config.get('fuzzy_matching', {})

        self.min_similarity_score = self.fuzzy_config.get('min_similarity_score', 0.8)
        self.max_edit_distance = self.fuzzy_config.get('max_edit_distance', 2)
        self.use_phonetic_matching = self.fuzzy_config.get('use_phonetic_matching', True)

        # Reference mappings from MAP Pro
        self.reference_mappings = pd.DataFrame()

        # Preprocessed medication lists for fuzzy matching
        self.medication_variants = {}

        if not HAS_RAPIDFUZZ and fuzz is None:
            self.logger.warning(
                "No fuzzy matching library available. Install rapidfuzz or fuzzywuzzy: "
                "pip install rapidfuzz"
            )

    def load_reference_mappings(self, map_pro_mappings: pd.DataFrame) -> None:
        """
        Load reference mappings from MAP Pro.

        Args:
            map_pro_mappings: DataFrame with MAP Pro RxNorm mappings
        """
        self.reference_mappings = map_pro_mappings.copy()

        # Build medication variants lookup
        self._build_medication_variants()

        self.logger.info(
            f"Loaded {len(self.reference_mappings)} reference mappings from MAP Pro"
        )

    def _build_medication_variants(self) -> None:
        """Build lookup table of medication name variants."""
        if self.reference_mappings.empty:
            return

        self.medication_variants = {}

        for _, row in self.reference_mappings.iterrows():
            rxcui = row.get('rxcui')
            if not rxcui:
                continue

            # Collect all name variants for this RxCUI
            variants = set()

            # Generic name
            if pd.notna(row.get('generic_name')):
                variants.add(self._normalize_medication_name(row['generic_name']))

            # Brand name
            if pd.notna(row.get('brand_name')):
                variants.add(self._normalize_medication_name(row['brand_name']))

            # RxNorm name
            if pd.notna(row.get('rxnorm_name')):
                variants.add(self._normalize_medication_name(row['rxnorm_name']))

            # Medication texts (may be semicolon-separated)
            if pd.notna(row.get('medication_texts')):
                for text in str(row['medication_texts']).split(';'):
                    if text.strip():
                        variants.add(self._normalize_medication_name(text.strip()))

            # Store variants for this RxCUI
            for variant in variants:
                if variant and len(variant) > 2:  # Skip very short variants
                    if variant not in self.medication_variants:
                        self.medication_variants[variant] = []

                    self.medication_variants[variant].append({
                        'rxcui': rxcui,
                        'generic_name': row.get('generic_name'),
                        'brand_name': row.get('brand_name'),
                        'rxnorm_name': row.get('rxnorm_name'),
                        'drug_class': row.get('drug_class'),
                        'usage_count': row.get('usage_count', 0)
                    })

        self.logger.info(f"Built {len(self.medication_variants)} medication variants for matching")

    def _normalize_medication_name(self, name: str) -> str:
        """
        Normalize medication name for consistent matching.

        Args:
            name: Raw medication name

        Returns:
            Normalized name
        """
        if not name or pd.isna(name):
            return ""

        # Convert to lowercase
        normalized = str(name).lower().strip()

        # Remove common dosage information
        dosage_patterns = [
            r'\b\d+\s*(mg|mcg|g|ml|cc|units?|iu|miu)\b',  # Dose amounts
            r'\b\d+\s*%\b',  # Percentages
            r'\b\d+\s*(mg|mcg|g)/\s*\d+\s*(mg|mcg|g|ml)\b',  # Ratios
            r'\b\d+\s*x\s*\d+\b',  # Multiplications
        ]

        for pattern in dosage_patterns:
            normalized = re.sub(pattern, '', normalized)

        # Remove common form information
        form_patterns = [
            r'\b(tab|tablet|cap|capsule|injection|solution|cream|ointment|gel)\b',
            r'\b(oral|iv|im|sc|po|intravenous|intramuscular|subcutaneous)\b',
            r'\b(er|xr|xl|sr|cr|extended|release|sustained|controlled)\b'
        ]

        for pattern in form_patterns:
            normalized = re.sub(pattern, '', normalized)

        # Remove special characters except hyphens
        normalized = re.sub(r'[^\w\s\-]', ' ', normalized)

        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        return normalized

    def find_exact_match(self, medication_text: str) -> Optional[Dict[str, Any]]:
        """
        Find exact match in reference mappings.

        Args:
            medication_text: Medication text to match

        Returns:
            Matching reference entry or None
        """
        if self.reference_mappings.empty:
            return None

        normalized_input = self._normalize_medication_name(medication_text)

        if normalized_input in self.medication_variants:
            matches = self.medication_variants[normalized_input]
            # Return the most commonly used match
            best_match = max(matches, key=lambda x: x.get('usage_count', 0))

            return {
                **best_match,
                'match_type': 'exact_reference',
                'match_score': 1.0,
                'source': 'map_pro_reference'
            }

        return None

    def find_fuzzy_matches(
        self,
        medication_text: str,
        max_matches: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Find fuzzy matches using string similarity.

        Args:
            medication_text: Medication text to match
            max_matches: Maximum number of matches to return

        Returns:
            List of potential matches with similarity scores
        """
        if not HAS_RAPIDFUZZ and fuzz is None:
            self.logger.warning("Fuzzy matching not available - install rapidfuzz or fuzzywuzzy")
            return []

        if self.reference_mappings.empty:
            return []

        normalized_input = self._normalize_medication_name(medication_text)

        if not normalized_input or len(normalized_input) < 3:
            return []

        # Get fuzzy matches
        matches = []

        if HAS_RAPIDFUZZ or process:
            # Use rapidfuzz or fuzzywuzzy for efficient matching
            choices = list(self.medication_variants.keys())

            if HAS_RAPIDFUZZ:
                from rapidfuzz import fuzz as rf_fuzz
                similarity_results = process.extract(
                    normalized_input,
                    choices,
                    scorer=rf_fuzz.ratio,
                    limit=max_matches * 2
                )
            else:
                similarity_results = process.extract(
                    normalized_input,
                    choices,
                    limit=max_matches * 2
                )

            for match_text, score in similarity_results:
                if score >= (self.min_similarity_score * 100):  # Convert to percentage
                    reference_entries = self.medication_variants[match_text]

                    for entry in reference_entries:
                        matches.append({
                            **entry,
                            'match_type': 'fuzzy_reference',
                            'match_score': score / 100.0,  # Convert back to 0-1 scale
                            'matched_text': match_text,
                            'source': 'map_pro_reference'
                        })

        else:
            # Fallback to basic string matching
            for variant_text, reference_entries in self.medication_variants.items():
                similarity = self._calculate_similarity(normalized_input, variant_text)

                if similarity >= self.min_similarity_score:
                    for entry in reference_entries:
                        matches.append({
                            **entry,
                            'match_type': 'fuzzy_reference',
                            'match_score': similarity,
                            'matched_text': variant_text,
                            'source': 'map_pro_reference'
                        })

        # Sort by score and usage count
        matches.sort(key=lambda x: (x['match_score'], x.get('usage_count', 0)), reverse=True)

        # Remove duplicates (same RxCUI)
        seen_rxcuis = set()
        unique_matches = []

        for match in matches:
            rxcui = match.get('rxcui')
            if rxcui and rxcui not in seen_rxcuis:
                seen_rxcuis.add(rxcui)
                unique_matches.append(match)

                if len(unique_matches) >= max_matches:
                    break

        return unique_matches

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two text strings.

        Args:
            text1: First text
            text2: Second text

        Returns:
            Similarity score between 0 and 1
        """
        if not text1 or not text2:
            return 0.0

        # Use SequenceMatcher for basic similarity
        similarity = SequenceMatcher(None, text1, text2).ratio()

        # Bonus for partial matches
        if text1 in text2 or text2 in text1:
            similarity += 0.1

        # Bonus for word overlap
        words1 = set(text1.split())
        words2 = set(text2.split())

        if words1 and words2:
            word_overlap = len(words1.intersection(words2)) / len(words1.union(words2))
            similarity += word_overlap * 0.2

        return min(similarity, 1.0)

    def find_transplant_medications(self, medication_text: str) -> List[Dict[str, Any]]:
        """
        Find matches specifically for transplant medications.

        Args:
            medication_text: Medication text to search

        Returns:
            List of transplant medication matches
        """
        normalized_input = self._normalize_medication_name(medication_text)

        transplant_matches = []

        # Check against known transplant medications
        for category, medications in IMMUNOSUPPRESSIVE_MEDICATIONS.items():
            for med_name in medications:
                normalized_med = self._normalize_medication_name(med_name)

                if normalized_med in normalized_input or normalized_input in normalized_med:
                    # Look for exact match in reference mappings
                    exact_match = self.find_exact_match(med_name)

                    if exact_match:
                        exact_match['transplant_category'] = category
                        exact_match['is_transplant_medication'] = True
                        transplant_matches.append(exact_match)
                    else:
                        # Create a basic match entry
                        transplant_matches.append({
                            'medication_name': med_name,
                            'transplant_category': category,
                            'is_transplant_medication': True,
                            'match_type': 'transplant_keyword',
                            'match_score': 0.9,
                            'source': 'transplant_catalog'
                        })

        # Also search fuzzy matches and flag transplant medications
        fuzzy_matches = self.find_fuzzy_matches(medication_text)

        for match in fuzzy_matches:
            # Check if this is a known transplant medication
            generic_name = match.get('generic_name', '').lower()
            brand_name = match.get('brand_name', '').lower()

            for category, medications in IMMUNOSUPPRESSIVE_MEDICATIONS.items():
                for med_name in medications:
                    if (med_name.lower() in generic_name or
                        med_name.lower() in brand_name or
                        generic_name in med_name.lower() or
                        brand_name in med_name.lower()):

                        match['transplant_category'] = category
                        match['is_transplant_medication'] = True
                        transplant_matches.append(match)
                        break

        # Remove duplicates
        seen_rxcuis = set()
        unique_transplant_matches = []

        for match in transplant_matches:
            rxcui = match.get('rxcui')
            identifier = rxcui if rxcui else match.get('medication_name', '')

            if identifier and identifier not in seen_rxcuis:
                seen_rxcuis.add(identifier)
                unique_transplant_matches.append(match)

        return unique_transplant_matches

    def find_best_match(
        self,
        medication_text: str,
        prioritize_transplant: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Find the best overall match for a medication.

        Args:
            medication_text: Medication text to match
            prioritize_transplant: Whether to prioritize transplant medications

        Returns:
            Best matching medication or None
        """
        if not medication_text or not medication_text.strip():
            return None

        # Try exact match first
        exact_match = self.find_exact_match(medication_text)
        if exact_match:
            return exact_match

        # Try transplant-specific matching if prioritized
        if prioritize_transplant:
            transplant_matches = self.find_transplant_medications(medication_text)
            if transplant_matches:
                # Return best transplant match
                return max(transplant_matches, key=lambda x: x.get('match_score', 0))

        # Try fuzzy matching
        fuzzy_matches = self.find_fuzzy_matches(medication_text, max_matches=1)
        if fuzzy_matches:
            return fuzzy_matches[0]

        return None

    def batch_match_medications(
        self,
        medications_df: pd.DataFrame,
        text_column: str = 'medication_text',
        prioritize_transplant: bool = True
    ) -> pd.DataFrame:
        """
        Match multiple medications using reference mappings.

        Args:
            medications_df: DataFrame with medication data
            text_column: Column containing medication text
            prioritize_transplant: Whether to prioritize transplant medications

        Returns:
            DataFrame with matching results
        """
        if medications_df.empty or text_column not in medications_df.columns:
            return medications_df

        self.logger.info(f"Starting reference-based matching of {len(medications_df)} medications")

        result_df = medications_df.copy()

        # Add matching result columns
        matching_columns = {
            'reference_rxcui': None,
            'reference_generic_name': None,
            'reference_brand_name': None,
            'reference_drug_class': None,
            'reference_match_type': None,
            'reference_match_score': None,
            'is_transplant_medication': False,
            'transplant_category': None,
            'has_reference_match': False
        }

        for col, default in matching_columns.items():
            result_df[col] = default

        # Process each medication
        matched_count = 0

        for idx, row in result_df.iterrows():
            medication_text = row[text_column]

            if pd.isna(medication_text) or not str(medication_text).strip():
                continue

            try:
                best_match = self.find_best_match(medication_text, prioritize_transplant)

                if best_match:
                    result_df.at[idx, 'reference_rxcui'] = best_match.get('rxcui')
                    result_df.at[idx, 'reference_generic_name'] = best_match.get('generic_name')
                    result_df.at[idx, 'reference_brand_name'] = best_match.get('brand_name')
                    result_df.at[idx, 'reference_drug_class'] = best_match.get('drug_class')
                    result_df.at[idx, 'reference_match_type'] = best_match.get('match_type')
                    result_df.at[idx, 'reference_match_score'] = best_match.get('match_score')
                    result_df.at[idx, 'is_transplant_medication'] = best_match.get('is_transplant_medication', False)
                    result_df.at[idx, 'transplant_category'] = best_match.get('transplant_category')
                    result_df.at[idx, 'has_reference_match'] = True

                    matched_count += 1

            except Exception as e:
                self.logger.error(f"Failed to match medication '{medication_text}': {str(e)}")

        # Calculate matching statistics
        total_medications = len(result_df)
        matching_rate = (matched_count / total_medications) * 100 if total_medications > 0 else 0

        transplant_count = result_df['is_transplant_medication'].sum()

        self.logger.info(
            f"Reference matching complete: {matched_count}/{total_medications} "
            f"({matching_rate:.1f}%) matched, {transplant_count} transplant medications identified"
        )

        return result_df

    def get_matching_statistics(self) -> Dict[str, Any]:
        """Get medication matching statistics."""
        return {
            'reference_mappings_count': len(self.reference_mappings),
            'medication_variants_count': len(self.medication_variants),
            'configuration': {
                'min_similarity_score': self.min_similarity_score,
                'max_edit_distance': self.max_edit_distance,
                'use_phonetic_matching': self.use_phonetic_matching,
                'fuzzy_library': 'rapidfuzz' if HAS_RAPIDFUZZ else 'fuzzywuzzy' if fuzz else 'basic'
            },
            'transplant_medications': {
                category: len(medications)
                for category, medications in IMMUNOSUPPRESSIVE_MEDICATIONS.items()
            }
        }

    def generate_matching_report(self, medications_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate comprehensive matching report.

        Args:
            medications_df: DataFrame with matching results

        Returns:
            Detailed matching report
        """
        if medications_df.empty:
            return {}

        total_count = len(medications_df)
        matched_count = medications_df['has_reference_match'].sum() if 'has_reference_match' in medications_df.columns else 0
        transplant_count = medications_df['is_transplant_medication'].sum() if 'is_transplant_medication' in medications_df.columns else 0

        # Analyze match types
        match_type_counts = {}
        if 'reference_match_type' in medications_df.columns:
            match_type_counts = medications_df['reference_match_type'].value_counts().to_dict()

        # Analyze transplant categories
        transplant_category_counts = {}
        if 'transplant_category' in medications_df.columns:
            transplant_category_counts = medications_df['transplant_category'].value_counts().to_dict()

        # Get top unmapped medications
        unmapped_medications = []
        if 'has_reference_match' in medications_df.columns and 'medication_text' in medications_df.columns:
            unmapped_df = medications_df[~medications_df['has_reference_match']]['medication_text'].value_counts().head(20)
            unmapped_medications = unmapped_df.to_dict()

        return {
            'summary': {
                'total_medications': total_count,
                'matched_medications': matched_count,
                'matching_rate': (matched_count / total_count * 100) if total_count > 0 else 0,
                'transplant_medications': transplant_count,
                'transplant_rate': (transplant_count / total_count * 100) if total_count > 0 else 0
            },
            'match_types': match_type_counts,
            'transplant_categories': transplant_category_counts,
            'top_unmapped_medications': unmapped_medications,
            'statistics': self.get_matching_statistics()
        }