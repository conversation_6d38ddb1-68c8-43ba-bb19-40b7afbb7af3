#!/usr/bin/env python3
"""
Main medication normalization pipeline script.

This script orchestrates the complete medication normalization workflow:
1. Extracts medication data from multiple sources
2. Normalizes medications using RxNorm
3. Transforms to OMOP CDM format
4. Validates output quality

Designed for integration into Curtis's data warehouse workflow.
"""

import sys
import argparse
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.extractors import MAPProExtractor, PioneerRxExtractor, AlloCareExtractor
from src.normalizers import <PERSON><PERSON><PERSON>ormMapper, MedicationMatcher, DoseNormalizer
from src.transformers import OMOPTransformer
from src.validators import DataValidator, MappingValidator, OMOPValidator
from src.utils.logger import LoggerMixin
from src.utils.exceptions import ValidationError, ExtractionError, NormalizationError, TransformationError


class MedicationNormalizationPipeline(LoggerMixin):
    """
    Main medication normalization pipeline.

    Orchestrates extraction, normalization, transformation, and validation
    of medication data from multiple sources into OMOP CDM format.
    """

    def __init__(self, config_path: str):
        """
        Initialize pipeline with configuration.

        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.pipeline_stats = {
            'start_time': None,
            'end_time': None,
            'total_input_records': 0,
            'total_output_records': 0,
            'source_extraction_stats': {},
            'normalization_stats': {},
            'transformation_stats': {},
            'validation_stats': {},
            'errors': []
        }

        # Initialize components
        self._initialize_components()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load pipeline configuration."""
        config_file = Path(config_path)

        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        if config_file.suffix.lower() == '.yaml' or config_file.suffix.lower() == '.yml':
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        elif config_file.suffix.lower() == '.json':
            with open(config_file, 'r') as f:
                return json.load(f)
        else:
            raise ValueError(f"Unsupported configuration file format: {config_file.suffix}")

    def _initialize_components(self) -> None:
        """Initialize pipeline components."""
        # Extractors
        self.extractors = {
            'map_pro': MAPProExtractor(self.config),
            'pioneer_rx': PioneerRxExtractor(self.config),
            'allocare': AlloCareExtractor(self.config)
        }

        # Normalizers
        self.rxnorm_mapper = RxNormMapper(self.config)
        self.medication_matcher = MedicationMatcher(self.config)
        self.dose_normalizer = DoseNormalizer(self.config)

        # Transformer
        self.omop_transformer = OMOPTransformer(self.config)

        # Validators
        self.data_validator = DataValidator(self.config)
        self.mapping_validator = MappingValidator(self.config)
        self.omop_validator = OMOPValidator(self.config)

    def run_pipeline(
        self,
        sources: Optional[List[str]] = None,
        output_dir: Optional[str] = None,
        validate_only: bool = False,
        skip_normalization: bool = False,
        skip_transformation: bool = False
    ) -> Dict[str, Any]:
        """
        Run the complete medication normalization pipeline.

        Args:
            sources: List of source systems to process (None = all)
            output_dir: Directory to save outputs
            validate_only: Only run validation on existing data
            skip_normalization: Skip RxNorm normalization step
            skip_transformation: Skip OMOP transformation step

        Returns:
            Pipeline execution results
        """
        self.pipeline_stats['start_time'] = datetime.now()
        self.logger.info("Starting medication normalization pipeline")

        try:
            # Step 1: Extract data from sources
            if not validate_only:
                extracted_data = self._extract_data(sources)
                if not extracted_data:
                    return self._create_empty_result("No data extracted from any source")

                # Step 2: Normalize medications
                if not skip_normalization:
                    normalized_data = self._normalize_medications(extracted_data)
                else:
                    normalized_data = extracted_data

                # Step 3: Transform to OMOP
                if not skip_transformation:
                    omop_data = self._transform_to_omop(normalized_data, output_dir)
                else:
                    omop_data = {'tables': {}, 'statistics': {}}

            else:
                # Load existing data for validation
                omop_data = self._load_existing_omop_data(output_dir)

            # Step 4: Validate results
            validation_results = self._validate_results(omop_data)

            # Generate final results
            pipeline_results = self._generate_pipeline_results(
                omop_data, validation_results, output_dir
            )

            self.pipeline_stats['end_time'] = datetime.now()
            self._log_pipeline_summary()

            return pipeline_results

        except Exception as e:
            self.pipeline_stats['end_time'] = datetime.now()
            self.pipeline_stats['errors'].append(str(e))
            self.logger.error(f"Pipeline execution failed: {str(e)}")
            raise

    def _extract_data(self, sources: Optional[List[str]] = None) -> Dict[str, Any]:
        """Extract data from specified sources."""
        self.logger.info("Extracting medication data from sources")

        all_extracted_data = []
        sources_to_process = sources or list(self.extractors.keys())

        for source_name in sources_to_process:
            if source_name not in self.extractors:
                self.logger.warning(f"Unknown source: {source_name}")
                continue

            try:
                extractor = self.extractors[source_name]
                self.logger.info(f"Extracting from {source_name}")

                if source_name == 'map_pro':
                    # Extract both web and mobile data
                    web_data = extractor.extract_web_medications()
                    mobile_data = extractor.extract_mobile_medications()
                    source_data = web_data + mobile_data

                elif source_name == 'pioneer_rx':
                    source_data = extractor.extract_prescription_data()

                elif source_name == 'allocare':
                    source_data = extractor.extract_fhir_medications()

                else:
                    continue

                # Add source system identifier
                for record in source_data:
                    record['source_system'] = source_name

                all_extracted_data.extend(source_data)

                # Track statistics
                self.pipeline_stats['source_extraction_stats'][source_name] = {
                    'records_extracted': len(source_data),
                    'extractor_stats': extractor.get_extraction_statistics()
                }

                self.logger.info(f"Extracted {len(source_data)} records from {source_name}")

            except Exception as e:
                error_msg = f"Failed to extract from {source_name}: {str(e)}"
                self.logger.error(error_msg)
                self.pipeline_stats['errors'].append(error_msg)
                continue

        self.pipeline_stats['total_input_records'] = len(all_extracted_data)
        self.logger.info(f"Total extracted records: {len(all_extracted_data)}")

        return all_extracted_data

    def _normalize_medications(self, extracted_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Normalize medication data using RxNorm."""
        self.logger.info("Normalizing medications using RxNorm")

        # Convert to DataFrame for processing
        import pandas as pd
        df = pd.DataFrame(extracted_data)

        if df.empty:
            return []

        try:
            # Step 1: Get RxNorm reference mappings from MAP Pro
            reference_mappings = self.extractors['map_pro'].get_rxnorm_mappings()

            # Step 2: Match medications against reference mappings
            df = self.medication_matcher.match_medications(df, reference_mappings)

            # Step 3: Map remaining medications via RxNorm API
            df = self.rxnorm_mapper.map_medications(df)

            # Step 4: Normalize doses and frequencies
            df = self.dose_normalizer.normalize_doses(df)

            # Track normalization statistics
            self.pipeline_stats['normalization_stats'] = {
                'rxnorm_mapper_stats': self.rxnorm_mapper.get_mapping_statistics(),
                'medication_matcher_stats': self.medication_matcher.get_matching_statistics(),
                'dose_normalizer_stats': self.dose_normalizer.get_normalization_statistics()
            }

            self.logger.info(f"Normalized {len(df)} medication records")
            return df.to_dict('records')

        except Exception as e:
            error_msg = f"Medication normalization failed: {str(e)}"
            self.logger.error(error_msg)
            raise NormalizationError(error_msg)

    def _transform_to_omop(
        self,
        normalized_data: List[Dict[str, Any]],
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Transform normalized data to OMOP CDM format."""
        self.logger.info("Transforming to OMOP CDM format")

        # Convert to DataFrame
        import pandas as pd
        df = pd.DataFrame(normalized_data)

        if df.empty:
            return {'tables': {}, 'statistics': {}}

        try:
            # Transform to OMOP
            omop_result = self.omop_transformer.transform_to_omop(
                normalized_medications_df=df,
                generate_eras=True,
                include_vocabulary=True,
                output_dir=output_dir
            )

            # Track transformation statistics
            self.pipeline_stats['transformation_stats'] = omop_result.get('statistics', {})
            self.pipeline_stats['total_output_records'] = sum(
                len(table) for table in omop_result.get('tables', {}).values()
            )

            self.logger.info(f"Generated {len(omop_result.get('tables', {}))} OMOP tables")
            return omop_result

        except Exception as e:
            error_msg = f"OMOP transformation failed: {str(e)}"
            self.logger.error(error_msg)
            raise TransformationError(error_msg)

    def _validate_results(self, omop_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate pipeline results."""
        self.logger.info("Validating pipeline results")

        validation_results = {
            'data_validation': {},
            'mapping_validation': {},
            'omop_validation': {},
            'overall_status': 'PASS'
        }

        try:
            omop_tables = omop_data.get('tables', {})

            # Validate OMOP tables
            for table_name, table_df in omop_tables.items():
                if not table_df.empty:
                    table_validation = self.omop_validator.validate_table(table_df, table_name)
                    validation_results['omop_validation'][table_name] = table_validation

                    if not table_validation.get('is_valid', False):
                        validation_results['overall_status'] = 'FAIL'

            # Track validation statistics
            self.pipeline_stats['validation_stats'] = validation_results

            return validation_results

        except Exception as e:
            error_msg = f"Validation failed: {str(e)}"
            self.logger.error(error_msg)
            raise ValidationError(error_msg)

    def _load_existing_omop_data(self, output_dir: str) -> Dict[str, Any]:
        """Load existing OMOP data for validation."""
        import pandas as pd

        if not output_dir:
            raise ValueError("Output directory required for validation-only mode")

        output_path = Path(output_dir)
        omop_tables = {}

        # Load existing OMOP tables
        for table_file in output_path.glob("*.parquet"):
            table_name = table_file.stem
            try:
                omop_tables[table_name] = pd.read_parquet(table_file)
                self.logger.info(f"Loaded {table_name}: {len(omop_tables[table_name])} records")
            except Exception as e:
                self.logger.error(f"Failed to load {table_file}: {str(e)}")

        return {'tables': omop_tables, 'statistics': {}}

    def _generate_pipeline_results(
        self,
        omop_data: Dict[str, Any],
        validation_results: Dict[str, Any],
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate final pipeline results."""
        results = {
            'status': 'SUCCESS' if validation_results.get('overall_status') == 'PASS' else 'VALIDATION_FAILED',
            'execution_summary': {
                'start_time': self.pipeline_stats['start_time'].isoformat(),
                'end_time': self.pipeline_stats['end_time'].isoformat(),
                'execution_time_seconds': (
                    self.pipeline_stats['end_time'] - self.pipeline_stats['start_time']
                ).total_seconds(),
                'total_input_records': self.pipeline_stats['total_input_records'],
                'total_output_records': self.pipeline_stats['total_output_records'],
                'error_count': len(self.pipeline_stats['errors'])
            },
            'omop_tables': {
                name: len(table) for name, table in omop_data.get('tables', {}).items()
            },
            'validation_results': validation_results,
            'detailed_statistics': {
                'extraction': self.pipeline_stats['source_extraction_stats'],
                'normalization': self.pipeline_stats['normalization_stats'],
                'transformation': self.pipeline_stats['transformation_stats'],
                'validation': self.pipeline_stats['validation_stats']
            }
        }

        if output_dir:
            results['output_directory'] = output_dir
            results['saved_files'] = omop_data.get('saved_files', {})

        if self.pipeline_stats['errors']:
            results['errors'] = self.pipeline_stats['errors']

        return results

    def _create_empty_result(self, message: str) -> Dict[str, Any]:
        """Create empty result with message."""
        return {
            'status': 'NO_DATA',
            'message': message,
            'omop_tables': {},
            'validation_results': {},
            'execution_summary': {
                'total_input_records': 0,
                'total_output_records': 0
            }
        }

    def _log_pipeline_summary(self) -> None:
        """Log pipeline execution summary."""
        stats = self.pipeline_stats
        execution_time = (stats['end_time'] - stats['start_time']).total_seconds()

        self.logger.info(
            f"Pipeline Execution Summary:\n"
            f"  Input Records: {stats['total_input_records']:,}\n"
            f"  Output Records: {stats['total_output_records']:,}\n"
            f"  Execution Time: {execution_time:.2f} seconds\n"
            f"  Sources Processed: {len(stats['source_extraction_stats'])}\n"
            f"  Errors: {len(stats['errors'])}"
        )


def main():
    """Main entry point for the pipeline script."""
    parser = argparse.ArgumentParser(
        description="Medication Normalization Pipeline for OMOP CDM",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full pipeline for all sources
  python medication_normalization_pipeline.py --config config/pipeline_config.yaml --output-dir output/

  # Run for specific sources only
  python medication_normalization_pipeline.py --config config/pipeline_config.yaml --sources map_pro pioneer_rx --output-dir output/

  # Validate existing OMOP output
  python medication_normalization_pipeline.py --config config/pipeline_config.yaml --output-dir output/ --validate-only

  # Quick test without OMOP transformation
  python medication_normalization_pipeline.py --config config/pipeline_config.yaml --skip-transformation
        """
    )

    parser.add_argument(
        '--config',
        required=True,
        help='Path to pipeline configuration file (YAML or JSON)'
    )

    parser.add_argument(
        '--output-dir',
        help='Directory to save OMOP output files'
    )

    parser.add_argument(
        '--sources',
        nargs='+',
        choices=['map_pro', 'pioneer_rx', 'allocare'],
        help='Source systems to process (default: all)'
    )

    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='Only validate existing output without processing'
    )

    parser.add_argument(
        '--skip-normalization',
        action='store_true',
        help='Skip RxNorm normalization step'
    )

    parser.add_argument(
        '--skip-transformation',
        action='store_true',
        help='Skip OMOP transformation step'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    try:
        # Initialize pipeline
        pipeline = MedicationNormalizationPipeline(args.config)

        # Set log level
        if args.verbose:
            import logging
            logging.getLogger().setLevel(logging.DEBUG)

        # Run pipeline
        results = pipeline.run_pipeline(
            sources=args.sources,
            output_dir=args.output_dir,
            validate_only=args.validate_only,
            skip_normalization=args.skip_normalization,
            skip_transformation=args.skip_transformation
        )

        # Print results summary
        print("\n" + "="*60)
        print("MEDICATION NORMALIZATION PIPELINE RESULTS")
        print("="*60)
        print(f"Status: {results['status']}")
        print(f"Input Records: {results['execution_summary']['total_input_records']:,}")
        print(f"Output Records: {results['execution_summary']['total_output_records']:,}")
        print(f"Execution Time: {results['execution_summary']['execution_time_seconds']:.2f} seconds")

        if results.get('omop_tables'):
            print("\nGenerated OMOP Tables:")
            for table_name, record_count in results['omop_tables'].items():
                print(f"  {table_name}: {record_count:,} records")

        if results.get('errors'):
            print(f"\nErrors: {len(results['errors'])}")
            for error in results['errors']:
                print(f"  - {error}")

        if results['status'] == 'SUCCESS':
            print("\n✅ Pipeline completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Pipeline completed with issues.")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()