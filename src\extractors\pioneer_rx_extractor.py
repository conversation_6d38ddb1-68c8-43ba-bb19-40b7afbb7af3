"""
PioneerRx pharmacy system medication data extractor.
"""

import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_extractor import BaseExtractor
from ..utils.constants import SourceSystem
from ..utils.exceptions import ExtractionError


class PioneerRxExtractor(BaseExtractor):
    """
    Extractor for PioneerRx pharmacy management system.

    Handles prescription and dispensing data from The Transplant Pharmacy,
    focusing on actual medication transactions and pharmacy operations.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config, SourceSystem.PIONEER_RX)

    def extract_medications(self) -> pd.DataFrame:
        """
        Extract medication data from PioneerRx database.

        Combines prescription data with dispensing transactions and medication items
        to provide comprehensive pharmacy-centric medication information.

        Returns:
            DataFrame with medication data in unified schema
        """
        try:
            db_connector = self.connect_database()

            # Get table names from config
            tables = self.config.get('tables', {})

            # Extract active prescriptions with their items
            prescriptions_df = self._extract_prescriptions(db_connector, tables)

            # Extract dispensing transactions
            transactions_df = self._extract_transactions(db_connector, tables)

            # Combine prescription and transaction data
            medications_df = self._combine_prescription_and_transaction_data(
                prescriptions_df, transactions_df
            )

            self.logger.info(f"Extracted {len(medications_df)} PioneerRx medication records")
            return medications_df

        except Exception as e:
            raise ExtractionError(
                f"Failed to extract PioneerRx medications: {str(e)}",
                source_system=self.source_system.value,
                table_name="pioneer_rx_medications"
            )

    def _extract_prescriptions(self, db_connector, tables: Dict[str, str]) -> pd.DataFrame:
        """Extract prescription data with medication item details."""

        rx_table = tables.get('prescriptions', 'prescription__rx')
        items_table = tables.get('items', 'item__item')
        dosage_forms_table = tables.get('dosage_forms', 'item__dosageform')
        admin_table = tables.get('administrations', 'item__ItemAdministration')

        query = f"""
        SELECT
            -- Prescription information
            rx.RxID,
            rx.RxNumber,
            rx.PatientID,
            rx.PrescriberID,
            rx.DateWritten,
            rx.ExpirationDate,
            rx.Quantity,
            rx.NumberOfRefillsAllowed,
            rx.NumberOfRefillsFilled,
            rx.RxStatusTypeID,
            rx.DaysSupplyEndsOn,
            rx.RxTotalQuantityRemaining,

            -- Medication item details
            i.ItemID as PrescribedItemID,
            i.ItemName as medication_text,
            i.ItemDescription as medication_description,
            i.NDC as ndc_code,
            i.Strength as item_strength,
            i.StrengthUnit as strength_unit,

            -- Dosage form information
            df.DosageFormName as dosage_form,
            df.DosageFormDescription as dosage_form_description,

            -- Administration route
            ia.AdministrationName as administration_route,
            ia.AdministrationDescription as route_description

        FROM {rx_table} rx
        LEFT JOIN {items_table} i ON rx.PrescribedItemID = i.ItemID
        LEFT JOIN {dosage_forms_table} df ON i.DosageFormID = df.DosageFormID
        LEFT JOIN {admin_table} ia ON i.ItemAdministrationID = ia.AdministrationID

        WHERE rx.PatientID IS NOT NULL
        AND i.ItemName IS NOT NULL
        """

        # Apply date filtering
        date_column = self.config.get('extraction', {}).get('date_filter_column', 'DateWritten')
        query = self.apply_date_filter(query, f"rx.{date_column}")

        # Apply record limit
        query = self.apply_record_limit(query)

        self.logger.debug(f"Executing PioneerRx prescriptions query: {query[:200]}...")

        return db_connector.execute_query(query)

    def _extract_transactions(self, db_connector, tables: Dict[str, str]) -> pd.DataFrame:
        """Extract dispensing transaction data."""

        transactions_table = tables.get('transactions', 'prescription__rxtransaction')
        items_table = tables.get('items', 'item__item')

        query = f"""
        SELECT
            -- Transaction information
            rt.RxTransactionID,
            rt.RxID,
            rt.RefillNumber,
            rt.DateFilled,
            rt.CompletedDate,
            rt.DispensedQuantity,
            rt.DaysSupply,
            rt.RxTransactionStatusTypeID,

            -- Dispensed item details
            i.ItemID as DispensedItemID,
            i.ItemName as dispensed_medication_text,
            i.NDC as dispensed_ndc,
            i.Strength as dispensed_strength,
            i.StrengthUnit as dispensed_strength_unit,

            -- Financial information
            rt.TotalPricePaid,
            rt.PatientPaidAmount,
            rt.AcquisitionCost,

            -- Facility information
            rt.FacilityID,
            rt.PatientFacilityID

        FROM {transactions_table} rt
        LEFT JOIN {items_table} i ON rt.DispensedItemID = i.ItemID

        WHERE rt.RxID IS NOT NULL
        AND rt.DateFilled IS NOT NULL
        """

        # Apply date filtering on DateFilled
        query = self.apply_date_filter(query, "rt.DateFilled")

        self.logger.debug(f"Executing PioneerRx transactions query: {query[:200]}...")

        return db_connector.execute_query(query)

    def _combine_prescription_and_transaction_data(
        self,
        prescriptions_df: pd.DataFrame,
        transactions_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Combine prescription and transaction data into unified medication records.

        Args:
            prescriptions_df: Prescription data
            transactions_df: Transaction/dispensing data

        Returns:
            Combined DataFrame in unified schema
        """
        unified_records = []

        # Process prescriptions (what was prescribed)
        for _, rx_row in prescriptions_df.iterrows():
            record = self.create_unified_record(
                patient_id=f"prx_{rx_row['PatientID']}",
                medication_text=rx_row['medication_text'],
                source_table='prescription__rx',
                source_record_id=rx_row['RxID'],
                medication_code=rx_row.get('ndc_code'),
                dose_value=self._parse_dose_from_strength(rx_row.get('item_strength')),
                dose_unit=rx_row.get('strength_unit'),
                route=rx_row.get('administration_route'),
                frequency=self._estimate_frequency_from_days_supply(
                    rx_row.get('Quantity'), rx_row.get('DaysSupplyEndsOn')
                ),
                start_date=rx_row.get('DateWritten'),
                end_date=rx_row.get('ExpirationDate')
            )

            # Add PioneerRx specific fields
            record.update({
                'record_type': 'prescription',
                'rx_number': rx_row.get('RxNumber'),
                'prescriber_id': rx_row.get('PrescriberID'),
                'prescribed_item_id': rx_row.get('PrescribedItemID'),
                'quantity_prescribed': rx_row.get('Quantity'),
                'refills_allowed': rx_row.get('NumberOfRefillsAllowed'),
                'refills_filled': rx_row.get('NumberOfRefillsFilled'),
                'rx_status': rx_row.get('RxStatusTypeID'),
                'dosage_form': rx_row.get('dosage_form'),
                'days_supply_ends': rx_row.get('DaysSupplyEndsOn'),
                'remaining_quantity': rx_row.get('RxTotalQuantityRemaining')
            })

            unified_records.append(record)

        # Process transactions (what was actually dispensed)
        for _, tx_row in transactions_df.iterrows():
            # Find corresponding prescription
            matching_rx = prescriptions_df[prescriptions_df['RxID'] == tx_row['RxID']]

            if not matching_rx.empty:
                rx_info = matching_rx.iloc[0]

                record = self.create_unified_record(
                    patient_id=f"prx_{rx_info['PatientID']}",
                    medication_text=tx_row.get('dispensed_medication_text', rx_info['medication_text']),
                    source_table='prescription__rxtransaction',
                    source_record_id=tx_row['RxTransactionID'],
                    medication_code=tx_row.get('dispensed_ndc', rx_info.get('ndc_code')),
                    dose_value=self._parse_dose_from_strength(
                        tx_row.get('dispensed_strength', rx_info.get('item_strength'))
                    ),
                    dose_unit=tx_row.get('dispensed_strength_unit', rx_info.get('strength_unit')),
                    route=rx_info.get('administration_route'),
                    frequency=self._estimate_frequency_from_days_supply(
                        tx_row.get('DispensedQuantity'), tx_row.get('DaysSupply')
                    ),
                    start_date=tx_row.get('DateFilled'),
                    end_date=tx_row.get('CompletedDate')
                )

                # Add transaction specific fields
                record.update({
                    'record_type': 'dispensed',
                    'rx_id': tx_row.get('RxID'),
                    'refill_number': tx_row.get('RefillNumber'),
                    'dispensed_item_id': tx_row.get('DispensedItemID'),
                    'quantity_dispensed': tx_row.get('DispensedQuantity'),
                    'days_supply': tx_row.get('DaysSupply'),
                    'transaction_status': tx_row.get('RxTransactionStatusTypeID'),
                    'total_price': tx_row.get('TotalPricePaid'),
                    'patient_paid': tx_row.get('PatientPaidAmount'),
                    'acquisition_cost': tx_row.get('AcquisitionCost'),
                    'facility_id': tx_row.get('FacilityID'),
                    'date_filled': tx_row.get('DateFilled'),
                    'date_completed': tx_row.get('CompletedDate')
                })

                unified_records.append(record)

        return pd.DataFrame(unified_records)

    def extract_patients(self) -> pd.DataFrame:
        """
        Extract patient data from PioneerRx.

        Note: Patient table structure may vary. This extracts basic information
        linked to prescriptions.

        Returns:
            DataFrame with patient information
        """
        try:
            db_connector = self.connect_database()
            tables = self.config.get('tables', {})
            rx_table = tables.get('prescriptions', 'prescription__rx')

            # Extract unique patients from prescription data
            query = f"""
            SELECT DISTINCT
                PatientID,
                COUNT(DISTINCT RxID) as total_prescriptions,
                COUNT(DISTINCT PrescriberID) as unique_prescribers,
                MIN(DateWritten) as first_prescription_date,
                MAX(DateWritten) as last_prescription_date,
                SUM(Quantity) as total_quantity_prescribed

            FROM {rx_table}
            WHERE PatientID IS NOT NULL

            GROUP BY PatientID
            ORDER BY total_prescriptions DESC
            """

            patients_df = db_connector.execute_query(query)
            patients_df['source_system'] = self.source_system.value

            self.logger.info(f"Extracted {len(patients_df)} PioneerRx patients")
            return patients_df

        except Exception as e:
            raise ExtractionError(
                f"Failed to extract PioneerRx patients: {str(e)}",
                source_system=self.source_system.value
            )

    def _parse_dose_from_strength(self, strength_text: Optional[str]) -> Optional[float]:
        """
        Parse numeric dose value from strength text.

        Args:
            strength_text: Strength text (e.g., "5mg", "0.5mg/ml")

        Returns:
            Numeric dose value or None
        """
        if not strength_text:
            return None

        try:
            # Simple regex to extract first number
            import re
            match = re.search(r'(\d+\.?\d*)', str(strength_text))
            if match:
                return float(match.group(1))
        except (ValueError, AttributeError):
            pass

        return None

    def _estimate_frequency_from_days_supply(
        self,
        quantity: Optional[float],
        days_supply: Optional[int]
    ) -> Optional[str]:
        """
        Estimate dosing frequency from quantity and days supply.

        Args:
            quantity: Total quantity dispensed
            days_supply: Days of supply

        Returns:
            Estimated frequency string
        """
        if not quantity or not days_supply or days_supply <= 0:
            return None

        try:
            doses_per_day = quantity / days_supply

            if doses_per_day <= 1.2:
                return "once daily"
            elif doses_per_day <= 2.2:
                return "twice daily"
            elif doses_per_day <= 3.2:
                return "three times daily"
            elif doses_per_day <= 4.2:
                return "four times daily"
            else:
                return f"{doses_per_day:.1f} times daily"

        except (ValueError, ZeroDivisionError):
            return None

    def get_medication_items_catalog(self) -> pd.DataFrame:
        """
        Extract medication items catalog from PioneerRx for reference.

        This provides the master list of medication products available
        in the pharmacy system.

        Returns:
            DataFrame with medication items catalog
        """
        try:
            db_connector = self.connect_database()
            tables = self.config.get('tables', {})
            items_table = tables.get('items', 'item__item')
            dosage_forms_table = tables.get('dosage_forms', 'item__dosageform')
            admin_table = tables.get('administrations', 'item__ItemAdministration')

            query = f"""
            SELECT
                i.ItemID,
                i.ItemName,
                i.ItemDescription,
                i.NDC,
                i.Strength,
                i.StrengthUnit,
                i.PackageSize,
                i.PackageUnit,

                -- Dosage form details
                df.DosageFormName,
                df.DosageFormDescription,

                -- Administration details
                ia.AdministrationName,
                ia.AdministrationDescription,

                -- Usage statistics
                COUNT(DISTINCT rx.PatientID) as unique_patients,
                COUNT(rx.RxID) as total_prescriptions,
                SUM(rx.Quantity) as total_quantity_prescribed

            FROM {items_table} i
            LEFT JOIN {dosage_forms_table} df ON i.DosageFormID = df.DosageFormID
            LEFT JOIN {admin_table} ia ON i.ItemAdministrationID = ia.AdministrationID
            LEFT JOIN prescription__rx rx ON i.ItemID = rx.PrescribedItemID

            WHERE i.ItemName IS NOT NULL

            GROUP BY
                i.ItemID, i.ItemName, i.ItemDescription, i.NDC,
                i.Strength, i.StrengthUnit, i.PackageSize, i.PackageUnit,
                df.DosageFormName, df.DosageFormDescription,
                ia.AdministrationName, ia.AdministrationDescription

            ORDER BY total_prescriptions DESC, unique_patients DESC
            """

            catalog_df = db_connector.execute_query(query)

            self.logger.info(f"Retrieved {len(catalog_df)} medication items from PioneerRx catalog")
            return catalog_df

        except Exception as e:
            self.logger.error(f"Failed to extract medication catalog: {str(e)}")
            return pd.DataFrame()

    def get_dispensing_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics for PioneerRx dispensing data.

        Returns:
            Dictionary with dispensing summary statistics
        """
        try:
            db_connector = self.connect_database()
            tables = self.config.get('tables', {})

            # Get basic counts
            rx_table = tables.get('prescriptions', 'prescription__rx')
            tx_table = tables.get('transactions', 'prescription__rxtransaction')

            summary_query = f"""
            SELECT
                'prescriptions' as metric,
                COUNT(*) as count,
                COUNT(DISTINCT PatientID) as unique_patients,
                MIN(DateWritten) as earliest_date,
                MAX(DateWritten) as latest_date
            FROM {rx_table}

            UNION ALL

            SELECT
                'transactions' as metric,
                COUNT(*) as count,
                COUNT(DISTINCT RxID) as unique_prescriptions,
                MIN(DateFilled) as earliest_date,
                MAX(DateFilled) as latest_date
            FROM {tx_table}
            WHERE DateFilled IS NOT NULL
            """

            summary_df = db_connector.execute_query(summary_query)

            # Convert to dictionary
            summary = {}
            for _, row in summary_df.iterrows():
                summary[row['metric']] = {
                    'count': row['count'],
                    'unique_entities': row.get('unique_patients') or row.get('unique_prescriptions'),
                    'date_range': {
                        'earliest': row['earliest_date'],
                        'latest': row['latest_date']
                    }
                }

            self.logger.info(f"Generated PioneerRx dispensing summary: {summary}")
            return summary

        except Exception as e:
            self.logger.error(f"Failed to generate dispensing summary: {str(e)}")
            return {}