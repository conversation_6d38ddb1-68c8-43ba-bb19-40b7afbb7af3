"""
Utility functions and factory for medication data extractors.
"""

import pandas as pd
from typing import Dict, Any, List, Type, Optional
import yaml
import os
from pathlib import Path

from .base_extractor import BaseExtractor
from .map_pro_extractor import MapProExtractor
from .pioneer_rx_extractor import PioneerRxExtractor
from .allocare_extractor import AllocareExtractor
from ..utils.constants import SourceSystem
from ..utils.exceptions import ConfigurationError, ExtractionError
from ..utils.logger import LoggerMixin


class ExtractorFactory(LoggerMixin):
    """
    Factory class for creating and managing medication data extractors.
    """

    # Map source system names to extractor classes
    EXTRACTOR_CLASSES: Dict[str, Type[BaseExtractor]] = {
        'map_pro': MapProExtractor,
        'pioneer_rx': PioneerRxExtractor,
        'allocare': AllocareExtractor
    }

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize extractor factory.

        Args:
            config_path: Path to source configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        if not self.config_path:
            # Use default config path
            current_dir = Path(__file__).parent.parent.parent
            self.config_path = current_dir / "config" / "source_configs.yaml"

        if not os.path.exists(self.config_path):
            raise ConfigurationError(
                f"Configuration file not found: {self.config_path}",
                config_file=str(self.config_path)
            )

        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            # Replace environment variables
            config = self._substitute_environment_variables(config)

            self.logger.info(f"Loaded configuration from {self.config_path}")
            return config

        except Exception as e:
            raise ConfigurationError(
                f"Failed to load configuration: {str(e)}",
                config_file=str(self.config_path)
            )

    def _substitute_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Substitute environment variables in configuration values."""
        import re

        def substitute_recursive(obj):
            if isinstance(obj, dict):
                return {k: substitute_recursive(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [substitute_recursive(item) for item in obj]
            elif isinstance(obj, str):
                # Find environment variable patterns like ${VAR_NAME}
                pattern = r'\$\{([^}]+)\}'
                matches = re.findall(pattern, obj)
                result = obj
                for match in matches:
                    env_value = os.getenv(match)
                    if env_value is not None:
                        result = result.replace(f'${{{match}}}', env_value)
                return result
            else:
                return obj

        return substitute_recursive(config)

    def create_extractor(self, source_name: str) -> BaseExtractor:
        """
        Create an extractor for the specified source.

        Args:
            source_name: Name of the source system (e.g., 'map_pro', 'pioneer_rx')

        Returns:
            Configured extractor instance

        Raises:
            ConfigurationError: If source configuration is invalid
        """
        if source_name not in self.EXTRACTOR_CLASSES:
            raise ConfigurationError(
                f"Unknown source system: {source_name}. "
                f"Available sources: {list(self.EXTRACTOR_CLASSES.keys())}",
                config_key=source_name
            )

        source_config = self.config.get(source_name)
        if not source_config:
            raise ConfigurationError(
                f"No configuration found for source: {source_name}",
                config_file=str(self.config_path),
                config_key=source_name
            )

        extractor_class = self.EXTRACTOR_CLASSES[source_name]

        try:
            extractor = extractor_class(source_config)
            self.logger.info(f"Created {source_name} extractor")
            return extractor

        except Exception as e:
            raise ConfigurationError(
                f"Failed to create {source_name} extractor: {str(e)}",
                config_key=source_name
            )

    def create_all_extractors(self, enabled_only: bool = True) -> Dict[str, BaseExtractor]:
        """
        Create extractors for all configured sources.

        Args:
            enabled_only: Only create extractors for enabled sources

        Returns:
            Dictionary mapping source names to extractor instances
        """
        extractors = {}

        for source_name in self.EXTRACTOR_CLASSES.keys():
            source_config = self.config.get(source_name, {})

            if enabled_only and not source_config.get('enabled', True):
                self.logger.info(f"Skipping disabled source: {source_name}")
                continue

            try:
                extractor = self.create_extractor(source_name)
                extractors[source_name] = extractor
            except Exception as e:
                self.logger.error(f"Failed to create {source_name} extractor: {str(e)}")
                if not enabled_only:
                    # Re-raise if we're trying to create all extractors
                    raise

        self.logger.info(f"Created {len(extractors)} extractors")
        return extractors

    def get_available_sources(self) -> List[str]:
        """Get list of available source systems."""
        return list(self.EXTRACTOR_CLASSES.keys())

    def get_enabled_sources(self) -> List[str]:
        """Get list of enabled source systems."""
        enabled_sources = []
        for source_name in self.EXTRACTOR_CLASSES.keys():
            source_config = self.config.get(source_name, {})
            if source_config.get('enabled', True):
                enabled_sources.append(source_name)
        return enabled_sources


class ExtractionOrchestrator(LoggerMixin):
    """
    Orchestrates medication data extraction from multiple sources.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize extraction orchestrator.

        Args:
            config_path: Path to source configuration file
        """
        self.factory = ExtractorFactory(config_path)
        self.extraction_results = {}

    def extract_from_sources(
        self,
        source_names: Optional[List[str]] = None,
        parallel: bool = False
    ) -> Dict[str, pd.DataFrame]:
        """
        Extract medication data from specified sources.

        Args:
            source_names: List of source names to extract from (None = all enabled)
            parallel: Whether to run extractions in parallel

        Returns:
            Dictionary mapping source names to extracted DataFrames
        """
        if source_names is None:
            source_names = self.factory.get_enabled_sources()

        self.logger.info(f"Starting extraction from sources: {source_names}")

        if parallel:
            return self._extract_parallel(source_names)
        else:
            return self._extract_sequential(source_names)

    def _extract_sequential(self, source_names: List[str]) -> Dict[str, pd.DataFrame]:
        """Extract data sequentially from sources."""
        results = {}

        for source_name in source_names:
            try:
                self.logger.info(f"Extracting from {source_name}")
                extractor = self.factory.create_extractor(source_name)

                medications_df = extractor.run_extraction()
                results[source_name] = medications_df

                # Store extraction statistics
                self.extraction_results[source_name] = {
                    'status': 'success',
                    'record_count': len(medications_df),
                    'stats': extractor.get_extraction_stats()
                }

                self.logger.info(f"Successfully extracted {len(medications_df)} records from {source_name}")

            except Exception as e:
                self.logger.error(f"Extraction failed for {source_name}: {str(e)}")
                self.extraction_results[source_name] = {
                    'status': 'failed',
                    'error': str(e),
                    'record_count': 0
                }
                # Continue with other sources
                results[source_name] = pd.DataFrame()

        return results

    def _extract_parallel(self, source_names: List[str]) -> Dict[str, pd.DataFrame]:
        """Extract data in parallel from sources."""
        import concurrent.futures
        import threading

        results = {}
        extraction_results = {}
        lock = threading.Lock()

        def extract_source(source_name: str):
            try:
                extractor = self.factory.create_extractor(source_name)
                medications_df = extractor.run_extraction()

                with lock:
                    results[source_name] = medications_df
                    extraction_results[source_name] = {
                        'status': 'success',
                        'record_count': len(medications_df),
                        'stats': extractor.get_extraction_stats()
                    }

                self.logger.info(f"Successfully extracted {len(medications_df)} records from {source_name}")

            except Exception as e:
                self.logger.error(f"Extraction failed for {source_name}: {str(e)}")
                with lock:
                    results[source_name] = pd.DataFrame()
                    extraction_results[source_name] = {
                        'status': 'failed',
                        'error': str(e),
                        'record_count': 0
                    }

        # Get max workers from global config
        global_config = self.factory.config.get('global', {})
        max_workers = global_config.get('max_workers', 4)

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(extract_source, source): source for source in source_names}

            for future in concurrent.futures.as_completed(futures):
                source_name = futures[future]
                try:
                    future.result()  # Get result to raise any exceptions
                except Exception as e:
                    self.logger.error(f"Parallel extraction error for {source_name}: {str(e)}")

        # Update instance extraction results
        self.extraction_results.update(extraction_results)

        return results

    def combine_extracted_data(self, source_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        Combine extracted data from multiple sources into single DataFrame.

        Args:
            source_data: Dictionary mapping source names to DataFrames

        Returns:
            Combined DataFrame with all medication data
        """
        combined_dfs = []

        for source_name, df in source_data.items():
            if not df.empty:
                # Ensure source_system column is set
                df['source_system'] = source_name
                combined_dfs.append(df)
                self.logger.info(f"Added {len(df)} records from {source_name}")

        if not combined_dfs:
            self.logger.warning("No data extracted from any source")
            return pd.DataFrame()

        combined_df = pd.concat(combined_dfs, ignore_index=True)

        self.logger.info(
            f"Combined data from {len(combined_dfs)} sources: {len(combined_df)} total records"
        )

        return combined_df

    def get_extraction_summary(self) -> Dict[str, Any]:
        """
        Get summary of extraction results.

        Returns:
            Dictionary with extraction summary statistics
        """
        summary = {
            'total_sources': len(self.extraction_results),
            'successful_sources': len([r for r in self.extraction_results.values() if r['status'] == 'success']),
            'failed_sources': len([r for r in self.extraction_results.values() if r['status'] == 'failed']),
            'total_records': sum([r['record_count'] for r in self.extraction_results.values()]),
            'source_details': self.extraction_results.copy()
        }

        return summary

    def generate_extraction_report(self, output_path: Optional[str] = None) -> str:
        """
        Generate a detailed extraction report.

        Args:
            output_path: Path to save the report (optional)

        Returns:
            Report text
        """
        summary = self.get_extraction_summary()

        report_lines = [
            "# Medication Data Extraction Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## Summary",
            f"- Total Sources: {summary['total_sources']}",
            f"- Successful: {summary['successful_sources']}",
            f"- Failed: {summary['failed_sources']}",
            f"- Total Records: {summary['total_records']:,}",
            "",
            "## Source Details"
        ]

        for source_name, details in summary['source_details'].items():
            report_lines.extend([
                f"### {source_name}",
                f"- Status: {details['status']}",
                f"- Records: {details['record_count']:,}"
            ])

            if details['status'] == 'failed':
                report_lines.append(f"- Error: {details.get('error', 'Unknown error')}")
            elif 'stats' in details:
                stats = details['stats']
                if stats.get('start_time') and stats.get('end_time'):
                    duration = (stats['end_time'] - stats['start_time']).total_seconds()
                    report_lines.append(f"- Duration: {duration:.2f} seconds")

            report_lines.append("")

        report_text = "\n".join(report_lines)

        if output_path:
            with open(output_path, 'w') as f:
                f.write(report_text)
            self.logger.info(f"Extraction report saved to {output_path}")

        return report_text


def run_extraction_pipeline(
    config_path: Optional[str] = None,
    source_names: Optional[List[str]] = None,
    parallel: bool = True,
    output_dir: Optional[str] = None
) -> Dict[str, Any]:
    """
    Convenience function to run the complete extraction pipeline.

    Args:
        config_path: Path to configuration file
        source_names: List of sources to extract (None = all enabled)
        parallel: Whether to run extractions in parallel
        output_dir: Directory to save output files

    Returns:
        Dictionary with extraction results and file paths
    """
    orchestrator = ExtractionOrchestrator(config_path)

    # Extract data
    source_data = orchestrator.extract_from_sources(source_names, parallel)

    # Combine data
    combined_df = orchestrator.combine_extracted_data(source_data)

    # Generate summary
    summary = orchestrator.get_extraction_summary()

    results = {
        'status': 'success' if summary['failed_sources'] == 0 else 'partial',
        'summary': summary,
        'combined_data': combined_df,
        'source_data': source_data
    }

    # Save outputs if directory specified
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save combined data
        if not combined_df.empty:
            combined_file = output_path / "extracted_medications_combined.parquet"
            combined_df.to_parquet(combined_file, index=False)
            results['combined_file'] = str(combined_file)

        # Save individual source data
        source_files = {}
        for source_name, df in source_data.items():
            if not df.empty:
                source_file = output_path / f"extracted_medications_{source_name}.parquet"
                df.to_parquet(source_file, index=False)
                source_files[source_name] = str(source_file)

        results['source_files'] = source_files

        # Save extraction report
        report_file = output_path / "extraction_report.md"
        orchestrator.generate_extraction_report(str(report_file))
        results['report_file'] = str(report_file)

    return results