#!/usr/bin/env python3
"""
Database Connection Tester

Tests connectivity to all configured data sources and the data warehouse
to ensure the ETL pipeline can run successfully.
"""

import sys
import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List
import sqlalchemy as sa
from sqlalchemy import create_engine, text

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.database_connector import DatabaseConnector


class ConnectionTester:
    """Tests database connections for the medication normalization pipeline."""

    def __init__(self, config_path: str):
        """Initialize connection tester with configuration."""
        self.config = self._load_config(config_path)
        self.test_results = {}

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        config_file = Path(config_path)

        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        if config_file.suffix.lower() in ['.yaml', '.yml']:
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        else:
            with open(config_file, 'r') as f:
                return json.load(f)

    def test_all_connections(self) -> bool:
        """Test all configured database connections."""
        print("Testing database connections...")
        print("=" * 50)

        all_passed = True

        # Test source databases
        source_connections = [
            ('MAP Pro Web', 'map_pro_web'),
            ('MAP Pro Mobile', 'map_pro_mobile'),
            ('PioneerRx', 'pioneer_rx'),
            ('AlloCare', 'allocare')
        ]

        for display_name, config_key in source_connections:
            if self._is_source_enabled(config_key):
                success = self._test_source_connection(display_name, config_key)
                all_passed = all_passed and success
            else:
                print(f"⏭️  {display_name}: Disabled in configuration")

        # Test data warehouse connection if configured
        if self._has_data_warehouse_config():
            success = self._test_data_warehouse_connection()
            all_passed = all_passed and success

        print("\n" + "=" * 50)
        if all_passed:
            print("✅ All enabled connections tested successfully!")
        else:
            print("❌ Some connections failed. Check configuration and network connectivity.")

        return all_passed

    def _is_source_enabled(self, source_key: str) -> bool:
        """Check if a source is enabled in configuration."""
        sources_config = self.config.get('sources', {})
        return sources_config.get(source_key, {}).get('enabled', False)

    def _has_data_warehouse_config(self) -> bool:
        """Check if data warehouse connection is configured."""
        return 'data_warehouse_connection' in self.config

    def _test_source_connection(self, display_name: str, config_key: str) -> bool:
        """Test connection to a source database."""
        try:
            # Load source-specific configuration
            source_config_file = self.config.get('sources', {}).get(config_key, {}).get('config_file')

            if source_config_file:
                source_config_path = Path(__file__).parent.parent / source_config_file
                with open(source_config_path, 'r') as f:
                    source_configs = yaml.safe_load(f)

                connection_config = source_configs.get('databases', {}).get(config_key, {})
            else:
                connection_config = self.config.get('databases', {}).get(config_key, {})

            if not connection_config:
                print(f"❌ {display_name}: No connection configuration found")
                return False

            # Build connection string
            connection_string = self._build_connection_string(connection_config)

            if not connection_string:
                print(f"❌ {display_name}: Invalid connection configuration")
                return False

            # Test connection
            engine = create_engine(connection_string, connect_args={'connect_timeout': 10})

            with engine.connect() as conn:
                # Test basic query
                result = conn.execute(text("SELECT 1 as test"))
                test_value = result.scalar()

                if test_value == 1:
                    print(f"✅ {display_name}: Connection successful")
                    self.test_results[config_key] = {'status': 'success', 'error': None}
                    return True
                else:
                    print(f"❌ {display_name}: Unexpected query result")
                    return False

        except Exception as e:
            error_msg = str(e)
            print(f"❌ {display_name}: Connection failed - {error_msg}")
            self.test_results[config_key] = {'status': 'failed', 'error': error_msg}
            return False

        finally:
            try:
                if 'engine' in locals():
                    engine.dispose()
            except:
                pass

    def _test_data_warehouse_connection(self) -> bool:
        """Test connection to data warehouse."""
        try:
            dw_config = self.config.get('data_warehouse_connection', {})
            connection_string = dw_config.get('connection_string')

            if not connection_string:
                # Try to build from components
                connection_string = self._build_connection_string(dw_config)

            if not connection_string:
                print("❌ Data Warehouse: No connection string configured")
                return False

            # Substitute environment variables
            connection_string = os.path.expandvars(connection_string)

            engine = create_engine(connection_string, connect_args={'connect_timeout': 10})

            with engine.connect() as conn:
                # Test basic query
                result = conn.execute(text("SELECT 1 as test"))
                test_value = result.scalar()

                if test_value == 1:
                    print("✅ Data Warehouse: Connection successful")

                    # Test schema access
                    target_schema = self.config.get('omop', {}).get('target_schema', 'omop_cdm')
                    try:
                        conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {target_schema}"))
                        print(f"✅ Data Warehouse: Schema access verified ({target_schema})")
                    except Exception as e:
                        print(f"⚠️  Data Warehouse: Schema access limited - {str(e)}")

                    return True
                else:
                    print("❌ Data Warehouse: Unexpected query result")
                    return False

        except Exception as e:
            error_msg = str(e)
            print(f"❌ Data Warehouse: Connection failed - {error_msg}")
            return False

        finally:
            try:
                if 'engine' in locals():
                    engine.dispose()
            except:
                pass

    def _build_connection_string(self, config: Dict[str, Any]) -> str:
        """Build database connection string from configuration."""
        try:
            # Check if connection string is provided directly
            if 'connection_string' in config:
                return os.path.expandvars(config['connection_string'])

            # Build from components
            host = config.get('host', 'localhost')
            port = config.get('port', 5432)
            database = config.get('database')
            username = config.get('username')
            password = config.get('password')

            # Substitute environment variables
            host = os.path.expandvars(str(host))
            database = os.path.expandvars(str(database)) if database else None
            username = os.path.expandvars(str(username)) if username else None
            password = os.path.expandvars(str(password)) if password else None

            if not all([host, database, username]):
                return None

            # Build PostgreSQL connection string
            if password:
                connection_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"
            else:
                connection_string = f"postgresql://{username}@{host}:{port}/{database}"

            return connection_string

        except Exception:
            return None

    def test_rxnorm_api(self) -> bool:
        """Test RxNorm API connectivity."""
        try:
            import requests

            print("\n🔍 Testing RxNorm API connectivity...")

            # Test basic RxNorm API endpoint
            test_url = "https://rxnav.nlm.nih.gov/REST/rxcui.json?name=aspirin"
            response = requests.get(test_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if 'idGroup' in data and 'rxnormId' in data['idGroup']:
                    print("✅ RxNorm API: Connection successful")
                    return True
                else:
                    print("⚠️  RxNorm API: Unexpected response format")
                    return False
            else:
                print(f"❌ RxNorm API: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ RxNorm API: Connection failed - {str(e)}")
            return False

    def generate_connection_report(self) -> Dict[str, Any]:
        """Generate detailed connection test report."""
        return {
            'timestamp': str(pd.Timestamp.now()),
            'test_results': self.test_results,
            'summary': {
                'total_tests': len(self.test_results),
                'successful': sum(1 for r in self.test_results.values() if r['status'] == 'success'),
                'failed': sum(1 for r in self.test_results.values() if r['status'] == 'failed')
            }
        }


def main():
    """Main entry point for connection testing."""
    import argparse

    parser = argparse.ArgumentParser(description="Test database connections for medication normalization pipeline")

    parser.add_argument(
        '--config',
        default='../config/pipeline_config.yaml',
        help='Configuration file path'
    )

    parser.add_argument(
        '--test-rxnorm',
        action='store_true',
        help='Also test RxNorm API connectivity'
    )

    parser.add_argument(
        '--generate-report',
        help='Generate detailed report file'
    )

    args = parser.parse_args()

    try:
        # Initialize tester
        tester = ConnectionTester(args.config)

        # Test database connections
        db_success = tester.test_all_connections()

        # Test RxNorm API if requested
        rxnorm_success = True
        if args.test_rxnorm:
            rxnorm_success = tester.test_rxnorm_api()

        # Generate report if requested
        if args.generate_report:
            report = tester.generate_connection_report()
            with open(args.generate_report, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\n📄 Report saved to: {args.generate_report}")

        # Exit with appropriate code
        if db_success and rxnorm_success:
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Connection testing failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()