#!/usr/bin/env python3
"""
Data Warehouse Loader for OMOP CDM Tables

Loads generated OMOP tables into Curtis's data warehouse with proper
schema validation, type conversion, and incremental loading support.
"""

import sys
import argparse
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd
import sqlalchemy as sa
from sqlalchemy import create_engine, text, MetaData, Table, Column
from sqlalchemy.exc import SQLAlchemyError

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.logger import LoggerMixin
from src.utils.database_connector import DatabaseConnector


class DataWarehouseLoader(LoggerMixin):
    """
    Loads OMOP CDM tables into data warehouse.

    Supports full and incremental loading strategies with proper
    schema validation and data type handling.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize data warehouse loader."""
        self.config = config
        self.dw_config = config.get('data_warehouse', {})

        # Connection settings
        self.target_schema = self.dw_config.get('target_schema', 'omop_cdm')
        self.staging_schema = self.dw_config.get('staging_schema', 'staging')
        self.load_strategy = self.dw_config.get('load_strategy', 'incremental')

        # Initialize database connection
        self.db_connector = DatabaseConnector(config)
        self.engine = None

        # Loading statistics
        self.load_stats = {
            'start_time': None,
            'end_time': None,
            'tables_processed': 0,
            'total_records_loaded': 0,
            'tables_failed': 0,
            'errors': []
        }

        # OMOP table schemas
        self.omop_schemas = self._define_omop_schemas()

    def _define_omop_schemas(self) -> Dict[str, Dict[str, str]]:
        """Define OMOP CDM table schemas."""
        return {
            'DRUG_EXPOSURE': {
                'drug_exposure_id': 'BIGINT PRIMARY KEY',
                'person_id': 'BIGINT NOT NULL',
                'drug_concept_id': 'INTEGER NOT NULL',
                'drug_exposure_start_date': 'DATE NOT NULL',
                'drug_exposure_start_datetime': 'TIMESTAMP',
                'drug_exposure_end_date': 'DATE',
                'drug_exposure_end_datetime': 'TIMESTAMP',
                'verbatim_end_date': 'DATE',
                'drug_type_concept_id': 'INTEGER NOT NULL',
                'stop_reason': 'VARCHAR(20)',
                'refills': 'INTEGER',
                'quantity': 'NUMERIC',
                'days_supply': 'INTEGER',
                'sig': 'TEXT',
                'route_concept_id': 'INTEGER',
                'lot_number': 'VARCHAR(50)',
                'provider_id': 'BIGINT',
                'visit_occurrence_id': 'BIGINT',
                'visit_detail_id': 'BIGINT',
                'drug_source_value': 'VARCHAR(50)',
                'drug_source_concept_id': 'INTEGER',
                'route_source_value': 'VARCHAR(50)',
                'dose_unit_source_value': 'VARCHAR(50)',
                'dose_value': 'NUMERIC',
                'dose_unit_concept_id': 'INTEGER',
                'modifier_concept_id': 'INTEGER',
                'operator_concept_id': 'INTEGER',
                'value_as_number': 'NUMERIC',
                'value_as_concept_id': 'INTEGER',
                'unit_concept_id': 'INTEGER',
                'unit_source_value': 'VARCHAR(50)',
                'unit_source_concept_id': 'INTEGER',
                'value_source_value': 'VARCHAR(50)'
            },
            'DRUG_ERA': {
                'drug_era_id': 'BIGINT PRIMARY KEY',
                'person_id': 'BIGINT NOT NULL',
                'drug_concept_id': 'INTEGER NOT NULL',
                'drug_era_start_date': 'DATE NOT NULL',
                'drug_era_end_date': 'DATE NOT NULL',
                'drug_exposure_count': 'INTEGER',
                'gap_days': 'INTEGER'
            },
            'DOSE_ERA': {
                'dose_era_id': 'BIGINT PRIMARY KEY',
                'person_id': 'BIGINT NOT NULL',
                'drug_concept_id': 'INTEGER NOT NULL',
                'unit_concept_id': 'INTEGER NOT NULL',
                'dose_value': 'NUMERIC NOT NULL',
                'dose_era_start_date': 'DATE NOT NULL',
                'dose_era_end_date': 'DATE NOT NULL'
            },
            'CONCEPT': {
                'concept_id': 'INTEGER PRIMARY KEY',
                'concept_name': 'VARCHAR(255) NOT NULL',
                'domain_id': 'VARCHAR(20) NOT NULL',
                'vocabulary_id': 'VARCHAR(20) NOT NULL',
                'concept_class_id': 'VARCHAR(20) NOT NULL',
                'standard_concept': 'VARCHAR(1)',
                'concept_code': 'VARCHAR(50) NOT NULL',
                'valid_start_date': 'DATE NOT NULL',
                'valid_end_date': 'DATE NOT NULL',
                'invalid_reason': 'VARCHAR(1)'
            },
            'CONCEPT_RELATIONSHIP': {
                'concept_id_1': 'INTEGER NOT NULL',
                'concept_id_2': 'INTEGER NOT NULL',
                'relationship_id': 'VARCHAR(20) NOT NULL',
                'valid_start_date': 'DATE NOT NULL',
                'valid_end_date': 'DATE NOT NULL',
                'invalid_reason': 'VARCHAR(1)'
            },
            'VOCABULARY': {
                'vocabulary_id': 'VARCHAR(20) PRIMARY KEY',
                'vocabulary_name': 'VARCHAR(255) NOT NULL',
                'vocabulary_reference': 'VARCHAR(255)',
                'vocabulary_version': 'VARCHAR(255)',
                'vocabulary_concept_id': 'INTEGER NOT NULL'
            }
        }

    def load_omop_tables(
        self,
        input_dir: str,
        target_schema: Optional[str] = None,
        load_strategy: Optional[str] = None,
        validate_before_load: bool = True,
        create_indexes: bool = True
    ) -> Dict[str, Any]:
        """
        Load OMOP tables into data warehouse.

        Args:
            input_dir: Directory containing OMOP parquet files
            target_schema: Target database schema
            load_strategy: 'full' or 'incremental'
            validate_before_load: Validate data before loading
            create_indexes: Create indexes after loading

        Returns:
            Loading results and statistics
        """
        self.load_stats['start_time'] = datetime.now()

        # Override defaults if provided
        if target_schema:
            self.target_schema = target_schema
        if load_strategy:
            self.load_strategy = load_strategy

        self.logger.info(f"Starting data warehouse load to schema: {self.target_schema}")
        self.logger.info(f"Load strategy: {self.load_strategy}")

        try:
            # Initialize database connection
            self._initialize_connection()

            # Create schemas if needed
            self._create_schemas()

            # Discover OMOP files
            omop_files = self._discover_omop_files(input_dir)

            if not omop_files:
                return self._create_empty_result("No OMOP files found in input directory")

            # Process each table
            load_results = {}

            for table_name, file_path in omop_files.items():
                try:
                    self.logger.info(f"Loading table: {table_name}")

                    # Load and validate data
                    df = pd.read_parquet(file_path)

                    if validate_before_load:
                        self._validate_table_data(df, table_name)

                    # Load to database
                    records_loaded = self._load_table(df, table_name)

                    load_results[table_name] = {
                        'status': 'SUCCESS',
                        'records_loaded': records_loaded,
                        'file_path': str(file_path)
                    }

                    self.load_stats['total_records_loaded'] += records_loaded
                    self.load_stats['tables_processed'] += 1

                    self.logger.info(f"Successfully loaded {table_name}: {records_loaded} records")

                except Exception as e:
                    error_msg = f"Failed to load {table_name}: {str(e)}"
                    self.logger.error(error_msg)

                    load_results[table_name] = {
                        'status': 'FAILED',
                        'error': error_msg,
                        'file_path': str(file_path)
                    }

                    self.load_stats['tables_failed'] += 1
                    self.load_stats['errors'].append(error_msg)
                    continue

            # Create indexes if requested
            if create_indexes and self.load_stats['tables_processed'] > 0:
                self._create_indexes(load_results.keys())

            # Update load statistics
            self.load_stats['end_time'] = datetime.now()

            # Generate final results
            return self._generate_load_results(load_results)

        except Exception as e:
            self.load_stats['end_time'] = datetime.now()
            self.logger.error(f"Data warehouse loading failed: {str(e)}")
            raise

        finally:
            if self.engine:
                self.engine.dispose()

    def _initialize_connection(self) -> None:
        """Initialize database connection."""
        try:
            connection_config = self.config.get('data_warehouse_connection', {})

            if not connection_config:
                # Use default database connection
                self.engine = self.db_connector.get_engine('default')
            else:
                # Create specific data warehouse connection
                connection_string = connection_config.get('connection_string')
                if not connection_string:
                    raise ValueError("Data warehouse connection string not configured")

                self.engine = create_engine(connection_string)

            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            self.logger.info("Database connection established")

        except Exception as e:
            raise RuntimeError(f"Failed to connect to data warehouse: {str(e)}")

    def _create_schemas(self) -> None:
        """Create target and staging schemas if they don't exist."""
        schemas_to_create = [self.target_schema]

        if self.load_strategy == 'incremental':
            schemas_to_create.append(self.staging_schema)

        with self.engine.connect() as conn:
            for schema in schemas_to_create:
                try:
                    conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {schema}"))
                    conn.commit()
                    self.logger.info(f"Ensured schema exists: {schema}")
                except SQLAlchemyError as e:
                    self.logger.warning(f"Could not create schema {schema}: {str(e)}")

    def _discover_omop_files(self, input_dir: str) -> Dict[str, Path]:
        """Discover OMOP parquet files in input directory."""
        input_path = Path(input_dir)

        if not input_path.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")

        omop_files = {}

        # Look for standard OMOP table files
        for table_name in self.omop_schemas.keys():
            parquet_file = input_path / f"{table_name}.parquet"
            if parquet_file.exists():
                omop_files[table_name] = parquet_file

        self.logger.info(f"Discovered {len(omop_files)} OMOP table files")
        return omop_files

    def _validate_table_data(self, df: pd.DataFrame, table_name: str) -> None:
        """Validate table data before loading."""
        if df.empty:
            raise ValueError(f"Table {table_name} is empty")

        # Check if table schema is known
        if table_name not in self.omop_schemas:
            self.logger.warning(f"Unknown table schema: {table_name}")
            return

        schema = self.omop_schemas[table_name]

        # Validate required columns exist
        required_columns = [
            col for col, definition in schema.items()
            if 'NOT NULL' in definition and 'PRIMARY KEY' not in definition
        ]

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in {table_name}: {missing_columns}")

        # Check for null values in required columns
        for col in required_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    raise ValueError(f"Null values in required column {table_name}.{col}: {null_count}")

        self.logger.info(f"Validation passed for {table_name}")

    def _load_table(self, df: pd.DataFrame, table_name: str) -> int:
        """Load a single table to the database."""
        if self.load_strategy == 'full':
            return self._load_table_full(df, table_name)
        else:
            return self._load_table_incremental(df, table_name)

    def _load_table_full(self, df: pd.DataFrame, table_name: str) -> int:
        """Load table using full replacement strategy."""
        target_table = f"{self.target_schema}.{table_name}"

        with self.engine.connect() as conn:
            # Drop and recreate table
            conn.execute(text(f"DROP TABLE IF EXISTS {target_table}"))

            # Create table with proper schema
            self._create_table(conn, table_name, self.target_schema)

            # Load data
            df.to_sql(
                table_name,
                conn,
                schema=self.target_schema,
                if_exists='append',
                index=False,
                method='multi',
                chunksize=1000
            )

            conn.commit()

        return len(df)

    def _load_table_incremental(self, df: pd.DataFrame, table_name: str) -> int:
        """Load table using incremental strategy."""
        staging_table = f"{self.staging_schema}.{table_name}_staging"
        target_table = f"{self.target_schema}.{table_name}"

        with self.engine.connect() as conn:
            # Create staging table
            conn.execute(text(f"DROP TABLE IF EXISTS {staging_table}"))
            self._create_table(conn, f"{table_name}_staging", self.staging_schema)

            # Load data to staging
            df.to_sql(
                f"{table_name}_staging",
                conn,
                schema=self.staging_schema,
                if_exists='append',
                index=False,
                method='multi',
                chunksize=1000
            )

            # Create target table if it doesn't exist
            table_exists = conn.execute(text(
                f"SELECT COUNT(*) FROM information_schema.tables "
                f"WHERE table_schema = '{self.target_schema}' "
                f"AND table_name = '{table_name}'"
            )).scalar()

            if not table_exists:
                self._create_table(conn, table_name, self.target_schema)

            # Merge staging data to target
            records_merged = self._merge_staging_to_target(conn, table_name)

            # Clean up staging table
            conn.execute(text(f"DROP TABLE {staging_table}"))
            conn.commit()

        return records_merged

    def _create_table(self, conn, table_name: str, schema: str) -> None:
        """Create table with proper OMOP schema."""
        base_table_name = table_name.replace('_staging', '')

        if base_table_name not in self.omop_schemas:
            raise ValueError(f"Unknown table schema: {base_table_name}")

        schema_def = self.omop_schemas[base_table_name]

        # Build CREATE TABLE statement
        columns = []
        for col_name, col_definition in schema_def.items():
            columns.append(f"{col_name} {col_definition}")

        create_sql = f"""
        CREATE TABLE {schema}.{table_name} (
            {', '.join(columns)}
        )
        """

        conn.execute(text(create_sql))
        self.logger.info(f"Created table: {schema}.{table_name}")

    def _merge_staging_to_target(self, conn, table_name: str) -> int:
        """Merge staging data to target table."""
        staging_table = f"{self.staging_schema}.{table_name}_staging"
        target_table = f"{self.target_schema}.{table_name}"

        # Get primary key column
        schema = self.omop_schemas[table_name]
        pk_columns = [
            col for col, definition in schema.items()
            if 'PRIMARY KEY' in definition
        ]

        if not pk_columns:
            # For tables without explicit primary key, do simple insert
            merge_sql = f"""
            INSERT INTO {target_table}
            SELECT * FROM {staging_table}
            """
        else:
            # For tables with primary key, do upsert
            pk_col = pk_columns[0]
            all_columns = list(schema.keys())
            non_pk_columns = [col for col in all_columns if col != pk_col]

            merge_sql = f"""
            INSERT INTO {target_table} ({', '.join(all_columns)})
            SELECT {', '.join(all_columns)}
            FROM {staging_table}
            ON CONFLICT ({pk_col}) DO UPDATE SET
            {', '.join([f"{col} = EXCLUDED.{col}" for col in non_pk_columns])}
            """

        result = conn.execute(text(merge_sql))
        return result.rowcount

    def _create_indexes(self, table_names: List[str]) -> None:
        """Create indexes on loaded tables."""
        index_definitions = {
            'DRUG_EXPOSURE': [
                'person_id',
                'drug_concept_id',
                'drug_exposure_start_date'
            ],
            'DRUG_ERA': [
                'person_id',
                'drug_concept_id'
            ],
            'DOSE_ERA': [
                'person_id',
                'drug_concept_id',
                'dose_value'
            ]
        }

        with self.engine.connect() as conn:
            for table_name in table_names:
                if table_name in index_definitions:
                    for column in index_definitions[table_name]:
                        try:
                            index_name = f"idx_{table_name}_{column}"
                            index_sql = f"""
                            CREATE INDEX IF NOT EXISTS {index_name}
                            ON {self.target_schema}.{table_name} ({column})
                            """
                            conn.execute(text(index_sql))
                            self.logger.info(f"Created index: {index_name}")
                        except SQLAlchemyError as e:
                            self.logger.warning(f"Failed to create index on {table_name}.{column}: {str(e)}")

            conn.commit()

    def _generate_load_results(self, load_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final load results."""
        execution_time = (self.load_stats['end_time'] - self.load_stats['start_time']).total_seconds()

        return {
            'status': 'SUCCESS' if self.load_stats['tables_failed'] == 0 else 'PARTIAL_SUCCESS',
            'execution_summary': {
                'start_time': self.load_stats['start_time'].isoformat(),
                'end_time': self.load_stats['end_time'].isoformat(),
                'execution_time_seconds': execution_time,
                'tables_processed': self.load_stats['tables_processed'],
                'tables_failed': self.load_stats['tables_failed'],
                'total_records_loaded': self.load_stats['total_records_loaded']
            },
            'table_results': load_results,
            'target_schema': self.target_schema,
            'load_strategy': self.load_strategy,
            'errors': self.load_stats['errors'] if self.load_stats['errors'] else None
        }

    def _create_empty_result(self, message: str) -> Dict[str, Any]:
        """Create empty result with message."""
        return {
            'status': 'NO_DATA',
            'message': message,
            'execution_summary': {
                'tables_processed': 0,
                'total_records_loaded': 0
            }
        }


def main():
    """Main entry point for the data warehouse loader."""
    parser = argparse.ArgumentParser(
        description="Load OMOP CDM tables into data warehouse",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument(
        '--input-dir',
        required=True,
        help='Directory containing OMOP parquet files'
    )

    parser.add_argument(
        '--config',
        default='../config/pipeline_config.yaml',
        help='Configuration file path'
    )

    parser.add_argument(
        '--target-schema',
        default='omop_cdm',
        help='Target database schema'
    )

    parser.add_argument(
        '--load-strategy',
        choices=['full', 'incremental'],
        default='incremental',
        help='Loading strategy'
    )

    parser.add_argument(
        '--validate-before-load',
        action='store_true',
        default=True,
        help='Validate data before loading'
    )

    parser.add_argument(
        '--create-indexes',
        action='store_true',
        default=True,
        help='Create indexes after loading'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    try:
        # Load configuration
        config_path = Path(args.config)
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
        else:
            with open(config_path, 'r') as f:
                config = json.load(f)

        # Initialize loader
        loader = DataWarehouseLoader(config)

        # Run loading
        results = loader.load_omop_tables(
            input_dir=args.input_dir,
            target_schema=args.target_schema,
            load_strategy=args.load_strategy,
            validate_before_load=args.validate_before_load,
            create_indexes=args.create_indexes
        )

        # Print results
        print("\n" + "="*60)
        print("DATA WAREHOUSE LOADING RESULTS")
        print("="*60)
        print(f"Status: {results['status']}")
        print(f"Tables Processed: {results['execution_summary']['tables_processed']}")
        print(f"Records Loaded: {results['execution_summary']['total_records_loaded']:,}")
        print(f"Execution Time: {results['execution_summary']['execution_time_seconds']:.2f} seconds")

        if results.get('table_results'):
            print("\nTable Loading Results:")
            for table_name, table_result in results['table_results'].items():
                status = table_result['status']
                if status == 'SUCCESS':
                    records = table_result['records_loaded']
                    print(f"  ✅ {table_name}: {records:,} records")
                else:
                    error = table_result.get('error', 'Unknown error')
                    print(f"  ❌ {table_name}: {error}")

        if results.get('errors'):
            print(f"\nErrors: {len(results['errors'])}")
            for error in results['errors']:
                print(f"  - {error}")

        if results['status'] == 'SUCCESS':
            print("\n✅ Data warehouse loading completed successfully!")
            sys.exit(0)
        else:
            print("\n⚠️ Data warehouse loading completed with issues.")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Data warehouse loading failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()