"""
OMOP CDM compliance validation.
"""

import pandas as pd
from typing import Dict, Any, List, Optional, Set
from datetime import datetime

from ..utils.logger import LoggerMixin
from ..utils.constants import OMOP_STANDARD_CONCEPTS, ValidationSeverity


class OMOPValidator(LoggerMixin):
    """
    Validates OMOP CDM compliance and data quality.

    Ensures transformed data meets OMOP CDM standards and
    follows proper vocabulary conventions.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize OMOP validator.

        Args:
            config: OMOP configuration dictionary
        """
        self.config = config
        self.omop_config = config.get('omop', {})
        self.cdm_version = self.omop_config.get('cdm', {}).get('version', '6.0')

        # OMOP CDM table schemas
        self.drug_exposure_schema = self._get_drug_exposure_schema()
        self.drug_era_schema = self._get_drug_era_schema()
        self.dose_era_schema = self._get_dose_era_schema()

        # Standard concept validation
        self.standard_concepts = OMOP_STANDARD_CONCEPTS

    def _get_drug_exposure_schema(self) -> Dict[str, Dict[str, Any]]:
        """Get DRUG_EXPOSURE table schema requirements."""
        return {
            'drug_exposure_id': {
                'required': True,
                'data_type': 'integer',
                'unique': True,
                'description': 'Unique identifier for drug exposure record'
            },
            'person_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Foreign key to person'
            },
            'drug_concept_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Standard concept ID for drug'
            },
            'drug_exposure_start_date': {
                'required': True,
                'data_type': 'date',
                'description': 'Start date of drug exposure'
            },
            'drug_exposure_end_date': {
                'required': False,
                'data_type': 'date',
                'description': 'End date of drug exposure'
            },
            'drug_type_concept_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Type of drug exposure'
            },
            'dose_value': {
                'required': False,
                'data_type': 'float',
                'description': 'Numeric value of dose'
            },
            'dose_unit_concept_id': {
                'required': False,
                'data_type': 'integer',
                'description': 'Standard concept ID for dose unit'
            },
            'route_concept_id': {
                'required': False,
                'data_type': 'integer',
                'description': 'Standard concept ID for route'
            },
            'drug_source_value': {
                'required': False,
                'data_type': 'string',
                'description': 'Original drug value from source'
            },
            'drug_source_concept_id': {
                'required': False,
                'data_type': 'integer',
                'description': 'Source concept ID for drug'
            }
        }

    def _get_drug_era_schema(self) -> Dict[str, Dict[str, Any]]:
        """Get DRUG_ERA table schema requirements."""
        return {
            'drug_era_id': {
                'required': True,
                'data_type': 'integer',
                'unique': True,
                'description': 'Unique identifier for drug era'
            },
            'person_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Foreign key to person'
            },
            'drug_concept_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Standard concept ID for drug'
            },
            'drug_era_start_date': {
                'required': True,
                'data_type': 'date',
                'description': 'Start date of drug era'
            },
            'drug_era_end_date': {
                'required': True,
                'data_type': 'date',
                'description': 'End date of drug era'
            },
            'drug_exposure_count': {
                'required': False,
                'data_type': 'integer',
                'description': 'Number of exposures in era'
            },
            'gap_days': {
                'required': False,
                'data_type': 'integer',
                'description': 'Gap days used in era construction'
            }
        }

    def _get_dose_era_schema(self) -> Dict[str, Dict[str, Any]]:
        """Get DOSE_ERA table schema requirements."""
        return {
            'dose_era_id': {
                'required': True,
                'data_type': 'integer',
                'unique': True,
                'description': 'Unique identifier for dose era'
            },
            'person_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Foreign key to person'
            },
            'drug_concept_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Standard concept ID for drug'
            },
            'unit_concept_id': {
                'required': True,
                'data_type': 'integer',
                'description': 'Standard concept ID for unit'
            },
            'dose_value': {
                'required': True,
                'data_type': 'float',
                'description': 'Numeric dose value'
            },
            'dose_era_start_date': {
                'required': True,
                'data_type': 'date',
                'description': 'Start date of dose era'
            },
            'dose_era_end_date': {
                'required': True,
                'data_type': 'date',
                'description': 'End date of dose era'
            }
        }

    def validate_drug_exposure_table(self, drug_exposure_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate DRUG_EXPOSURE table compliance.

        Args:
            drug_exposure_df: DataFrame with DRUG_EXPOSURE data

        Returns:
            Validation results
        """
        if drug_exposure_df.empty:
            return {
                'status': 'skipped',
                'message': 'Empty DRUG_EXPOSURE table'
            }

        self.logger.info(f"Validating DRUG_EXPOSURE table with {len(drug_exposure_df)} records")

        validation_results = {}

        # 1. Schema compliance
        validation_results['schema_compliance'] = self._validate_table_schema(
            drug_exposure_df, self.drug_exposure_schema, 'DRUG_EXPOSURE'
        )

        # 2. Required field completeness
        validation_results['required_fields'] = self._validate_required_fields(
            drug_exposure_df, self.drug_exposure_schema
        )

        # 3. Data type compliance
        validation_results['data_types'] = self._validate_data_types(
            drug_exposure_df, self.drug_exposure_schema
        )

        # 4. Foreign key integrity
        validation_results['foreign_keys'] = self._validate_foreign_key_integrity(drug_exposure_df)

        # 5. Standard concept validation
        validation_results['standard_concepts'] = self._validate_standard_concepts(drug_exposure_df)

        # 6. Date logic validation
        validation_results['date_logic'] = self._validate_date_logic(drug_exposure_df)

        # 7. Business rule validation
        validation_results['business_rules'] = self._validate_business_rules(drug_exposure_df)

        # Determine overall status
        overall_status = self._determine_validation_status(validation_results)

        return {
            'table': 'DRUG_EXPOSURE',
            'status': overall_status,
            'record_count': len(drug_exposure_df),
            'validation_results': validation_results,
            'summary': self._generate_table_summary(validation_results, len(drug_exposure_df))
        }

    def validate_drug_era_table(self, drug_era_df: pd.DataFrame) -> Dict[str, Any]:
        """Validate DRUG_ERA table compliance."""
        if drug_era_df.empty:
            return {
                'status': 'skipped',
                'message': 'Empty DRUG_ERA table'
            }

        self.logger.info(f"Validating DRUG_ERA table with {len(drug_era_df)} records")

        validation_results = {}

        # Schema and basic validations
        validation_results['schema_compliance'] = self._validate_table_schema(
            drug_era_df, self.drug_era_schema, 'DRUG_ERA'
        )

        validation_results['required_fields'] = self._validate_required_fields(
            drug_era_df, self.drug_era_schema
        )

        # Era-specific validations
        validation_results['era_logic'] = self._validate_era_logic(drug_era_df)

        overall_status = self._determine_validation_status(validation_results)

        return {
            'table': 'DRUG_ERA',
            'status': overall_status,
            'record_count': len(drug_era_df),
            'validation_results': validation_results
        }

    def validate_dose_era_table(self, dose_era_df: pd.DataFrame) -> Dict[str, Any]:
        """Validate DOSE_ERA table compliance."""
        if dose_era_df.empty:
            return {
                'status': 'skipped',
                'message': 'Empty DOSE_ERA table'
            }

        self.logger.info(f"Validating DOSE_ERA table with {len(dose_era_df)} records")

        validation_results = {}

        # Schema and basic validations
        validation_results['schema_compliance'] = self._validate_table_schema(
            dose_era_df, self.dose_era_schema, 'DOSE_ERA'
        )

        validation_results['required_fields'] = self._validate_required_fields(
            dose_era_df, self.dose_era_schema
        )

        # Dose-specific validations
        validation_results['dose_logic'] = self._validate_dose_logic(dose_era_df)

        overall_status = self._determine_validation_status(validation_results)

        return {
            'table': 'DOSE_ERA',
            'status': overall_status,
            'record_count': len(dose_era_df),
            'validation_results': validation_results
        }

    def _validate_table_schema(
        self,
        df: pd.DataFrame,
        schema: Dict[str, Dict[str, Any]],
        table_name: str
    ) -> Dict[str, Any]:
        """Validate table schema compliance."""
        missing_columns = []
        extra_columns = []
        schema_columns = set(schema.keys())
        df_columns = set(df.columns)

        # Check for missing required columns
        for col, spec in schema.items():
            if spec.get('required', False) and col not in df_columns:
                missing_columns.append(col)

        # Check for extra columns (informational)
        extra_columns = list(df_columns - schema_columns)

        status = 'failed' if missing_columns else 'passed'

        return {
            'status': status,
            'missing_required_columns': missing_columns,
            'extra_columns': extra_columns,
            'total_columns': len(df_columns),
            'expected_columns': len(schema_columns),
            'column_coverage': len(df_columns & schema_columns) / len(schema_columns)
        }

    def _validate_required_fields(
        self,
        df: pd.DataFrame,
        schema: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Validate required field completeness."""
        field_completeness = {}
        overall_issues = 0

        for col, spec in schema.items():
            if spec.get('required', False) and col in df.columns:
                null_count = df[col].isnull().sum()
                total_count = len(df)
                completeness_rate = (total_count - null_count) / total_count if total_count > 0 else 0

                field_completeness[col] = {
                    'null_count': null_count,
                    'completeness_rate': completeness_rate,
                    'passes_requirement': null_count == 0
                }

                if null_count > 0:
                    overall_issues += 1

        status = 'passed' if overall_issues == 0 else 'failed'

        return {
            'status': status,
            'field_completeness': field_completeness,
            'required_fields_with_nulls': overall_issues
        }

    def _validate_data_types(
        self,
        df: pd.DataFrame,
        schema: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Validate data type compliance."""
        type_validation = {}
        type_issues = 0

        for col, spec in schema.items():
            if col not in df.columns:
                continue

            expected_type = spec.get('data_type')
            if not expected_type:
                continue

            actual_dtype = str(df[col].dtype)
            is_compatible = self._check_type_compatibility(actual_dtype, expected_type)

            type_validation[col] = {
                'expected_type': expected_type,
                'actual_type': actual_dtype,
                'is_compatible': is_compatible
            }

            if not is_compatible:
                type_issues += 1

        status = 'passed' if type_issues == 0 else 'warning'

        return {
            'status': status,
            'type_validation': type_validation,
            'incompatible_types': type_issues
        }

    def _check_type_compatibility(self, actual_dtype: str, expected_type: str) -> bool:
        """Check if actual pandas dtype is compatible with expected OMOP type."""
        type_mappings = {
            'integer': ['int64', 'int32', 'Int64', 'Int32'],
            'float': ['float64', 'float32', 'Float64', 'Float32'],
            'string': ['object', 'string', 'str'],
            'date': ['datetime64[ns]', 'object'],  # Object might contain dates
            'boolean': ['bool', 'boolean']
        }

        compatible_types = type_mappings.get(expected_type, [])
        return any(dtype in actual_dtype for dtype in compatible_types)

    def _validate_foreign_key_integrity(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate foreign key integrity (basic checks)."""
        fk_validation = {}

        # Check person_id (should be positive integers)
        if 'person_id' in df.columns:
            person_ids = pd.to_numeric(df['person_id'], errors='coerce')
            invalid_person_ids = person_ids.isnull().sum() + (person_ids <= 0).sum()

            fk_validation['person_id'] = {
                'invalid_count': invalid_person_ids,
                'total_count': len(df),
                'validity_rate': (len(df) - invalid_person_ids) / len(df) if len(df) > 0 else 0
            }

        status = 'passed'
        for field, validation in fk_validation.items():
            if validation['invalid_count'] > 0:
                status = 'warning'
                break

        return {
            'status': status,
            'foreign_key_validation': fk_validation
        }

    def _validate_standard_concepts(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate standard concept usage."""
        concept_validation = {}

        # Validate drug_concept_id
        if 'drug_concept_id' in df.columns:
            drug_concepts = pd.to_numeric(df['drug_concept_id'], errors='coerce')
            valid_concepts = (~drug_concepts.isnull() & (drug_concepts > 0)).sum()

            concept_validation['drug_concept_id'] = {
                'valid_count': valid_concepts,
                'total_count': len(df),
                'validity_rate': valid_concepts / len(df) if len(df) > 0 else 0
            }

        # Validate drug_type_concept_id
        if 'drug_type_concept_id' in df.columns:
            type_concepts = df['drug_type_concept_id'].dropna()
            known_types = set(self.standard_concepts['drug_types'].values())
            valid_types = type_concepts.isin(known_types).sum()

            concept_validation['drug_type_concept_id'] = {
                'valid_count': valid_types,
                'total_count': len(type_concepts),
                'validity_rate': valid_types / len(type_concepts) if len(type_concepts) > 0 else 0,
                'unknown_types': type_concepts[~type_concepts.isin(known_types)].unique().tolist()
            }

        # Validate unit and route concepts
        for concept_field in ['dose_unit_concept_id', 'route_concept_id']:
            if concept_field in df.columns:
                concepts = pd.to_numeric(df[concept_field], errors='coerce').dropna()
                valid_concepts = (concepts > 0).sum()

                concept_validation[concept_field] = {
                    'valid_count': valid_concepts,
                    'total_count': len(concepts),
                    'validity_rate': valid_concepts / len(concepts) if len(concepts) > 0 else 0
                }

        # Determine status
        avg_validity = sum(
            v['validity_rate'] for v in concept_validation.values()
        ) / len(concept_validation) if concept_validation else 1.0

        status = 'passed' if avg_validity >= 0.95 else 'warning'

        return {
            'status': status,
            'concept_validation': concept_validation,
            'average_validity_rate': avg_validity
        }

    def _validate_date_logic(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate date logic in drug exposure records."""
        date_issues = 0
        total_with_dates = 0

        start_col = 'drug_exposure_start_date'
        end_col = 'drug_exposure_end_date'

        if start_col in df.columns and end_col in df.columns:
            # Convert to datetime
            start_dates = pd.to_datetime(df[start_col], errors='coerce')
            end_dates = pd.to_datetime(df[end_col], errors='coerce')

            # Check for records with both start and end dates
            both_dates_mask = start_dates.notna() & end_dates.notna()
            records_with_both = both_dates_mask.sum()
            total_with_dates = records_with_both

            if records_with_both > 0:
                # Check for end date before start date
                invalid_order = (end_dates < start_dates) & both_dates_mask
                date_issues = invalid_order.sum()

        status = 'passed' if date_issues == 0 else 'warning'

        return {
            'status': status,
            'records_with_both_dates': total_with_dates,
            'invalid_date_order': date_issues,
            'date_logic_validity_rate': (
                (total_with_dates - date_issues) / total_with_dates
                if total_with_dates > 0 else 1.0
            )
        }

    def _validate_business_rules(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate OMOP business rules."""
        business_rule_violations = {}

        # Rule 1: dose_value should have corresponding dose_unit_concept_id
        if 'dose_value' in df.columns and 'dose_unit_concept_id' in df.columns:
            has_dose_value = df['dose_value'].notna()
            has_dose_unit = df['dose_unit_concept_id'].notna()

            dose_without_unit = (has_dose_value & ~has_dose_unit).sum()
            unit_without_dose = (~has_dose_value & has_dose_unit).sum()

            business_rule_violations['dose_unit_consistency'] = {
                'dose_without_unit': dose_without_unit,
                'unit_without_dose': unit_without_dose,
                'total_violations': dose_without_unit + unit_without_dose
            }

        # Rule 2: Check for reasonable dose values
        if 'dose_value' in df.columns:
            dose_values = pd.to_numeric(df['dose_value'], errors='coerce').dropna()
            unreasonable_doses = ((dose_values <= 0) | (dose_values > 10000)).sum()

            business_rule_violations['reasonable_doses'] = {
                'unreasonable_count': unreasonable_doses,
                'total_doses': len(dose_values),
                'reasonableness_rate': (
                    (len(dose_values) - unreasonable_doses) / len(dose_values)
                    if len(dose_values) > 0 else 1.0
                )
            }

        total_violations = sum(
            rule.get('total_violations', rule.get('unreasonable_count', 0))
            for rule in business_rule_violations.values()
        )

        status = 'passed' if total_violations == 0 else 'warning'

        return {
            'status': status,
            'business_rule_violations': business_rule_violations,
            'total_violations': total_violations
        }

    def _validate_era_logic(self, era_df: pd.DataFrame) -> Dict[str, Any]:
        """Validate era construction logic."""
        era_issues = 0

        # Check that era end date >= era start date
        if 'drug_era_start_date' in era_df.columns and 'drug_era_end_date' in era_df.columns:
            start_dates = pd.to_datetime(era_df['drug_era_start_date'], errors='coerce')
            end_dates = pd.to_datetime(era_df['drug_era_end_date'], errors='coerce')

            invalid_eras = (end_dates < start_dates).sum()
            era_issues += invalid_eras

        status = 'passed' if era_issues == 0 else 'warning'

        return {
            'status': status,
            'invalid_era_logic': era_issues,
            'total_eras': len(era_df)
        }

    def _validate_dose_logic(self, dose_era_df: pd.DataFrame) -> Dict[str, Any]:
        """Validate dose era logic."""
        dose_issues = 0

        # Check that dose values are positive
        if 'dose_value' in dose_era_df.columns:
            dose_values = pd.to_numeric(dose_era_df['dose_value'], errors='coerce')
            non_positive_doses = (dose_values <= 0).sum()
            dose_issues += non_positive_doses

        status = 'passed' if dose_issues == 0 else 'warning'

        return {
            'status': status,
            'non_positive_doses': dose_issues,
            'total_dose_eras': len(dose_era_df)
        }

    def _determine_validation_status(self, validation_results: Dict[str, Any]) -> str:
        """Determine overall validation status."""
        critical_checks = ['schema_compliance', 'required_fields']
        warning_checks = ['standard_concepts', 'date_logic', 'business_rules']

        # Check critical validations
        for check in critical_checks:
            if check in validation_results:
                if validation_results[check].get('status') == 'failed':
                    return 'failed'

        # Check warning validations
        has_warnings = False
        for check in warning_checks:
            if check in validation_results:
                if validation_results[check].get('status') in ['warning', 'failed']:
                    has_warnings = True

        return 'warnings' if has_warnings else 'passed'

    def _generate_table_summary(
        self,
        validation_results: Dict[str, Any],
        record_count: int
    ) -> Dict[str, Any]:
        """Generate summary for table validation."""
        summary = {
            'record_count': record_count,
            'validation_checks': len(validation_results),
            'passed_checks': len([r for r in validation_results.values() if r.get('status') == 'passed']),
            'failed_checks': len([r for r in validation_results.values() if r.get('status') == 'failed']),
            'warning_checks': len([r for r in validation_results.values() if r.get('status') == 'warning'])
        }

        return summary

    def validate_omop_tables(
        self,
        drug_exposure_df: Optional[pd.DataFrame] = None,
        drug_era_df: Optional[pd.DataFrame] = None,
        dose_era_df: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """
        Validate all OMOP tables together.

        Args:
            drug_exposure_df: DRUG_EXPOSURE table
            drug_era_df: DRUG_ERA table
            dose_era_df: DOSE_ERA table

        Returns:
            Comprehensive validation report
        """
        self.logger.info("Starting comprehensive OMOP CDM validation")

        validation_results = {}

        # Validate individual tables
        if drug_exposure_df is not None:
            validation_results['drug_exposure'] = self.validate_drug_exposure_table(drug_exposure_df)

        if drug_era_df is not None:
            validation_results['drug_era'] = self.validate_drug_era_table(drug_era_df)

        if dose_era_df is not None:
            validation_results['dose_era'] = self.validate_dose_era_table(dose_era_df)

        # Cross-table validations
        if drug_exposure_df is not None and drug_era_df is not None:
            validation_results['cross_table'] = self._validate_cross_table_consistency(
                drug_exposure_df, drug_era_df, dose_era_df
            )

        # Overall assessment
        overall_status = self._determine_overall_omop_status(validation_results)

        return {
            'status': overall_status,
            'cdm_version': self.cdm_version,
            'validation_timestamp': datetime.now().isoformat(),
            'table_results': validation_results,
            'summary': self._generate_omop_summary(validation_results),
            'recommendations': self._generate_omop_recommendations(validation_results)
        }

    def _validate_cross_table_consistency(
        self,
        drug_exposure_df: pd.DataFrame,
        drug_era_df: Optional[pd.DataFrame],
        dose_era_df: Optional[pd.DataFrame]
    ) -> Dict[str, Any]:
        """Validate consistency across OMOP tables."""
        consistency_issues = {}

        # Check person_id consistency
        exposure_persons = set(drug_exposure_df['person_id'].dropna())

        if drug_era_df is not None:
            era_persons = set(drug_era_df['person_id'].dropna())
            missing_in_era = exposure_persons - era_persons
            consistency_issues['person_id_era_coverage'] = len(missing_in_era)

        if dose_era_df is not None:
            dose_persons = set(dose_era_df['person_id'].dropna())
            missing_in_dose = exposure_persons - dose_persons
            consistency_issues['person_id_dose_coverage'] = len(missing_in_dose)

        total_issues = sum(consistency_issues.values())
        status = 'passed' if total_issues == 0 else 'warning'

        return {
            'status': status,
            'consistency_issues': consistency_issues,
            'total_issues': total_issues
        }

    def _determine_overall_omop_status(self, validation_results: Dict[str, Any]) -> str:
        """Determine overall OMOP validation status."""
        table_statuses = [
            result.get('status', 'unknown')
            for result in validation_results.values()
            if isinstance(result, dict) and 'status' in result
        ]

        if 'failed' in table_statuses:
            return 'failed'
        elif 'warnings' in table_statuses:
            return 'warnings'
        else:
            return 'passed'

    def _generate_omop_summary(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate OMOP validation summary."""
        summary = {
            'validated_tables': list(validation_results.keys()),
            'total_tables': len(validation_results)
        }

        # Aggregate record counts
        total_records = 0
        for result in validation_results.values():
            if isinstance(result, dict) and 'record_count' in result:
                total_records += result['record_count']

        summary['total_records'] = total_records

        return summary

    def _generate_omop_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """Generate OMOP-specific recommendations."""
        recommendations = []

        for table_name, result in validation_results.items():
            if not isinstance(result, dict) or result.get('status') == 'passed':
                continue

            if result.get('status') == 'failed':
                recommendations.append(
                    f"**{table_name.upper()}**: Critical validation failures detected. "
                    "Review table structure and required fields."
                )

            elif result.get('status') == 'warnings':
                recommendations.append(
                    f"**{table_name.upper()}**: Warning-level issues detected. "
                    "Review data quality and concept mappings."
                )

        if not recommendations:
            recommendations.append("All OMOP CDM validations passed successfully.")

        return recommendations