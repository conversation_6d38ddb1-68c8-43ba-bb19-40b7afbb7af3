"""
Custom exceptions for medication normalization pipeline.
"""


class MedicationNormalizationError(Exception):
    """Base exception for medication normalization pipeline."""

    def __init__(self, message: str, source_system: str = None, details: dict = None):
        self.message = message
        self.source_system = source_system
        self.details = details or {}
        super().__init__(self.message)

    def __str__(self):
        error_str = self.message
        if self.source_system:
            error_str = f"[{self.source_system}] {error_str}"
        if self.details:
            error_str += f" Details: {self.details}"
        return error_str


class ExtractionError(MedicationNormalizationError):
    """Raised when data extraction fails."""

    def __init__(self, message: str, source_system: str = None, table_name: str = None, sql_error: str = None):
        details = {}
        if table_name:
            details['table_name'] = table_name
        if sql_error:
            details['sql_error'] = sql_error

        super().__init__(message, source_system, details)


class NormalizationError(MedicationNormalizationError):
    """Raised when medication normalization fails."""

    def __init__(self, message: str, medication_text: str = None, rxnorm_error: str = None):
        details = {}
        if medication_text:
            details['medication_text'] = medication_text
        if rxnorm_error:
            details['rxnorm_error'] = rxnorm_error

        super().__init__(message, details=details)


class TransformationError(MedicationNormalizationError):
    """Raised when OMOP transformation fails."""

    def __init__(self, message: str, omop_table: str = None, concept_id: str = None):
        details = {}
        if omop_table:
            details['omop_table'] = omop_table
        if concept_id:
            details['concept_id'] = concept_id

        super().__init__(message, details=details)


class ValidationError(MedicationNormalizationError):
    """Raised when data validation fails."""

    def __init__(self, message: str, validation_rule: str = None, failed_count: int = None):
        details = {}
        if validation_rule:
            details['validation_rule'] = validation_rule
        if failed_count:
            details['failed_count'] = failed_count

        super().__init__(message, details=details)


class ConfigurationError(MedicationNormalizationError):
    """Raised when configuration is invalid."""

    def __init__(self, message: str, config_file: str = None, config_key: str = None):
        details = {}
        if config_file:
            details['config_file'] = config_file
        if config_key:
            details['config_key'] = config_key

        super().__init__(message, details=details)


class DatabaseConnectionError(MedicationNormalizationError):
    """Raised when database connection fails."""

    def __init__(self, message: str, database_type: str = None, host: str = None):
        details = {}
        if database_type:
            details['database_type'] = database_type
        if host:
            details['host'] = host

        super().__init__(message, details=details)


class RxNormAPIError(MedicationNormalizationError):
    """Raised when RxNorm API calls fail."""

    def __init__(self, message: str, api_endpoint: str = None, status_code: int = None, response_text: str = None):
        details = {}
        if api_endpoint:
            details['api_endpoint'] = api_endpoint
        if status_code:
            details['status_code'] = status_code
        if response_text:
            details['response_text'] = response_text

        super().__init__(message, details=details)