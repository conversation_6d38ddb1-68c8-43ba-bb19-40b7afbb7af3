# Source Database Configurations
# Configure database connections for each data source

map_pro:
  enabled: true
  database:
    type: "sql_server"  # or "postgresql", "mysql"
    host: "${MAP_PRO_HOST}"
    port: "${MAP_PRO_PORT}"
    database: "map_pro_sql"
    username: "${MAP_PRO_USER}"
    password: "${MAP_PRO_PASSWORD}"
    connection_timeout: 30
  tables:
    emr_med: "dbo__emr_med"
    map_meds: "map__map_meds"
    patients_web: "patient__patient"
    patients_mobile: "dbo__patient"
    facilities: "facility__facility"
  extraction:
    batch_size: 10000
    max_records: null  # null = no limit
    date_filter_column: "ccd_start_date"
    lookback_days: 365  # Extract last N days of data

pioneer_rx:
  enabled: true
  database:
    type: "sql_server"
    host: "${PIONEER_RX_HOST}"
    port: "${PIONEER_RX_PORT}"
    database: "${PIONEER_RX_DB}"
    username: "${PIONEER_RX_USER}"
    password: "${PIONEER_RX_PASSWORD}"
    connection_timeout: 30
  tables:
    prescriptions: "prescription__rx"
    transactions: "prescription__rxtransaction"
    items: "item__item"
    dosage_forms: "item__dosageform"
    administrations: "item__ItemAdministration"
  extraction:
    batch_size: 5000
    max_records: null
    date_filter_column: "DateWritten"
    lookback_days: 365

allocare:
  enabled: true
  database:
    type: "fhir_server"  # Special type for FHIR API
    base_url: "${ALLOCARE_FHIR_URL}"
    auth_type: "bearer_token"  # or "basic", "oauth2"
    token: "${ALLOCARE_TOKEN}"
    timeout: 60
  resources:
    patients: "Patient"
    medication_statements: "MedicationStatement"
    medication_administrations: "MedicationAdministration"
    observations: "Observation"
  extraction:
    batch_size: 100  # FHIR bundle size
    max_records: null
    date_filter: "_lastUpdated"
    lookback_days: 365

# Global extraction settings
global:
  output_format: "parquet"  # "csv", "parquet", "json"
  include_test_data: false
  anonymize_patient_ids: true
  validation_enabled: true
  parallel_processing: true
  max_workers: 4