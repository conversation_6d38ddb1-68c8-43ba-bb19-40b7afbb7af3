"""
Logging utilities for medication normalization pipeline.
"""

import logging
import logging.config
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSO<PERSON>."""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # Add extra fields if they exist
        if hasattr(record, 'source_system'):
            log_data['source_system'] = record.source_system
        if hasattr(record, 'patient_count'):
            log_data['patient_count'] = record.patient_count
        if hasattr(record, 'medication_count'):
            log_data['medication_count'] = record.medication_count
        if hasattr(record, 'processing_time'):
            log_data['processing_time'] = record.processing_time

        # Add exception info if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)

        return json.dumps(log_data)


def setup_logging(
    level: str = "INFO",
    log_dir: Optional[str] = None,
    console_output: bool = True,
    json_format: bool = True
) -> None:
    """
    Setup logging configuration for the medication normalization pipeline.

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory for log files (creates if doesn't exist)
        console_output: Whether to output logs to console
        json_format: Whether to use JSON formatting
    """
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)

    # Create formatters
    if json_format:
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))

    # Clear any existing handlers
    root_logger.handlers.clear()

    # Console handler
    if console_output:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # File handlers
    if log_dir:
        # Main log file
        file_handler = logging.FileHandler(log_dir / "medication_normalization.log")
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

        # Error log file
        error_handler = logging.FileHandler(log_dir / "errors.log")
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)

        # Performance log file for specific metrics
        perf_logger = logging.getLogger('performance')
        perf_handler = logging.FileHandler(log_dir / "performance.log")
        perf_handler.setFormatter(formatter)
        perf_logger.addHandler(perf_handler)
        perf_logger.propagate = False


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.

    Args:
        name: Logger name (usually __name__)

    Returns:
        Logger instance
    """
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging functionality to other classes."""

    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    def log_performance(
        self,
        operation: str,
        execution_time: float,
        record_count: Optional[int] = None,
        **kwargs
    ) -> None:
        """
        Log performance metrics.

        Args:
            operation: Name of the operation
            execution_time: Time taken in seconds
            record_count: Number of records processed
            **kwargs: Additional metrics to log
        """
        perf_logger = logging.getLogger('performance')

        extra_data = {
            'operation': operation,
            'execution_time': execution_time,
            'records_per_second': record_count / execution_time if record_count and execution_time > 0 else None
        }

        if record_count:
            extra_data['record_count'] = record_count

        extra_data.update(kwargs)

        perf_logger.info(
            f"Performance: {operation} completed in {execution_time:.2f}s",
            extra=extra_data
        )

    def log_extraction_metrics(
        self,
        source_system: str,
        table_name: str,
        extracted_count: int,
        execution_time: float
    ) -> None:
        """Log extraction-specific metrics."""
        self.logger.info(
            f"Extracted {extracted_count} records from {source_system}.{table_name}",
            extra={
                'source_system': source_system,
                'table_name': table_name,
                'extracted_count': extracted_count,
                'execution_time': execution_time
            }
        )

    def log_normalization_metrics(
        self,
        input_count: int,
        mapped_count: int,
        unmapped_count: int,
        fuzzy_matched_count: int,
        execution_time: float
    ) -> None:
        """Log normalization-specific metrics."""
        mapping_rate = (mapped_count / input_count * 100) if input_count > 0 else 0

        self.logger.info(
            f"Normalization complete: {mapped_count}/{input_count} mapped ({mapping_rate:.1f}%)",
            extra={
                'input_count': input_count,
                'mapped_count': mapped_count,
                'unmapped_count': unmapped_count,
                'fuzzy_matched_count': fuzzy_matched_count,
                'mapping_rate': mapping_rate,
                'execution_time': execution_time
            }
        )