"""
MAP Pro (Web and Mobile) medication data extractor.
"""

import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_extractor import BaseExtractor
from ..utils.constants import SourceSystem
from ..utils.exceptions import ExtractionError


class MapProExtractor(BaseExtractor):
    """
    Extractor for MedActionPlan Pro (Web and Mobile) medication data.

    Handles both MAP Pro Web and MAP MMS Mobile data from the same database,
    leveraging existing RxNorm mappings in map__map_meds table.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config, SourceSystem.MAP_PRO_WEB)  # Primary source designation

    def extract_medications(self) -> pd.DataFrame:
        """
        Extract medication data from MAP Pro database.

        Combines data from both Web and Mobile platforms, utilizing existing
        RxCUI mappings and joins medication details with patient information.

        Returns:
            DataFrame with medication data in unified schema
        """
        try:
            db_connector = self.connect_database()

            # Get table names from config
            tables = self.config.get('tables', {})
            emr_med_table = tables.get('emr_med', 'dbo__emr_med')
            map_meds_table = tables.get('map_meds', 'map__map_meds')
            patients_web_table = tables.get('patients_web', 'patient__patient')
            patients_mobile_table = tables.get('patients_mobile', 'dbo__patient')

            # Extract Web platform medications
            web_medications = self._extract_web_medications(
                db_connector, emr_med_table, map_meds_table, patients_web_table
            )

            # Extract Mobile platform medications
            mobile_medications = self._extract_mobile_medications(
                db_connector, emr_med_table, map_meds_table, patients_mobile_table
            )

            # Combine both platforms
            all_medications = pd.concat([web_medications, mobile_medications], ignore_index=True)

            self.logger.info(
                f"Extracted {len(web_medications)} Web + {len(mobile_medications)} Mobile "
                f"= {len(all_medications)} total MAP Pro medications"
            )

            return all_medications

        except Exception as e:
            raise ExtractionError(
                f"Failed to extract MAP Pro medications: {str(e)}",
                source_system=self.source_system.value,
                table_name="map_pro_medications"
            )

    def _extract_web_medications(
        self,
        db_connector,
        emr_med_table: str,
        map_meds_table: str,
        patients_table: str
    ) -> pd.DataFrame:
        """Extract medications from MAP Pro Web platform."""

        query = f"""
        SELECT
            -- Patient information
            p.id as patient_id,

            -- Medication text and codes
            e.ccd_name as medication_text,
            e.ccd_ndc as ndc_code,

            -- Existing RxNorm mappings (key advantage!)
            m.rxcui,
            m.generic as generic_name,
            m.brand as brand_name,
            m.scd as rxnorm_scd,
            m.display_strength,
            m.drug_class,

            -- Dose information
            e.ccd_dose_value as dose_value,
            e.ccd_dose_unit as dose_unit,
            e.ccd_route as route,

            -- Frequency information
            e.map_intraday_freq as intraday_frequency,
            e.map_interday_freq as interday_frequency,

            -- Dates
            e.ccd_start_date as start_date,
            e.ccd_stop_date as end_date,

            -- Metadata
            e.facility_id,
            e.id as emr_med_id,
            e.map_med_id,

            -- Additional flags
            e.ccd_inpatient_med as is_inpatient,
            e.ccd_prior_to_admission as prior_to_admission,
            e.ccd_patient_reported as patient_reported

        FROM {emr_med_table} e
        LEFT JOIN {map_meds_table} m ON e.map_med_id = m.id
        LEFT JOIN {patients_table} p ON e.patient_id = p.id

        WHERE p.id IS NOT NULL  -- Ensure valid patient
        """

        # Apply date filtering if configured
        date_column = self.config.get('extraction', {}).get('date_filter_column', 'ccd_start_date')
        query = self.apply_date_filter(query, f"e.{date_column}")

        # Apply record limit if configured
        query = self.apply_record_limit(query)

        self.logger.debug(f"Executing Web medications query: {query[:200]}...")

        df = db_connector.execute_query(query)

        # Convert to unified schema
        unified_records = []
        for _, row in df.iterrows():
            # Combine frequency information
            frequency = self._combine_frequency(row.get('intraday_frequency'), row.get('interday_frequency'))

            record = self.create_unified_record(
                patient_id=f"web_{row['patient_id']}",  # Prefix to distinguish from mobile
                medication_text=row['medication_text'],
                source_table=emr_med_table,
                source_record_id=row['emr_med_id'],
                medication_code=row.get('ndc_code'),
                dose_value=row.get('dose_value'),
                dose_unit=row.get('dose_unit'),
                route=row.get('route'),
                frequency=frequency,
                start_date=row.get('start_date'),
                end_date=row.get('end_date')
            )

            # Add MAP Pro specific fields
            record.update({
                'platform': 'web',
                'rxcui': row.get('rxcui'),
                'generic_name': row.get('generic_name'),
                'brand_name': row.get('brand_name'),
                'rxnorm_scd': row.get('rxnorm_scd'),
                'display_strength': row.get('display_strength'),
                'drug_class': row.get('drug_class'),
                'facility_id': row.get('facility_id'),
                'map_med_id': row.get('map_med_id'),
                'is_inpatient': row.get('is_inpatient'),
                'prior_to_admission': row.get('prior_to_admission'),
                'patient_reported': row.get('patient_reported')
            })

            unified_records.append(record)

        return pd.DataFrame(unified_records)

    def _extract_mobile_medications(
        self,
        db_connector,
        emr_med_table: str,
        map_meds_table: str,
        patients_table: str
    ) -> pd.DataFrame:
        """Extract medications from MAP MMS Mobile platform."""

        # Similar query but for mobile patients
        query = f"""
        SELECT
            -- Patient information
            p.id as patient_id,

            -- Medication text and codes
            e.ccd_name as medication_text,
            e.ccd_ndc as ndc_code,

            -- Existing RxNorm mappings
            m.rxcui,
            m.generic as generic_name,
            m.brand as brand_name,
            m.scd as rxnorm_scd,
            m.display_strength,
            m.drug_class,

            -- Dose information
            e.ccd_dose_value as dose_value,
            e.ccd_dose_unit as dose_unit,
            e.ccd_route as route,

            -- Frequency information
            e.map_intraday_freq as intraday_frequency,
            e.map_interday_freq as interday_frequency,

            -- Dates
            e.ccd_start_date as start_date,
            e.ccd_stop_date as end_date,

            -- Metadata
            e.facility_id,
            e.id as emr_med_id,
            e.map_med_id,

            -- Additional flags
            e.ccd_inpatient_med as is_inpatient,
            e.ccd_prior_to_admission as prior_to_admission,
            e.ccd_patient_reported as patient_reported

        FROM {emr_med_table} e
        LEFT JOIN {map_meds_table} m ON e.map_med_id = m.id
        LEFT JOIN {patients_table} p ON e.patient_id = p.id

        WHERE p.id IS NOT NULL  -- Ensure valid patient
        """

        # Apply filters
        date_column = self.config.get('extraction', {}).get('date_filter_column', 'ccd_start_date')
        query = self.apply_date_filter(query, f"e.{date_column}")
        query = self.apply_record_limit(query)

        self.logger.debug(f"Executing Mobile medications query: {query[:200]}...")

        df = db_connector.execute_query(query)

        # Convert to unified schema
        unified_records = []
        for _, row in df.iterrows():
            frequency = self._combine_frequency(row.get('intraday_frequency'), row.get('interday_frequency'))

            record = self.create_unified_record(
                patient_id=f"mobile_{row['patient_id']}",  # Prefix to distinguish from web
                medication_text=row['medication_text'],
                source_table=patients_table,  # Using mobile patient table reference
                source_record_id=row['emr_med_id'],
                medication_code=row.get('ndc_code'),
                dose_value=row.get('dose_value'),
                dose_unit=row.get('dose_unit'),
                route=row.get('route'),
                frequency=frequency,
                start_date=row.get('start_date'),
                end_date=row.get('end_date')
            )

            # Add MAP Pro specific fields
            record.update({
                'platform': 'mobile',
                'rxcui': row.get('rxcui'),
                'generic_name': row.get('generic_name'),
                'brand_name': row.get('brand_name'),
                'rxnorm_scd': row.get('rxnorm_scd'),
                'display_strength': row.get('display_strength'),
                'drug_class': row.get('drug_class'),
                'facility_id': row.get('facility_id'),
                'map_med_id': row.get('map_med_id'),
                'is_inpatient': row.get('is_inpatient'),
                'prior_to_admission': row.get('prior_to_admission'),
                'patient_reported': row.get('patient_reported')
            })

            unified_records.append(record)

        return pd.DataFrame(unified_records)

    def extract_patients(self) -> pd.DataFrame:
        """
        Extract patient data from MAP Pro (both Web and Mobile).

        Returns:
            DataFrame with patient demographics and facility information
        """
        try:
            db_connector = self.connect_database()
            tables = self.config.get('tables', {})

            # Extract Web patients
            web_patients = self._extract_web_patients(db_connector, tables)

            # Extract Mobile patients
            mobile_patients = self._extract_mobile_patients(db_connector, tables)

            # Combine both platforms
            all_patients = pd.concat([web_patients, mobile_patients], ignore_index=True)

            self.logger.info(f"Extracted {len(all_patients)} MAP Pro patients")
            return all_patients

        except Exception as e:
            raise ExtractionError(
                f"Failed to extract MAP Pro patients: {str(e)}",
                source_system=self.source_system.value,
                table_name="map_pro_patients"
            )

    def _extract_web_patients(self, db_connector, tables: Dict[str, str]) -> pd.DataFrame:
        """Extract Web platform patients."""
        patients_table = tables.get('patients_web', 'patient__patient')
        facilities_table = tables.get('facilities', 'facility__facility')

        query = f"""
        SELECT
            p.id as patient_id,
            'web' as platform,
            p.first_name,
            p.last_name,
            p.date_of_birth,
            p.gender,
            p.race,
            p.facility_id,
            f.name as facility_name,
            f.city as facility_city,
            f.state as facility_state,
            f.emr_type,
            f.emr_integration
        FROM {patients_table} p
        LEFT JOIN {facilities_table} f ON p.facility_id = f.id
        """

        return db_connector.execute_query(query)

    def _extract_mobile_patients(self, db_connector, tables: Dict[str, str]) -> pd.DataFrame:
        """Extract Mobile platform patients."""
        patients_table = tables.get('patients_mobile', 'dbo__patient')
        facilities_table = tables.get('facilities', 'dbo__facility')

        query = f"""
        SELECT
            p.id as patient_id,
            'mobile' as platform,
            p.first_name,
            p.last_name,
            p.date_of_birth,
            p.gender,
            p.race,
            p.facility_id,
            f.facility_name,
            f.facility_code,
            f.source_emr as emr_type
        FROM {patients_table} p
        LEFT JOIN {facilities_table} f ON p.facility_id = f.facility_id
        """

        return db_connector.execute_query(query)

    def _combine_frequency(self, intraday_freq: Optional[str], interday_freq: Optional[str]) -> Optional[str]:
        """
        Combine intraday and interday frequency information.

        Args:
            intraday_freq: Frequency within a day (e.g., "BID", "TID")
            interday_freq: Frequency between days (e.g., "Daily", "Weekly")

        Returns:
            Combined frequency string
        """
        parts = []

        if intraday_freq and str(intraday_freq).strip():
            parts.append(str(intraday_freq).strip())

        if interday_freq and str(interday_freq).strip():
            parts.append(str(interday_freq).strip())

        return " ".join(parts) if parts else None

    def get_rxnorm_mappings(self) -> pd.DataFrame:
        """
        Extract existing RxNorm mappings from MAP Pro for use in other source normalization.

        This is a key advantage - MAP Pro already has RxCUI mappings that can serve
        as reference data for normalizing medications from other sources.

        Returns:
            DataFrame with medication text to RxCUI mappings
        """
        try:
            db_connector = self.connect_database()
            map_meds_table = self.config.get('tables', {}).get('map_meds', 'map__map_meds')

            query = f"""
            SELECT DISTINCT
                m.generic as generic_name,
                m.brand as brand_name,
                m.rxcui,
                m.scd as rxnorm_scd,
                m.display_strength,
                m.drug_class,
                m.rxnorm_form_id,
                m.map_form,

                -- Get associated medication texts from EMR
                STRING_AGG(DISTINCT e.ccd_name, '; ') as medication_texts,
                COUNT(DISTINCT e.id) as usage_count

            FROM {map_meds_table} m
            LEFT JOIN dbo__emr_med e ON m.id = e.map_med_id

            WHERE m.rxcui IS NOT NULL
            AND m.rxcui != ''
            AND m.rxcui != '0'

            GROUP BY
                m.generic, m.brand, m.rxcui, m.scd,
                m.display_strength, m.drug_class,
                m.rxnorm_form_id, m.map_form

            ORDER BY usage_count DESC
            """

            mappings_df = db_connector.execute_query(query)

            self.logger.info(f"Retrieved {len(mappings_df)} RxNorm mappings from MAP Pro")
            return mappings_df

        except Exception as e:
            self.logger.error(f"Failed to extract RxNorm mappings: {str(e)}")
            return pd.DataFrame()

    def get_transplant_medications(self) -> pd.DataFrame:
        """
        Extract transplant-specific medications from MAP Pro.

        Focus on immunosuppressive and anti-rejection medications
        commonly used in transplant patients.

        Returns:
            DataFrame with transplant-specific medication data
        """
        try:
            db_connector = self.connect_database()
            map_meds_table = self.config.get('tables', {}).get('map_meds', 'map__map_meds')

            # Define transplant medication patterns
            transplant_keywords = [
                'tacrolimus', 'prograf', 'cyclosporine', 'neoral', 'mycophenolate', 'cellcept',
                'sirolimus', 'rapamune', 'everolimus', 'prednisone', 'prednisolone',
                'azathioprine', 'imuran', 'basiliximab', 'simulect', 'alemtuzumab'
            ]

            # Build WHERE clause for transplant medications
            keyword_conditions = " OR ".join([
                f"(m.generic LIKE '%{keyword}%' OR m.brand LIKE '%{keyword}%')"
                for keyword in transplant_keywords
            ])

            query = f"""
            SELECT
                m.*,
                COUNT(DISTINCT e.patient_id) as patient_count,
                COUNT(e.id) as prescription_count,
                STRING_AGG(DISTINCT e.ccd_name, '; ') as medication_variants

            FROM {map_meds_table} m
            LEFT JOIN dbo__emr_med e ON m.id = e.map_med_id

            WHERE {keyword_conditions}

            GROUP BY m.id, m.generic, m.brand, m.rxcui, m.scd, m.display_strength,
                     m.drug_class, m.rxnorm_form_id, m.map_form, m.sideeffect

            ORDER BY patient_count DESC, prescription_count DESC
            """

            transplant_df = db_connector.execute_query(query)

            self.logger.info(f"Found {len(transplant_df)} transplant medications in MAP Pro")
            return transplant_df

        except Exception as e:
            self.logger.error(f"Failed to extract transplant medications: {str(e)}")
            return pd.DataFrame()