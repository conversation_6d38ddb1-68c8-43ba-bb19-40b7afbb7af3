"""
AlloCare FHIR medication data extractor.
"""

import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

from .base_extractor import BaseExtractor
from ..utils.constants import SourceSystem
from ..utils.exceptions import ExtractionError


class AllocareExtractor(BaseExtractor):
    """
    Extractor for AlloCare FHIR-compliant patient monitoring system.

    Handles FHIR v6.0.0-ballot3 MedicationStatement and MedicationAdministration
    resources with built-in compliance tracking.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config, SourceSystem.ALLOCARE)

    def extract_medications(self) -> pd.DataFrame:
        """
        Extract medication data from AlloCare FHIR server.

        Combines MedicationStatement and MedicationAdministration resources
        to provide comprehensive medication usage data with compliance metrics.

        Returns:
            DataFrame with medication data in unified schema
        """
        try:
            # Check if using FHIR server or database tables
            if self.config.get('database', {}).get('type') == 'fhir_server':
                return self._extract_from_fhir_server()
            else:
                return self._extract_from_database_tables()

        except Exception as e:
            raise ExtractionError(
                f"Failed to extract AlloCare medications: {str(e)}",
                source_system=self.source_system.value,
                table_name="allocare_medications"
            )

    def _extract_from_fhir_server(self) -> pd.DataFrame:
        """Extract medication data from FHIR server API."""

        db_connector = self.connect_database()
        unified_records = []

        # Extract MedicationStatement resources
        medication_statements = self._extract_medication_statements(db_connector)

        # Extract MedicationAdministration resources
        medication_administrations = self._extract_medication_administrations(db_connector)

        # Extract medication compliance observations
        compliance_observations = self._extract_compliance_observations(db_connector)

        # Convert FHIR resources to unified schema
        unified_records.extend(self._convert_medication_statements(medication_statements))
        unified_records.extend(self._convert_medication_administrations(medication_administrations))

        # Add compliance data
        medications_df = pd.DataFrame(unified_records)
        if not compliance_observations.empty:
            medications_df = self._merge_compliance_data(medications_df, compliance_observations)

        self.logger.info(f"Extracted {len(medications_df)} AlloCare FHIR medication records")
        return medications_df

    def _extract_from_database_tables(self) -> pd.DataFrame:
        """Extract medication data from AlloCare database tables."""

        db_connector = self.connect_database()
        unified_records = []

        # Extract from MedicationStatement table
        med_statements = self._extract_medication_statements_table(db_connector)

        # Extract from MedicationAdministration table
        med_administrations = self._extract_medication_administrations_table(db_connector)

        # Extract compliance data from Observation table
        compliance_data = self._extract_compliance_observations_table(db_connector)

        # Convert to unified schema
        unified_records.extend(self._convert_statements_table_data(med_statements))
        unified_records.extend(self._convert_administrations_table_data(med_administrations))

        medications_df = pd.DataFrame(unified_records)

        # Merge compliance data
        if not compliance_data.empty:
            medications_df = self._merge_compliance_data(medications_df, compliance_data)

        self.logger.info(f"Extracted {len(medications_df)} AlloCare database medication records")
        return medications_df

    def _extract_medication_statements(self, db_connector) -> List[Dict]:
        """Extract MedicationStatement resources from FHIR server."""

        try:
            # Get extraction parameters
            extraction_config = self.config.get('extraction', {})
            batch_size = extraction_config.get('batch_size', 100)
            max_records = extraction_config.get('max_records')

            # Calculate max pages if record limit specified
            max_pages = None
            if max_records:
                max_pages = (max_records // batch_size) + 1

            # Build query parameters
            params = {
                '_count': batch_size,
                'status': 'active,completed,intended',  # Focus on relevant statuses
                '_include': 'MedicationStatement:medication'
            }

            # Add date filter if configured
            lookback_days = extraction_config.get('lookback_days')
            if lookback_days:
                cutoff_date = (datetime.now() - pd.Timedelta(days=lookback_days)).strftime('%Y-%m-%d')
                params['_lastUpdated'] = f'ge{cutoff_date}'

            resources = db_connector.execute_fhir_query(
                'MedicationStatement',
                params=params,
                max_pages=max_pages
            )

            self.logger.info(f"Retrieved {len(resources)} MedicationStatement resources")
            return resources

        except Exception as e:
            self.logger.error(f"Failed to extract MedicationStatement resources: {str(e)}")
            return []

    def _extract_medication_administrations(self, db_connector) -> List[Dict]:
        """Extract MedicationAdministration resources from FHIR server."""

        try:
            extraction_config = self.config.get('extraction', {})
            batch_size = extraction_config.get('batch_size', 100)
            max_records = extraction_config.get('max_records')

            max_pages = None
            if max_records:
                max_pages = (max_records // batch_size) + 1

            params = {
                '_count': batch_size,
                'status': 'completed,in-progress',
                '_include': 'MedicationAdministration:medication'
            }

            # Add date filter
            lookback_days = extraction_config.get('lookback_days')
            if lookback_days:
                cutoff_date = (datetime.now() - pd.Timedelta(days=lookback_days)).strftime('%Y-%m-%d')
                params['_lastUpdated'] = f'ge{cutoff_date}'

            resources = db_connector.execute_fhir_query(
                'MedicationAdministration',
                params=params,
                max_pages=max_pages
            )

            self.logger.info(f"Retrieved {len(resources)} MedicationAdministration resources")
            return resources

        except Exception as e:
            self.logger.error(f"Failed to extract MedicationAdministration resources: {str(e)}")
            return []

    def _extract_compliance_observations(self, db_connector) -> pd.DataFrame:
        """Extract medication compliance data from Observation resources."""

        try:
            extraction_config = self.config.get('extraction', {})
            batch_size = extraction_config.get('batch_size', 100)

            params = {
                '_count': batch_size,
                'code': 'http://loinc.org|11366-2',  # History of medication use
                '_include': 'Observation:subject'
            }

            # Add date filter
            lookback_days = extraction_config.get('lookback_days')
            if lookback_days:
                cutoff_date = (datetime.now() - pd.Timedelta(days=lookback_days)).strftime('%Y-%m-%d')
                params['_lastUpdated'] = f'ge{cutoff_date}'

            resources = db_connector.execute_fhir_query('Observation', params=params)

            # Filter for medication compliance observations
            compliance_resources = []
            for resource in resources:
                if self._is_medication_compliance_observation(resource):
                    compliance_resources.append(resource)

            # Convert to DataFrame
            compliance_data = []
            for resource in compliance_resources:
                compliance_record = self._extract_compliance_data_from_observation(resource)
                if compliance_record:
                    compliance_data.append(compliance_record)

            compliance_df = pd.DataFrame(compliance_data)
            self.logger.info(f"Retrieved {len(compliance_df)} compliance observations")
            return compliance_df

        except Exception as e:
            self.logger.error(f"Failed to extract compliance observations: {str(e)}")
            return pd.DataFrame()

    def _convert_medication_statements(self, statements: List[Dict]) -> List[Dict]:
        """Convert FHIR MedicationStatement resources to unified schema."""

        unified_records = []

        for statement in statements:
            try:
                # Extract patient reference
                patient_id = self._extract_patient_id_from_reference(statement.get('subject', {}))

                # Extract medication information
                medication_info = self._extract_medication_info(statement)

                # Extract dosage information
                dosage_info = self._extract_dosage_info(statement.get('dosage', []))

                # Extract dates
                effective_period = statement.get('effectivePeriod', {})
                start_date = self._parse_fhir_date(effective_period.get('start'))
                end_date = self._parse_fhir_date(effective_period.get('end'))

                record = self.create_unified_record(
                    patient_id=f"allo_{patient_id}",
                    medication_text=medication_info.get('text'),
                    source_table='MedicationStatement',
                    source_record_id=statement.get('id'),
                    medication_code=medication_info.get('code'),
                    dose_value=dosage_info.get('dose_value'),
                    dose_unit=dosage_info.get('dose_unit'),
                    route=dosage_info.get('route'),
                    frequency=dosage_info.get('frequency'),
                    start_date=start_date,
                    end_date=end_date
                )

                # Add FHIR-specific fields
                record.update({
                    'resource_type': 'MedicationStatement',
                    'status': statement.get('status'),
                    'category': self._extract_category(statement.get('category', [])),
                    'medication_coding': medication_info.get('coding'),
                    'taken': statement.get('taken'),
                    'reason_code': self._extract_reason_codes(statement.get('reasonCode', [])),
                    'note': self._extract_notes(statement.get('note', []))
                })

                unified_records.append(record)

            except Exception as e:
                self.logger.error(f"Failed to convert MedicationStatement {statement.get('id')}: {str(e)}")
                continue

        return unified_records

    def _convert_medication_administrations(self, administrations: List[Dict]) -> List[Dict]:
        """Convert FHIR MedicationAdministration resources to unified schema."""

        unified_records = []

        for admin in administrations:
            try:
                # Extract patient reference
                patient_id = self._extract_patient_id_from_reference(admin.get('subject', {}))

                # Extract medication information
                medication_info = self._extract_medication_info(admin)

                # Extract dosage information
                dosage_info = self._extract_dosage_info_from_administration(admin.get('dosage', {}))

                # Extract effective date/time
                effective_datetime = self._parse_fhir_datetime(admin.get('effectiveDateTime'))

                record = self.create_unified_record(
                    patient_id=f"allo_{patient_id}",
                    medication_text=medication_info.get('text'),
                    source_table='MedicationAdministration',
                    source_record_id=admin.get('id'),
                    medication_code=medication_info.get('code'),
                    dose_value=dosage_info.get('dose_value'),
                    dose_unit=dosage_info.get('dose_unit'),
                    route=dosage_info.get('route'),
                    frequency=None,  # Not typically specified for administrations
                    start_date=effective_datetime,
                    end_date=effective_datetime  # Point in time
                )

                # Add administration-specific fields
                record.update({
                    'resource_type': 'MedicationAdministration',
                    'status': admin.get('status'),
                    'category': self._extract_category(admin.get('category', [])),
                    'medication_coding': medication_info.get('coding'),
                    'performer': self._extract_performer_info(admin.get('performer', [])),
                    'reason_code': self._extract_reason_codes(admin.get('reasonCode', [])),
                    'note': self._extract_notes(admin.get('note', [])),
                    'effective_datetime': effective_datetime
                })

                unified_records.append(record)

            except Exception as e:
                self.logger.error(f"Failed to convert MedicationAdministration {admin.get('id')}: {str(e)}")
                continue

        return unified_records

    def extract_patients(self) -> pd.DataFrame:
        """
        Extract patient data from AlloCare FHIR system.

        Returns:
            DataFrame with patient demographics and basic information
        """
        try:
            if self.config.get('database', {}).get('type') == 'fhir_server':
                return self._extract_patients_from_fhir()
            else:
                return self._extract_patients_from_table()

        except Exception as e:
            raise ExtractionError(
                f"Failed to extract AlloCare patients: {str(e)}",
                source_system=self.source_system.value
            )

    def _extract_patients_from_fhir(self) -> pd.DataFrame:
        """Extract patients from FHIR server."""

        db_connector = self.connect_database()

        try:
            params = {
                '_count': 1000,
                'active': 'true'
            }

            patients = db_connector.execute_fhir_query('Patient', params=params)

            patient_records = []
            for patient in patients:
                record = {
                    'patient_id': patient.get('id'),
                    'active': patient.get('active'),
                    'gender': patient.get('gender'),
                    'birth_date': patient.get('birthDate'),
                    'deceased': patient.get('deceasedBoolean', False),
                    'source_system': self.source_system.value
                }

                # Extract name information
                names = patient.get('name', [])
                if names:
                    name = names[0]  # Use first name
                    record.update({
                        'family_name': ' '.join(name.get('family', [])) if isinstance(name.get('family'), list) else name.get('family'),
                        'given_names': ' '.join(name.get('given', [])) if name.get('given') else None
                    })

                # Extract contact information
                telecoms = patient.get('telecom', [])
                for telecom in telecoms:
                    if telecom.get('system') == 'email':
                        record['email'] = telecom.get('value')
                    elif telecom.get('system') == 'phone':
                        record['phone'] = telecom.get('value')

                patient_records.append(record)

            patients_df = pd.DataFrame(patient_records)
            self.logger.info(f"Extracted {len(patients_df)} AlloCare FHIR patients")
            return patients_df

        except Exception as e:
            self.logger.error(f"Failed to extract patients from FHIR server: {str(e)}")
            return pd.DataFrame()

    # Helper methods for FHIR data extraction
    def _extract_patient_id_from_reference(self, subject: Dict) -> Optional[str]:
        """Extract patient ID from FHIR reference."""
        reference = subject.get('reference', '')
        if reference.startswith('Patient/'):
            return reference.replace('Patient/', '')
        return None

    def _extract_medication_info(self, resource: Dict) -> Dict[str, Any]:
        """Extract medication information from FHIR resource."""
        medication_info = {'text': None, 'code': None, 'coding': None}

        # Check for medicationCodeableConcept
        med_concept = resource.get('medicationCodeableConcept', {})
        if med_concept:
            medication_info['text'] = med_concept.get('text')
            coding = med_concept.get('coding', [])
            if coding:
                medication_info['coding'] = coding
                # Prefer RxNorm codes
                for code in coding:
                    if code.get('system') == 'http://www.nlm.nih.gov/research/umls/rxnorm':
                        medication_info['code'] = code.get('code')
                        break
                # Fallback to first code
                if not medication_info['code'] and coding:
                    medication_info['code'] = coding[0].get('code')

        # Check for medicationReference
        med_ref = resource.get('medicationReference', {})
        if med_ref and not medication_info['text']:
            medication_info['text'] = med_ref.get('display')

        return medication_info

    def _extract_dosage_info(self, dosage_list: List[Dict]) -> Dict[str, Any]:
        """Extract dosage information from FHIR dosage array."""
        dosage_info = {'dose_value': None, 'dose_unit': None, 'route': None, 'frequency': None}

        if not dosage_list:
            return dosage_info

        # Use first dosage instruction
        dosage = dosage_list[0]

        # Extract dose quantity
        dose_quantity = dosage.get('doseAndRate', [{}])[0].get('doseQuantity', {})
        if dose_quantity:
            dosage_info['dose_value'] = dose_quantity.get('value')
            dosage_info['dose_unit'] = dose_quantity.get('unit')

        # Extract route
        route = dosage.get('route', {})
        if route:
            dosage_info['route'] = route.get('text') or (route.get('coding', [{}])[0].get('display'))

        # Extract timing/frequency
        timing = dosage.get('timing', {})
        if timing:
            repeat = timing.get('repeat', {})
            frequency = repeat.get('frequency', 1)
            period = repeat.get('period', 1)
            period_unit = repeat.get('periodUnit', 'day')

            if period_unit == 'd':
                period_unit = 'day'

            dosage_info['frequency'] = f"{frequency} times per {period} {period_unit}{'s' if period > 1 else ''}"

        return dosage_info

    def _extract_dosage_info_from_administration(self, dosage: Dict) -> Dict[str, Any]:
        """Extract dosage information from MedicationAdministration dosage."""
        dosage_info = {'dose_value': None, 'dose_unit': None, 'route': None}

        # Extract dose
        dose = dosage.get('dose', {})
        if dose:
            dosage_info['dose_value'] = dose.get('value')
            dosage_info['dose_unit'] = dose.get('unit')

        # Extract route
        route = dosage.get('route', {})
        if route:
            dosage_info['route'] = route.get('text') or (route.get('coding', [{}])[0].get('display'))

        return dosage_info

    def _parse_fhir_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse FHIR date string to datetime."""
        if not date_str:
            return None

        try:
            # Handle different FHIR date formats
            if 'T' in date_str:
                return pd.to_datetime(date_str).to_pydatetime()
            else:
                return pd.to_datetime(date_str).to_pydatetime()
        except (ValueError, TypeError):
            return None

    def _parse_fhir_datetime(self, datetime_str: Optional[str]) -> Optional[datetime]:
        """Parse FHIR datetime string to datetime."""
        return self._parse_fhir_date(datetime_str)

    def _extract_category(self, category_list: List[Dict]) -> Optional[str]:
        """Extract category text from FHIR category array."""
        if not category_list:
            return None

        category = category_list[0]
        return category.get('text') or (category.get('coding', [{}])[0].get('display'))

    def _extract_reason_codes(self, reason_list: List[Dict]) -> List[str]:
        """Extract reason codes from FHIR reasonCode array."""
        reasons = []
        for reason in reason_list:
            text = reason.get('text')
            if text:
                reasons.append(text)
            else:
                coding = reason.get('coding', [])
                if coding:
                    display = coding[0].get('display')
                    if display:
                        reasons.append(display)

        return reasons

    def _extract_notes(self, note_list: List[Dict]) -> List[str]:
        """Extract notes from FHIR note array."""
        return [note.get('text') for note in note_list if note.get('text')]

    def _extract_performer_info(self, performer_list: List[Dict]) -> List[Dict]:
        """Extract performer information from MedicationAdministration."""
        performers = []
        for performer in performer_list:
            actor = performer.get('actor', {})
            performers.append({
                'reference': actor.get('reference'),
                'display': actor.get('display')
            })
        return performers

    def _is_medication_compliance_observation(self, observation: Dict) -> bool:
        """Check if observation is medication compliance related."""
        coding = observation.get('code', {}).get('coding', [])
        for code in coding:
            if (code.get('system') == 'http://loinc.org' and
                code.get('code') == '11366-2'):  # History of medication use
                return True

        # Also check display text
        display = observation.get('code', {}).get('text', '')
        return 'medication use narrative' in display.lower()

    def _extract_compliance_data_from_observation(self, observation: Dict) -> Optional[Dict]:
        """Extract compliance data from medication use observation."""
        try:
            patient_id = self._extract_patient_id_from_reference(observation.get('subject', {}))
            if not patient_id:
                return None

            # Extract compliance metrics
            value_ratio = observation.get('valueRatio', {})
            numerator = value_ratio.get('numerator', {}).get('value')  # Medications taken
            denominator = value_ratio.get('denominator', {}).get('value')  # Medications prescribed

            return {
                'patient_id': f"allo_{patient_id}",
                'observation_id': observation.get('id'),
                'effective_date': self._parse_fhir_date(observation.get('effectiveDateTime')),
                'medications_taken': numerator,
                'medications_prescribed': denominator,
                'compliance_ratio': numerator / denominator if numerator and denominator and denominator > 0 else None,
                'status': observation.get('status'),
                'category': self._extract_category(observation.get('category', []))
            }

        except Exception as e:
            self.logger.error(f"Failed to extract compliance data from observation {observation.get('id')}: {str(e)}")
            return None

    def _merge_compliance_data(self, medications_df: pd.DataFrame, compliance_df: pd.DataFrame) -> pd.DataFrame:
        """Merge compliance data with medication records."""
        if compliance_df.empty or medications_df.empty:
            return medications_df

        # Merge on patient_id
        merged_df = medications_df.merge(
            compliance_df[['patient_id', 'compliance_ratio', 'medications_taken', 'medications_prescribed']],
            on='patient_id',
            how='left'
        )

        self.logger.info(f"Merged compliance data for {len(merged_df)} medication records")
        return merged_df

    # Methods for database table extraction (when not using FHIR server)
    def _extract_medication_statements_table(self, db_connector) -> pd.DataFrame:
        """Extract from MedicationStatement database table."""
        # Implementation depends on actual table structure
        # This is a placeholder for database-based extraction
        return pd.DataFrame()

    def _extract_medication_administrations_table(self, db_connector) -> pd.DataFrame:
        """Extract from MedicationAdministration database table."""
        # Implementation depends on actual table structure
        return pd.DataFrame()

    def _extract_compliance_observations_table(self, db_connector) -> pd.DataFrame:
        """Extract compliance data from Observation database table."""
        # Implementation depends on actual table structure
        return pd.DataFrame()

    def _convert_statements_table_data(self, statements_df: pd.DataFrame) -> List[Dict]:
        """Convert database MedicationStatement data to unified schema."""
        # Implementation depends on actual table structure
        return []

    def _convert_administrations_table_data(self, administrations_df: pd.DataFrame) -> List[Dict]:
        """Convert database MedicationAdministration data to unified schema."""
        # Implementation depends on actual table structure
        return []

    def _extract_patients_from_table(self) -> pd.DataFrame:
        """Extract patients from database table."""
        # Implementation depends on actual table structure
        return pd.DataFrame()