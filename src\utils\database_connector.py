"""
Database connection utilities for multiple database types.
"""

import os
import pandas as pd
from typing import Dict, Any, Optional, Union, List
from urllib.parse import quote_plus
import logging

try:
    import sqlalchemy
    from sqlalchemy import create_engine, text
    from sqlalchemy.engine import Engine
    HAS_SQLALCHEMY = True
except ImportError:
    HAS_SQLALCHEMY = False

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False

from .exceptions import DatabaseConnectionError, ConfigurationError
from .logger import LoggerMixin


class DatabaseConnector(LoggerMixin):
    """
    Universal database connector supporting multiple database types.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize database connector.

        Args:
            config: Database configuration dictionary
        """
        self.config = config
        self.db_type = config.get('type', '').lower()
        self.engine: Optional[Engine] = None
        self._validate_config()

    def _validate_config(self) -> None:
        """Validate database configuration."""
        required_fields = ['type']

        if self.db_type == 'fhir_server':
            required_fields.extend(['base_url'])
        else:
            required_fields.extend(['host', 'database'])

        missing_fields = [field for field in required_fields if not self.config.get(field)]
        if missing_fields:
            raise ConfigurationError(
                f"Missing required database configuration fields: {missing_fields}",
                config_key=', '.join(missing_fields)
            )

        if not HAS_SQLALCHEMY and self.db_type != 'fhir_server':
            raise ConfigurationError(
                "SQLAlchemy is required for database connections. Install with: pip install sqlalchemy"
            )

        if self.db_type == 'fhir_server' and not HAS_REQUESTS:
            raise ConfigurationError(
                "Requests library is required for FHIR server connections. Install with: pip install requests"
            )

    def connect(self) -> Union[Engine, requests.Session]:
        """
        Establish database connection.

        Returns:
            Database engine or FHIR session
        """
        if self.db_type == 'fhir_server':
            return self._connect_fhir()
        else:
            return self._connect_sql()

    def _connect_sql(self) -> Engine:
        """Connect to SQL database."""
        if self.engine is not None:
            return self.engine

        try:
            connection_string = self._build_connection_string()
            self.engine = create_engine(
                connection_string,
                pool_timeout=self.config.get('connection_timeout', 30),
                pool_recycle=3600,
                echo=False
            )

            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            self.logger.info(f"Successfully connected to {self.db_type} database")
            return self.engine

        except Exception as e:
            raise DatabaseConnectionError(
                f"Failed to connect to {self.db_type} database: {str(e)}",
                database_type=self.db_type,
                host=self.config.get('host')
            )

    def _connect_fhir(self) -> requests.Session:
        """Connect to FHIR server."""
        session = requests.Session()

        # Set authentication
        auth_type = self.config.get('auth_type', 'bearer_token')
        if auth_type == 'bearer_token':
            token = self.config.get('token')
            if token:
                session.headers.update({'Authorization': f'Bearer {token}'})
        elif auth_type == 'basic':
            username = self.config.get('username')
            password = self.config.get('password')
            if username and password:
                session.auth = (username, password)

        # Set timeout
        session.timeout = self.config.get('timeout', 60)

        # Test connection
        try:
            response = session.get(f"{self.config['base_url']}/metadata")
            response.raise_for_status()
            self.logger.info("Successfully connected to FHIR server")
            return session
        except Exception as e:
            raise DatabaseConnectionError(
                f"Failed to connect to FHIR server: {str(e)}",
                database_type='fhir_server',
                host=self.config.get('base_url')
            )

    def _build_connection_string(self) -> str:
        """Build SQL connection string."""
        host = self.config['host']
        port = self.config.get('port')
        database = self.config['database']
        username = self.config.get('username')
        password = self.config.get('password')

        # URL encode password if it contains special characters
        if password:
            password = quote_plus(password)

        if self.db_type == 'sql_server':
            driver = self.config.get('driver', 'ODBC Driver 17 for SQL Server')
            conn_str = f"mssql+pyodbc://{username}:{password}@{host}"
            if port:
                conn_str += f":{port}"
            conn_str += f"/{database}?driver={quote_plus(driver)}"

        elif self.db_type == 'postgresql':
            conn_str = f"postgresql://{username}:{password}@{host}"
            if port:
                conn_str += f":{port}"
            conn_str += f"/{database}"

        elif self.db_type == 'mysql':
            conn_str = f"mysql+pymysql://{username}:{password}@{host}"
            if port:
                conn_str += f":{port}"
            conn_str += f"/{database}"

        elif self.db_type == 'sqlite':
            conn_str = f"sqlite:///{database}"

        else:
            raise ConfigurationError(f"Unsupported database type: {self.db_type}")

        return conn_str

    def execute_query(self, query: str, params: Optional[Dict] = None) -> pd.DataFrame:
        """
        Execute SQL query and return results as DataFrame.

        Args:
            query: SQL query string
            params: Query parameters

        Returns:
            Query results as pandas DataFrame
        """
        if self.db_type == 'fhir_server':
            raise ValueError("Use execute_fhir_query for FHIR servers")

        engine = self.connect()

        try:
            with engine.connect() as conn:
                result = pd.read_sql(
                    text(query),
                    conn,
                    params=params or {}
                )
            self.logger.debug(f"Query executed successfully, returned {len(result)} rows")
            return result

        except Exception as e:
            self.logger.error(f"Query execution failed: {str(e)}")
            raise

    def execute_fhir_query(
        self,
        resource_type: str,
        params: Optional[Dict] = None,
        max_pages: Optional[int] = None
    ) -> List[Dict]:
        """
        Execute FHIR query and return results.

        Args:
            resource_type: FHIR resource type (e.g., 'Patient', 'MedicationStatement')
            params: Query parameters
            max_pages: Maximum number of pages to retrieve

        Returns:
            List of FHIR resources
        """
        if self.db_type != 'fhir_server':
            raise ValueError("FHIR queries only supported for FHIR servers")

        session = self.connect()
        base_url = self.config['base_url']
        url = f"{base_url}/{resource_type}"

        all_resources = []
        page_count = 0

        try:
            while url and (max_pages is None or page_count < max_pages):
                self.logger.debug(f"Fetching FHIR page {page_count + 1}: {url}")

                response = session.get(url, params=params if page_count == 0 else None)
                response.raise_for_status()

                bundle = response.json()

                # Extract resources from bundle
                entries = bundle.get('entry', [])
                resources = [entry.get('resource') for entry in entries if entry.get('resource')]
                all_resources.extend(resources)

                # Find next page link
                url = None
                links = bundle.get('link', [])
                for link in links:
                    if link.get('relation') == 'next':
                        url = link.get('url')
                        break

                page_count += 1
                params = None  # Only use params on first request

            self.logger.info(f"Retrieved {len(all_resources)} {resource_type} resources from FHIR server")
            return all_resources

        except Exception as e:
            self.logger.error(f"FHIR query execution failed: {str(e)}")
            raise

    def test_connection(self) -> bool:
        """
        Test database connection.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            if self.db_type == 'fhir_server':
                session = self.connect()
                response = session.get(f"{self.config['base_url']}/metadata")
                return response.status_code == 200
            else:
                engine = self.connect()
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                return True
        except Exception as e:
            self.logger.error(f"Connection test failed: {str(e)}")
            return False

    def close(self) -> None:
        """Close database connection."""
        if self.engine:
            self.engine.dispose()
            self.engine = None
            self.logger.info("Database connection closed")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()