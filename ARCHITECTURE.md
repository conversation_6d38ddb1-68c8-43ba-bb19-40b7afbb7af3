# Medication Normalization Pipeline Architecture

## Overview
A modular, scalable medication normalization system that extracts medication data from four transplant patient sources and transforms it into OMOP CDM format with RxNorm standardization.

## High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌──────────────────┐
│  Data Sources   │───▶│   Extraction     │───▶│  Normalization  │───▶│  OMOP Transform  │
│                 │    │     Layer        │    │     Layer       │    │      Layer       │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └──────────────────┘
│                      │                  │    │                 │    │                  │
│ • MAP Pro Web       │ • Source-specific │    │ • RxNorm Mapping│    │ • DRUG_EXPOSURE  │
│ • MAP MMS Mobile    │   extractors      │    │ • Dose Normaliz.│    │ • DRUG_ERA       │
│ • PioneerRx         │ • Unified schema  │    │ • Quality checks│    │ • DOSE_ERA       │
│ • AlloCare          │ • Data validation │    │ • Conflict res. │    │ • Vocabulary     │
└─────────────────────┘ └──────────────────┘    └─────────────────┘    └──────────────────┘
                                                                       │
                                                                       ▼
                                                        ┌──────────────────┐
                                                        │  Curtis's Data   │
                                                        │   Warehouse      │
                                                        │   Workflow       │
                                                        └──────────────────┘
```

## Directory Structure

```
medication_normalization/
├── README.md                    # Project documentation
├── requirements.txt             # Python dependencies
├── setup.py                    # Package installation
├── config/                     # Configuration management
│   ├── __init__.py
│   ├── source_configs.yaml     # Source database configurations
│   ├── rxnorm_config.yaml      # RxNorm API settings
│   ├── omop_config.yaml        # OMOP CDM mappings
│   └── logging_config.yaml     # Logging configuration
├── src/                        # Source code
│   ├── __init__.py
│   ├── extractors/             # Data extraction modules
│   │   ├── __init__.py
│   │   ├── base_extractor.py   # Abstract base class
│   │   ├── map_pro_extractor.py
│   │   ├── pioneer_rx_extractor.py
│   │   ├── allocare_extractor.py
│   │   └── extraction_utils.py
│   ├── normalizers/            # Medication normalization
│   │   ├── __init__.py
│   │   ├── rxnorm_mapper.py    # RxNorm API integration
│   │   ├── dose_normalizer.py  # Dose standardization
│   │   ├── medication_matcher.py # Fuzzy matching logic
│   │   └── normalization_utils.py
│   ├── transformers/           # OMOP CDM transformation
│   │   ├── __init__.py
│   │   ├── omop_transformer.py # Main OMOP converter
│   │   ├── drug_exposure_builder.py
│   │   ├── drug_era_builder.py
│   │   ├── dose_era_builder.py
│   │   └── vocabulary_builder.py
│   ├── validators/             # Quality assurance
│   │   ├── __init__.py
│   │   ├── data_validator.py   # Data quality checks
│   │   ├── mapping_validator.py # RxNorm mapping validation
│   │   └── omop_validator.py   # OMOP CDM validation
│   ├── utils/                  # Shared utilities
│   │   ├── __init__.py
│   │   ├── database_connector.py
│   │   ├── logger.py
│   │   ├── exceptions.py
│   │   └── constants.py
│   └── pipeline/               # Main orchestration
│       ├── __init__.py
│       ├── medication_pipeline.py
│       └── pipeline_runner.py
├── tests/                      # Unit and integration tests
│   ├── __init__.py
│   ├── test_extractors/
│   ├── test_normalizers/
│   ├── test_transformers/
│   └── test_pipeline/
├── scripts/                    # Standalone execution scripts
│   ├── run_full_pipeline.py    # Complete pipeline execution
│   ├── run_extraction_only.py  # Extract data only
│   ├── run_normalization_only.py
│   └── validate_results.py
├── output/                     # Generated output files
│   ├── extracted/              # Raw extracted data
│   ├── normalized/             # Normalized medication data
│   ├── omop/                   # OMOP CDM formatted data
│   └── reports/                # Quality and validation reports
└── docs/                       # Additional documentation
    ├── user_guide.md
    ├── api_reference.md
    └── troubleshooting.md
```

## Core Components Design

### 1. Extraction Layer
**Purpose**: Extract and standardize medication data from heterogeneous sources

**Base Extractor Interface**:
```python
class BaseExtractor:
    def extract_medications(self) -> pd.DataFrame
    def extract_patients(self) -> pd.DataFrame
    def validate_extraction(self) -> ValidationReport
```

**Unified Extraction Schema**:
```python
{
    'source_system': str,           # Source identifier
    'patient_id': str,              # Source patient ID
    'medication_text': str,         # Raw medication name/description
    'medication_code': str,         # Source medication code (NDC, ItemID, etc.)
    'dose_value': float,            # Numeric dose
    'dose_unit': str,               # Dose unit (mg, ml, etc.)
    'route': str,                   # Administration route
    'frequency': str,               # Dosing frequency
    'start_date': datetime,         # Start date
    'end_date': datetime,           # End date (if available)
    'source_table': str,            # Source table name
    'source_record_id': str         # Source record identifier
}
```

### 2. Normalization Layer
**Purpose**: Map medications to RxNorm and standardize doses

**RxNorm Mapping Strategy**:
1. **Direct Mapping**: Use existing RxCUI from MAP Pro
2. **API Lookup**: Query RxNorm API for unmapped medications
3. **Fuzzy Matching**: Use string similarity for partial matches
4. **Manual Review**: Flag unmapped medications for review

**Dose Normalization**:
- Standardize units (mg, mcg, g, ml, etc.)
- Convert strengths to consistent formats
- Handle combination medications

### 3. Transformation Layer
**Purpose**: Convert normalized data to OMOP CDM format

**OMOP Table Generation**:
- **DRUG_EXPOSURE**: Primary medication records
- **DRUG_ERA**: Continuous medication periods
- **DOSE_ERA**: Dose-specific periods
- **Vocabulary Updates**: Custom concept mappings

### 4. Validation Layer
**Purpose**: Ensure data quality and completeness

**Validation Checks**:
- Source data completeness
- RxNorm mapping coverage
- OMOP CDM compliance
- Cross-source consistency
- Transplant medication coverage

## Data Flow Architecture

### Phase 1: Extraction
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ MAP Pro DB  │───▶│ MAP Pro     │───▶│  Unified    │
│             │    │ Extractor   │    │  Schema     │
└─────────────┘    └─────────────┘    │             │
┌─────────────┐    ┌─────────────┐    │             │
│ PioneerRx   │───▶│ PioneerRx   │───▶│             │
│ DB          │    │ Extractor   │    │             │
└─────────────┘    └─────────────┘    │             │
┌─────────────┐    ┌─────────────┐    │             │
│ AlloCare    │───▶│ AlloCare    │───▶│             │
│ DB          │    │ Extractor   │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
```

### Phase 2: Normalization
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Unified    │───▶│  RxNorm     │───▶│ Normalized  │
│  Schema     │    │  Mapper     │    │ Medications │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │ Dose        │
                   │ Normalizer  │
                   └─────────────┘
```

### Phase 3: OMOP Transformation
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Normalized  │───▶│ OMOP        │───▶│ OMOP CDM    │
│ Medications │    │ Transformer │    │ Tables      │
└─────────────┘    └─────────────┘    └─────────────┘
```

## Integration Points for Curtis

### 1. Configuration-Driven Execution
```bash
# Full pipeline
python scripts/run_full_pipeline.py --config config/production.yaml

# Specific sources only
python scripts/run_extraction_only.py --sources map_pro,pioneer_rx

# Custom output location
python scripts/run_full_pipeline.py --output-dir /path/to/warehouse/staging
```

### 2. Programmatic API
```python
from medication_normalization import MedicationPipeline

pipeline = MedicationPipeline(config_path="config/production.yaml")
results = pipeline.run_full_pipeline()
```

### 3. Output Formats
- **CSV files**: For easy integration with ETL tools
- **Parquet files**: For big data workflows
- **Database inserts**: Direct OMOP CDM table population
- **JSON reports**: Quality metrics and validation results

## Error Handling & Monitoring

### Logging Strategy
- **Structured logging**: JSON format for easy parsing
- **Log levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Component-specific logs**: Separate logs per module
- **Performance metrics**: Execution times and record counts

### Error Recovery
- **Graceful degradation**: Continue processing on partial failures
- **Retry mechanisms**: For API calls and database connections
- **Fallback strategies**: Alternative mapping approaches
- **Error reporting**: Detailed error summaries for review

## Quality Assurance

### Validation Metrics
- **Coverage rates**: Percentage of medications mapped to RxNorm
- **Mapping confidence**: Quality scores for fuzzy matches
- **Data completeness**: Missing value analysis
- **Cross-source consistency**: Overlap analysis between sources

### Quality Reports
- **Executive summary**: High-level metrics for stakeholders
- **Technical details**: Detailed validation results
- **Unmapped medications**: List for manual review
- **Suggested improvements**: Recommendations for data quality

This architecture provides Curtis with a robust, maintainable system that can be easily integrated into his data warehouse workflow while giving you clear development targets.