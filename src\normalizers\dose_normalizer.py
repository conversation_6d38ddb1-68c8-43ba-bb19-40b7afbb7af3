"""
Dose normalization and standardization functionality.
"""

import pandas as pd
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from decimal import Decimal, InvalidOperation

from ..utils.logger import LoggerMixin
from ..utils.constants import STANDARD_DOSE_UNITS, DOSE_FREQUENCIES, MEDICATION_ROUTES


class DoseNormalizer(LoggerMixin):
    """
    Comprehensive dose normalization and standardization.

    Handles dose values, units, frequencies, and routes to create
    consistent medication dosing information across all sources.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize dose normalizer.

        Args:
            config: Dose normalization configuration
        """
        self.config = config
        self.dose_config = config.get('dose_normalization', {})

        self.standard_units = self.dose_config.get('standard_units', STANDARD_DOSE_UNITS)
        self.unit_conversions = self.dose_config.get('unit_conversions', {})

        # Build comprehensive unit conversion tables
        self._build_conversion_tables()

        # Dose validation limits
        self.dose_limits = config.get('quality', {}).get('dose_limits', {
            'min_dose': 0.001,
            'max_dose': 10000
        })

    def _build_conversion_tables(self) -> None:
        """Build comprehensive unit conversion tables."""
        # Weight conversions (normalize to mg)
        self.weight_conversions = {
            # Basic units
            'ng': 0.000001,
            'mcg': 0.001,
            'μg': 0.001,  # Greek mu
            'ug': 0.001,   # ASCII alternative
            'mg': 1.0,
            'g': 1000.0,
            'kg': 1000000.0,

            # Common variations
            'milligram': 1.0,
            'milligrams': 1.0,
            'microgram': 0.001,
            'micrograms': 0.001,
            'gram': 1000.0,
            'grams': 1000.0,
            'nanogram': 0.000001,
            'nanograms': 0.000001,

            # With periods
            'mg.': 1.0,
            'mcg.': 0.001,
            'g.': 1000.0,

            # International units (no conversion - keep as-is)
            'units': None,
            'unit': None,
            'iu': None,
            'i.u.': None,
            'miu': None,  # Million IU
            'international units': None,
            'international unit': None
        }

        # Volume conversions (normalize to ml)
        self.volume_conversions = {
            'ul': 0.001,
            'μl': 0.001,
            'microliters': 0.001,
            'ml': 1.0,
            'milliliter': 1.0,
            'milliliters': 1.0,
            'cc': 1.0,  # Cubic centimeter
            'cm3': 1.0,
            'l': 1000.0,
            'liter': 1000.0,
            'liters': 1000.0,

            # With periods
            'ml.': 1.0,
            'l.': 1000.0
        }

        # Percentage and other units (no conversion)
        self.other_units = {
            '%': 1.0,
            'percent': 1.0,
            'percentage': 1.0,
            'ppm': 1.0,  # Parts per million
            'ratio': 1.0,
            'meq': None,  # Milliequivalents
            'mmol': None,  # Millimoles
            'mol': None   # Moles
        }

        # Apply custom conversions from config
        for original, converted in self.unit_conversions.items():
            original_lower = original.lower()

            # Determine which category this belongs to
            if converted in ['mg', 'g', 'mcg', 'ng']:
                if converted == 'mg':
                    self.weight_conversions[original_lower] = 1.0
                elif converted == 'g':
                    self.weight_conversions[original_lower] = 1000.0
                elif converted == 'mcg':
                    self.weight_conversions[original_lower] = 0.001
                elif converted == 'ng':
                    self.weight_conversions[original_lower] = 0.000001

            elif converted in ['ml', 'l']:
                if converted == 'ml':
                    self.volume_conversions[original_lower] = 1.0
                elif converted == 'l':
                    self.volume_conversions[original_lower] = 1000.0

    def normalize_dose_value(self, dose_input: Union[str, int, float]) -> Optional[float]:
        """
        Normalize dose value to a standard numeric format.

        Args:
            dose_input: Raw dose value (string, int, or float)

        Returns:
            Normalized dose value or None if invalid
        """
        if pd.isna(dose_input) or dose_input is None:
            return None

        try:
            # If already numeric, validate and return
            if isinstance(dose_input, (int, float)):
                dose_value = float(dose_input)
                return dose_value if self._is_valid_dose(dose_value) else None

            # Convert to string for processing
            dose_str = str(dose_input).strip().lower()

            if not dose_str:
                return None

            # Handle ranges (e.g., "5-10", "2.5 to 5")
            range_match = re.search(r'(\d+\.?\d*)\s*[-–—to]\s*(\d+\.?\d*)', dose_str)
            if range_match:
                low = float(range_match.group(1))
                high = float(range_match.group(2))
                return (low + high) / 2  # Return average of range

            # Handle fractions (e.g., "1/2", "0.5/1")
            fraction_match = re.search(r'(\d+\.?\d*)\s*/\s*(\d+\.?\d*)', dose_str)
            if fraction_match:
                numerator = float(fraction_match.group(1))
                denominator = float(fraction_match.group(2))
                if denominator != 0:
                    return numerator / denominator

            # Extract numeric value from string
            numeric_match = re.search(r'(\d+\.?\d*)', dose_str)
            if numeric_match:
                dose_value = float(numeric_match.group(1))
                return dose_value if self._is_valid_dose(dose_value) else None

            return None

        except (ValueError, TypeError, InvalidOperation) as e:
            self.logger.debug(f"Failed to normalize dose value '{dose_input}': {str(e)}")
            return None

    def normalize_dose_unit(self, unit_input: Union[str, None]) -> Optional[str]:
        """
        Normalize dose unit to standard format.

        Args:
            unit_input: Raw dose unit

        Returns:
            Normalized unit or None
        """
        if pd.isna(unit_input) or unit_input is None:
            return None

        unit_str = str(unit_input).strip().lower()

        if not unit_str:
            return None

        # Remove common prefixes/suffixes
        unit_str = re.sub(r'[^\w.]', '', unit_str)  # Keep only word chars and periods

        # Check weight units
        if unit_str in self.weight_conversions:
            if self.weight_conversions[unit_str] is None:
                return unit_str  # Keep as-is for units like IU
            elif self.weight_conversions[unit_str] == 1.0:
                return 'mg'
            elif self.weight_conversions[unit_str] == 1000.0:
                return 'g'
            elif self.weight_conversions[unit_str] == 0.001:
                return 'mcg'
            elif self.weight_conversions[unit_str] == 0.000001:
                return 'ng'

        # Check volume units
        if unit_str in self.volume_conversions:
            if self.volume_conversions[unit_str] == 1.0:
                return 'ml'
            elif self.volume_conversions[unit_str] == 1000.0:
                return 'l'

        # Check other units
        if unit_str in self.other_units:
            if unit_str == 'percent':
                return '%'
            else:
                return unit_str

        # Handle compound units (e.g., "mg/ml", "mcg/kg")
        if '/' in unit_str:
            parts = unit_str.split('/')
            if len(parts) == 2:
                numerator = self.normalize_dose_unit(parts[0])
                denominator = self.normalize_dose_unit(parts[1])
                if numerator and denominator:
                    return f"{numerator}/{denominator}"

        # Return original if no normalization found
        return unit_str

    def convert_dose_to_standard_unit(
        self,
        dose_value: float,
        original_unit: str,
        target_unit: str = None
    ) -> Tuple[Optional[float], Optional[str]]:
        """
        Convert dose to standard unit.

        Args:
            dose_value: Original dose value
            original_unit: Original unit
            target_unit: Target unit (if None, auto-select standard)

        Returns:
            Tuple of (converted_value, standard_unit)
        """
        if dose_value is None or original_unit is None:
            return None, None

        original_lower = original_unit.lower()

        # Weight conversions
        if original_lower in self.weight_conversions:
            conversion_factor = self.weight_conversions[original_lower]

            if conversion_factor is None:
                # Units like IU - no conversion
                return dose_value, original_unit

            if target_unit:
                target_lower = target_unit.lower()
                if target_lower in self.weight_conversions:
                    target_factor = self.weight_conversions[target_lower]
                    if target_factor is not None:
                        converted_value = dose_value * conversion_factor / target_factor
                        return converted_value, target_unit

            # Auto-select appropriate unit based on magnitude
            mg_value = dose_value * conversion_factor

            if mg_value >= 1000:
                return mg_value / 1000, 'g'
            elif mg_value >= 1:
                return mg_value, 'mg'
            elif mg_value >= 0.001:
                return mg_value * 1000, 'mcg'
            else:
                return mg_value * 1000000, 'ng'

        # Volume conversions
        if original_lower in self.volume_conversions:
            conversion_factor = self.volume_conversions[original_lower]
            ml_value = dose_value * conversion_factor

            if target_unit and target_unit.lower() in self.volume_conversions:
                target_factor = self.volume_conversions[target_unit.lower()]
                converted_value = ml_value / target_factor
                return converted_value, target_unit

            # Auto-select unit
            if ml_value >= 1000:
                return ml_value / 1000, 'l'
            else:
                return ml_value, 'ml'

        # No conversion available
        return dose_value, original_unit

    def normalize_frequency(self, frequency_input: Union[str, None]) -> Optional[str]:
        """
        Normalize dosing frequency to standard format.

        Args:
            frequency_input: Raw frequency text

        Returns:
            Normalized frequency or None
        """
        if pd.isna(frequency_input) or frequency_input is None:
            return None

        freq_str = str(frequency_input).strip().lower()

        if not freq_str:
            return None

        # Remove extra whitespace and punctuation
        freq_str = re.sub(r'[^\w\s]', ' ', freq_str)
        freq_str = re.sub(r'\s+', ' ', freq_str).strip()

        # Check against known frequency patterns
        for standard_freq, patterns in DOSE_FREQUENCIES.items():
            for pattern in patterns:
                if pattern.lower() in freq_str:
                    return standard_freq.replace('_', ' ')

        # Handle numeric patterns (e.g., "2 times daily", "3x per day")
        numeric_patterns = [
            r'(\d+)\s*times?\s+(daily|per\s+day|day)',
            r'(\d+)x\s+(daily|per\s+day|day)',
            r'(\d+)\s*per\s+day',
            r'(\d+)\s*/\s*day'
        ]

        for pattern in numeric_patterns:
            match = re.search(pattern, freq_str)
            if match:
                times = int(match.group(1))
                if times == 1:
                    return 'once daily'
                elif times == 2:
                    return 'twice daily'
                elif times == 3:
                    return 'three times daily'
                elif times == 4:
                    return 'four times daily'
                else:
                    return f'{times} times daily'

        # Handle interval patterns (e.g., "every 8 hours", "q8h")
        interval_patterns = [
            r'q(\d+)h',
            r'every\s+(\d+)\s+hours?',
            r'(\d+)\s+hourly'
        ]

        for pattern in interval_patterns:
            match = re.search(pattern, freq_str)
            if match:
                hours = int(match.group(1))
                times_per_day = 24 / hours if hours > 0 else 0

                if abs(times_per_day - 1) < 0.1:
                    return 'once daily'
                elif abs(times_per_day - 2) < 0.1:
                    return 'twice daily'
                elif abs(times_per_day - 3) < 0.1:
                    return 'three times daily'
                elif abs(times_per_day - 4) < 0.1:
                    return 'four times daily'
                else:
                    return f'every {hours} hours'

        # Return cleaned original if no pattern matches
        return freq_str if len(freq_str) <= 50 else None

    def normalize_route(self, route_input: Union[str, None]) -> Optional[str]:
        """
        Normalize administration route to standard format.

        Args:
            route_input: Raw route text

        Returns:
            Normalized route or None
        """
        if pd.isna(route_input) or route_input is None:
            return None

        route_str = str(route_input).strip().lower()

        if not route_str:
            return None

        # Remove punctuation
        route_str = re.sub(r'[^\w\s]', ' ', route_str)
        route_str = re.sub(r'\s+', ' ', route_str).strip()

        # Check against known route patterns
        for standard_route, patterns in MEDICATION_ROUTES.items():
            for pattern in patterns:
                if pattern.lower() in route_str or route_str in pattern.lower():
                    return standard_route

        # Return cleaned original if no pattern matches
        return route_str if len(route_str) <= 30 else None

    def _is_valid_dose(self, dose_value: float) -> bool:
        """
        Validate dose value against reasonable limits.

        Args:
            dose_value: Dose value to validate

        Returns:
            True if valid, False otherwise
        """
        if dose_value is None or pd.isna(dose_value):
            return False

        min_dose = self.dose_limits.get('min_dose', 0.001)
        max_dose = self.dose_limits.get('max_dose', 10000)

        return min_dose <= dose_value <= max_dose

    def normalize_medication_dosing(
        self,
        medications_df: pd.DataFrame,
        dose_value_column: str = 'dose_value',
        dose_unit_column: str = 'dose_unit',
        frequency_column: str = 'frequency',
        route_column: str = 'route'
    ) -> pd.DataFrame:
        """
        Normalize all dosing information for a DataFrame of medications.

        Args:
            medications_df: DataFrame with medication data
            dose_value_column: Column containing dose values
            dose_unit_column: Column containing dose units
            frequency_column: Column containing frequencies
            route_column: Column containing routes

        Returns:
            DataFrame with normalized dosing information
        """
        if medications_df.empty:
            return medications_df

        self.logger.info(f"Normalizing dosing information for {len(medications_df)} medications")

        result_df = medications_df.copy()

        # Add normalized columns
        result_df['normalized_dose_value'] = None
        result_df['normalized_dose_unit'] = None
        result_df['normalized_frequency'] = None
        result_df['normalized_route'] = None
        result_df['standard_dose_value'] = None
        result_df['standard_dose_unit'] = None
        result_df['dose_normalization_status'] = 'pending'

        # Process each row
        normalized_count = 0
        error_count = 0

        for idx, row in result_df.iterrows():
            try:
                # Normalize dose value
                original_dose = row.get(dose_value_column)
                normalized_dose = self.normalize_dose_value(original_dose)
                result_df.at[idx, 'normalized_dose_value'] = normalized_dose

                # Normalize dose unit
                original_unit = row.get(dose_unit_column)
                normalized_unit = self.normalize_dose_unit(original_unit)
                result_df.at[idx, 'normalized_dose_unit'] = normalized_unit

                # Convert to standard unit if both dose and unit are available
                if normalized_dose is not None and normalized_unit is not None:
                    standard_dose, standard_unit = self.convert_dose_to_standard_unit(
                        normalized_dose, normalized_unit
                    )
                    result_df.at[idx, 'standard_dose_value'] = standard_dose
                    result_df.at[idx, 'standard_dose_unit'] = standard_unit

                # Normalize frequency
                original_freq = row.get(frequency_column)
                normalized_freq = self.normalize_frequency(original_freq)
                result_df.at[idx, 'normalized_frequency'] = normalized_freq

                # Normalize route
                original_route = row.get(route_column)
                normalized_route = self.normalize_route(original_route)
                result_df.at[idx, 'normalized_route'] = normalized_route

                # Set status
                if any([normalized_dose, normalized_unit, normalized_freq, normalized_route]):
                    result_df.at[idx, 'dose_normalization_status'] = 'completed'
                    normalized_count += 1
                else:
                    result_df.at[idx, 'dose_normalization_status'] = 'no_data'

            except Exception as e:
                self.logger.error(f"Failed to normalize dosing for row {idx}: {str(e)}")
                result_df.at[idx, 'dose_normalization_status'] = 'error'
                error_count += 1

        # Calculate statistics
        total_count = len(result_df)
        normalization_rate = (normalized_count / total_count * 100) if total_count > 0 else 0

        self.logger.info(
            f"Dose normalization complete: {normalized_count}/{total_count} "
            f"({normalization_rate:.1f}%) normalized, {error_count} errors"
        )

        return result_df

    def generate_dose_normalization_report(self, medications_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate comprehensive dose normalization report.

        Args:
            medications_df: DataFrame with normalized dosing data

        Returns:
            Detailed normalization report
        """
        if medications_df.empty:
            return {}

        total_count = len(medications_df)

        # Analyze normalization status
        status_counts = {}
        if 'dose_normalization_status' in medications_df.columns:
            status_counts = medications_df['dose_normalization_status'].value_counts().to_dict()

        # Analyze dose values
        dose_stats = {}
        if 'normalized_dose_value' in medications_df.columns:
            dose_series = pd.to_numeric(medications_df['normalized_dose_value'], errors='coerce')
            dose_stats = {
                'count': dose_series.count(),
                'mean': dose_series.mean(),
                'median': dose_series.median(),
                'min': dose_series.min(),
                'max': dose_series.max(),
                'std': dose_series.std()
            }

        # Analyze units
        unit_counts = {}
        if 'normalized_dose_unit' in medications_df.columns:
            unit_counts = medications_df['normalized_dose_unit'].value_counts().to_dict()

        # Analyze frequencies
        frequency_counts = {}
        if 'normalized_frequency' in medications_df.columns:
            frequency_counts = medications_df['normalized_frequency'].value_counts().to_dict()

        # Analyze routes
        route_counts = {}
        if 'normalized_route' in medications_df.columns:
            route_counts = medications_df['normalized_route'].value_counts().to_dict()

        return {
            'summary': {
                'total_medications': total_count,
                'normalization_status': status_counts,
                'dose_value_coverage': dose_stats.get('count', 0) / total_count * 100 if total_count > 0 else 0,
                'unit_coverage': len([u for u in medications_df.get('normalized_dose_unit', []) if pd.notna(u)]) / total_count * 100 if total_count > 0 else 0
            },
            'dose_statistics': dose_stats,
            'unit_distribution': unit_counts,
            'frequency_distribution': frequency_counts,
            'route_distribution': route_counts,
            'configuration': {
                'dose_limits': self.dose_limits,
                'supported_weight_units': list(self.weight_conversions.keys()),
                'supported_volume_units': list(self.volume_conversions.keys()),
                'supported_frequencies': list(DOSE_FREQUENCIES.keys()),
                'supported_routes': list(MEDICATION_ROUTES.keys())
            }
        }