"""
RxNorm mapping validation and quality assessment.
"""

import pandas as pd
import requests
from typing import Dict, Any, List, Optional, Tuple
from collections import Counter
import re

from ..utils.logger import LoggerMixin
from ..utils.constants import (
    RXNORM_TERM_TYPE_PREFERENCE,
    IMMUNOSUPPRESSIVE_MEDICATIONS,
    ValidationSeverity
)


class MappingValidator(LoggerMixin):
    """
    Validates RxNorm mapping quality and accuracy.

    Performs quality checks on medication normalization results,
    validates RxCUI integrity, and assesses mapping confidence.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize mapping validator.

        Args:
            config: RxNorm and mapping configuration
        """
        self.config = config
        self.rxnorm_api_base = config.get('api', {}).get('base_url', 'https://rxnav.nlm.nih.gov/REST')

        # Validation thresholds
        self.min_mapping_rate = 0.7  # 70% minimum mapping rate
        self.min_confidence_score = 0.8  # 80% minimum confidence
        self.max_api_error_rate = 0.05  # 5% maximum API error rate

        # Transplant medication tracking
        self.transplant_keywords = self._build_transplant_keywords()

    def _build_transplant_keywords(self) -> List[str]:
        """Build list of transplant medication keywords for validation."""
        keywords = []
        for category, medications in IMMUNOSUPPRESSIVE_MEDICATIONS.items():
            keywords.extend([med.lower() for med in medications])
        return keywords

    def validate_mapping_results(self, medications_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Comprehensive validation of mapping results.

        Args:
            medications_df: DataFrame with mapping results

        Returns:
            Validation report dictionary
        """
        if medications_df.empty:
            return {
                'status': 'skipped',
                'message': 'Empty DataFrame - no mapping validation performed'
            }

        self.logger.info(f"Validating mapping results for {len(medications_df)} medications")

        validation_results = {}

        # 1. Overall mapping coverage
        validation_results['mapping_coverage'] = self._validate_mapping_coverage(medications_df)

        # 2. RxCUI validity
        validation_results['rxcui_validity'] = self._validate_rxcui_integrity(medications_df)

        # 3. Mapping confidence assessment
        validation_results['confidence_assessment'] = self._assess_mapping_confidence(medications_df)

        # 4. Transplant medication coverage
        validation_results['transplant_coverage'] = self._validate_transplant_medication_mapping(medications_df)

        # 5. Term type distribution
        validation_results['term_type_analysis'] = self._analyze_term_types(medications_df)

        # 6. Cross-source consistency
        validation_results['cross_source_consistency'] = self._validate_cross_source_consistency(medications_df)

        # 7. Mapping method effectiveness
        validation_results['method_effectiveness'] = self._analyze_mapping_methods(medications_df)

        # Generate overall assessment
        overall_status = self._determine_overall_mapping_status(validation_results)

        return {
            'status': overall_status,
            'summary': self._generate_mapping_summary(validation_results, medications_df),
            'detailed_results': validation_results,
            'recommendations': self._generate_mapping_recommendations(validation_results)
        }

    def _validate_mapping_coverage(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate overall mapping coverage rates."""
        total_count = len(df)

        # Count different types of mappings
        has_final_rxcui = df['final_rxcui'].notna().sum() if 'final_rxcui' in df.columns else 0
        has_reference_match = df['has_reference_match'].sum() if 'has_reference_match' in df.columns else 0
        has_api_match = df['is_mapped'].sum() if 'is_mapped' in df.columns else 0

        overall_rate = has_final_rxcui / total_count if total_count > 0 else 0
        reference_rate = has_reference_match / total_count if total_count > 0 else 0
        api_rate = has_api_match / total_count if total_count > 0 else 0

        # Determine status
        status = 'passed' if overall_rate >= self.min_mapping_rate else 'failed'

        return {
            'status': status,
            'overall_mapping_rate': overall_rate,
            'reference_mapping_rate': reference_rate,
            'api_mapping_rate': api_rate,
            'total_mapped': has_final_rxcui,
            'total_unmapped': total_count - has_final_rxcui,
            'meets_threshold': overall_rate >= self.min_mapping_rate,
            'threshold': self.min_mapping_rate
        }

    def _validate_rxcui_integrity(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate RxCUI format and basic integrity."""
        if 'final_rxcui' not in df.columns:
            return {'status': 'skipped', 'message': 'No final_rxcui column found'}

        rxcui_series = df['final_rxcui'].dropna()
        total_rxcuis = len(rxcui_series)

        if total_rxcuis == 0:
            return {'status': 'failed', 'message': 'No RxCUIs found to validate'}

        # Validate RxCUI format (should be numeric strings)
        valid_format = rxcui_series.astype(str).str.match(r'^\d+$')
        invalid_format_count = (~valid_format).sum()

        # Check for suspicious patterns
        suspicious_patterns = []

        # Very short RxCUIs (suspicious)
        short_rxcuis = rxcui_series.astype(str).str.len() < 3
        suspicious_patterns.append(('very_short', short_rxcuis.sum()))

        # Very long RxCUIs (suspicious)
        long_rxcuis = rxcui_series.astype(str).str.len() > 8
        suspicious_patterns.append(('very_long', long_rxcuis.sum()))

        # Common invalid values
        zero_rxcuis = (rxcui_series.astype(str) == '0').sum()
        suspicious_patterns.append(('zero_values', zero_rxcuis))

        format_validation_rate = valid_format.sum() / total_rxcuis if total_rxcuis > 0 else 0
        status = 'passed' if format_validation_rate > 0.95 else 'failed'

        return {
            'status': status,
            'total_rxcuis': total_rxcuis,
            'valid_format_count': valid_format.sum(),
            'invalid_format_count': invalid_format_count,
            'format_validation_rate': format_validation_rate,
            'suspicious_patterns': dict(suspicious_patterns),
            'sample_invalid': rxcui_series[~valid_format].head(10).tolist() if invalid_format_count > 0 else []
        }

    def _assess_mapping_confidence(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Assess confidence levels of mappings."""
        if 'normalization_confidence' not in df.columns:
            return {'status': 'skipped', 'message': 'No confidence scores available'}

        confidence_series = pd.to_numeric(df['normalization_confidence'], errors='coerce').dropna()
        total_with_confidence = len(confidence_series)

        if total_with_confidence == 0:
            return {'status': 'failed', 'message': 'No valid confidence scores found'}

        # Analyze confidence distribution
        high_confidence = (confidence_series >= 0.9).sum()
        medium_confidence = ((confidence_series >= 0.7) & (confidence_series < 0.9)).sum()
        low_confidence = (confidence_series < 0.7).sum()

        avg_confidence = confidence_series.mean()
        median_confidence = confidence_series.median()

        # Check if average confidence meets threshold
        status = 'passed' if avg_confidence >= self.min_confidence_score else 'warning'

        return {
            'status': status,
            'total_with_confidence': total_with_confidence,
            'average_confidence': avg_confidence,
            'median_confidence': median_confidence,
            'confidence_distribution': {
                'high': high_confidence,
                'medium': medium_confidence,
                'low': low_confidence
            },
            'confidence_percentiles': {
                '25th': confidence_series.quantile(0.25),
                '75th': confidence_series.quantile(0.75),
                '90th': confidence_series.quantile(0.90)
            },
            'meets_threshold': avg_confidence >= self.min_confidence_score
        }

    def _validate_transplant_medication_mapping(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate mapping coverage for transplant medications."""
        if 'medication_text' not in df.columns:
            return {'status': 'skipped', 'message': 'No medication text available'}

        transplant_mask = df['medication_text'].str.lower().str.contains(
            '|'.join(self.transplant_keywords), na=False, regex=True
        )

        transplant_df = df[transplant_mask]
        total_transplant = len(transplant_df)

        if total_transplant == 0:
            return {
                'status': 'info',
                'message': 'No transplant medications detected in dataset',
                'total_transplant_medications': 0
            }

        # Check mapping rates for transplant medications
        mapped_transplant = transplant_df['final_rxcui'].notna().sum() if 'final_rxcui' in transplant_df.columns else 0
        flagged_transplant = transplant_df['is_transplant_medication'].sum() if 'is_transplant_medication' in transplant_df.columns else 0

        mapping_rate = mapped_transplant / total_transplant if total_transplant > 0 else 0
        flagging_rate = flagged_transplant / total_transplant if total_transplant > 0 else 0

        # Analyze by category
        category_analysis = {}
        if 'transplant_category' in transplant_df.columns:
            category_counts = transplant_df['transplant_category'].value_counts()
            category_analysis = category_counts.to_dict()

        status = 'passed' if mapping_rate >= 0.8 else 'warning'

        return {
            'status': status,
            'total_transplant_medications': total_transplant,
            'mapped_transplant_medications': mapped_transplant,
            'flagged_transplant_medications': flagged_transplant,
            'transplant_mapping_rate': mapping_rate,
            'transplant_flagging_rate': flagging_rate,
            'category_distribution': category_analysis,
            'sample_transplant_medications': transplant_df['medication_text'].head(10).tolist()
        }

    def _analyze_term_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze distribution of RxNorm term types."""
        term_type_columns = ['rxnorm_tty', 'reference_match_type']
        term_type_data = {}

        for col in term_type_columns:
            if col in df.columns:
                tty_counts = df[col].value_counts()
                term_type_data[col] = tty_counts.to_dict()

        # Analyze preference alignment
        preferred_alignment = 0
        total_with_tty = 0

        if 'rxnorm_tty' in df.columns:
            tty_series = df['rxnorm_tty'].dropna()
            total_with_tty = len(tty_series)

            for tty in tty_series:
                if tty in RXNORM_TERM_TYPE_PREFERENCE[:3]:  # Top 3 preferred
                    preferred_alignment += 1

        preference_rate = preferred_alignment / total_with_tty if total_with_tty > 0 else 0

        return {
            'term_type_distributions': term_type_data,
            'total_with_term_types': total_with_tty,
            'preferred_term_types': preferred_alignment,
            'preference_alignment_rate': preference_rate,
            'preferred_types': RXNORM_TERM_TYPE_PREFERENCE[:5]
        }

    def _validate_cross_source_consistency(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate mapping consistency across data sources."""
        if 'source_system' not in df.columns:
            return {'status': 'skipped', 'message': 'No source system information'}

        source_analysis = {}

        for source in df['source_system'].unique():
            source_df = df[df['source_system'] == source]
            total_source = len(source_df)

            if total_source == 0:
                continue

            mapped_source = source_df['final_rxcui'].notna().sum() if 'final_rxcui' in source_df.columns else 0
            mapping_rate = mapped_source / total_source

            source_analysis[source] = {
                'total_medications': total_source,
                'mapped_medications': mapped_source,
                'mapping_rate': mapping_rate,
                'avg_confidence': pd.to_numeric(
                    source_df['normalization_confidence'], errors='coerce'
                ).mean() if 'normalization_confidence' in source_df.columns else None
            }

        # Check for significant differences between sources
        mapping_rates = [info['mapping_rate'] for info in source_analysis.values()]
        rate_variance = max(mapping_rates) - min(mapping_rates) if mapping_rates else 0

        status = 'passed' if rate_variance < 0.2 else 'warning'  # 20% variance threshold

        return {
            'status': status,
            'source_analysis': source_analysis,
            'mapping_rate_variance': rate_variance,
            'consistent_performance': rate_variance < 0.2
        }

    def _analyze_mapping_methods(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze effectiveness of different mapping methods."""
        if 'normalization_method' not in df.columns:
            return {'status': 'skipped', 'message': 'No normalization method information'}

        method_analysis = {}

        for method in df['normalization_method'].unique():
            if pd.isna(method):
                continue

            method_df = df[df['normalization_method'] == method]
            total_method = len(method_df)

            if total_method == 0:
                continue

            avg_confidence = pd.to_numeric(
                method_df['normalization_confidence'], errors='coerce'
            ).mean()

            method_analysis[method] = {
                'count': total_method,
                'percentage': total_method / len(df) * 100,
                'avg_confidence': avg_confidence,
                'success_rate': 1.0  # All methods that reach this point were successful
            }

        # Rank methods by effectiveness
        method_ranking = sorted(
            method_analysis.items(),
            key=lambda x: (x[1]['avg_confidence'] or 0, x[1]['count']),
            reverse=True
        )

        return {
            'method_analysis': method_analysis,
            'method_ranking': [method for method, _ in method_ranking],
            'most_effective_method': method_ranking[0][0] if method_ranking else None,
            'method_diversity': len(method_analysis)
        }

    def _determine_overall_mapping_status(self, validation_results: Dict[str, Any]) -> str:
        """Determine overall mapping validation status."""
        critical_checks = ['mapping_coverage', 'rxcui_validity']
        warning_checks = ['confidence_assessment', 'transplant_coverage']

        # Check critical validations
        for check in critical_checks:
            if check in validation_results:
                if validation_results[check].get('status') == 'failed':
                    return 'failed'

        # Check warning validations
        has_warnings = False
        for check in warning_checks:
            if check in validation_results:
                if validation_results[check].get('status') in ['warning', 'failed']:
                    has_warnings = True

        return 'warnings' if has_warnings else 'passed'

    def _generate_mapping_summary(
        self,
        validation_results: Dict[str, Any],
        df: pd.DataFrame
    ) -> Dict[str, Any]:
        """Generate mapping validation summary."""
        total_medications = len(df)

        summary = {
            'total_medications': total_medications,
            'validation_timestamp': pd.Timestamp.now().isoformat()
        }

        # Add key metrics from validation results
        if 'mapping_coverage' in validation_results:
            coverage = validation_results['mapping_coverage']
            summary.update({
                'overall_mapping_rate': coverage.get('overall_mapping_rate', 0),
                'total_mapped': coverage.get('total_mapped', 0),
                'total_unmapped': coverage.get('total_unmapped', 0)
            })

        if 'confidence_assessment' in validation_results:
            confidence = validation_results['confidence_assessment']
            summary['average_confidence'] = confidence.get('average_confidence', 0)

        if 'transplant_coverage' in validation_results:
            transplant = validation_results['transplant_coverage']
            summary.update({
                'transplant_medications': transplant.get('total_transplant_medications', 0),
                'transplant_mapping_rate': transplant.get('transplant_mapping_rate', 0)
            })

        return summary

    def _generate_mapping_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        # Mapping coverage recommendations
        if 'mapping_coverage' in validation_results:
            coverage = validation_results['mapping_coverage']
            if not coverage.get('meets_threshold', True):
                recommendations.append(
                    f"Mapping rate ({coverage.get('overall_mapping_rate', 0):.1%}) is below "
                    f"threshold ({coverage.get('threshold', 0):.1%}). Consider expanding "
                    "reference data or improving fuzzy matching parameters."
                )

        # Confidence recommendations
        if 'confidence_assessment' in validation_results:
            confidence = validation_results['confidence_assessment']
            if not confidence.get('meets_threshold', True):
                recommendations.append(
                    f"Average confidence ({confidence.get('average_confidence', 0):.2f}) is low. "
                    "Review mapping algorithms and consider manual validation of low-confidence matches."
                )

        # RxCUI integrity recommendations
        if 'rxcui_validity' in validation_results:
            rxcui = validation_results['rxcui_validity']
            if rxcui.get('invalid_format_count', 0) > 0:
                recommendations.append(
                    f"{rxcui.get('invalid_format_count', 0)} invalid RxCUI formats detected. "
                    "Review mapping logic to ensure proper RxCUI generation."
                )

        # Transplant medication recommendations
        if 'transplant_coverage' in validation_results:
            transplant = validation_results['transplant_coverage']
            if transplant.get('transplant_mapping_rate', 1) < 0.8:
                recommendations.append(
                    "Transplant medication mapping rate is low. Consider adding more "
                    "transplant-specific terms to the reference vocabulary."
                )

        # Method effectiveness recommendations
        if 'method_effectiveness' in validation_results:
            methods = validation_results['method_effectiveness']
            if methods.get('method_diversity', 0) < 2:
                recommendations.append(
                    "Limited mapping method diversity. Consider enabling additional "
                    "mapping strategies for better coverage."
                )

        if not recommendations:
            recommendations.append("Mapping validation results look good. No specific actions needed.")

        return recommendations

    def validate_sample_rxcuis(
        self,
        rxcui_list: List[str],
        sample_size: int = 50
    ) -> Dict[str, Any]:
        """
        Validate a sample of RxCUIs against the RxNorm API.

        Args:
            rxcui_list: List of RxCUIs to validate
            sample_size: Number of RxCUIs to sample for validation

        Returns:
            Validation results
        """
        if not rxcui_list:
            return {'status': 'skipped', 'message': 'No RxCUIs provided for validation'}

        # Sample RxCUIs for validation
        sample_rxcuis = pd.Series(rxcui_list).dropna().astype(str).unique()
        if len(sample_rxcuis) > sample_size:
            sample_rxcuis = pd.Series(sample_rxcuis).sample(sample_size).tolist()

        self.logger.info(f"Validating {len(sample_rxcuis)} RxCUIs against RxNorm API")

        valid_count = 0
        invalid_count = 0
        error_count = 0
        validation_details = []

        for rxcui in sample_rxcuis:
            try:
                # Check if RxCUI exists in RxNorm
                url = f"{self.rxnorm_api_base}/rxcui/{rxcui}/properties.json"
                response = requests.get(url, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    properties = data.get('properties', {})

                    if properties and properties.get('rxcui') == rxcui:
                        valid_count += 1
                        validation_details.append({
                            'rxcui': rxcui,
                            'status': 'valid',
                            'name': properties.get('name'),
                            'tty': properties.get('tty')
                        })
                    else:
                        invalid_count += 1
                        validation_details.append({
                            'rxcui': rxcui,
                            'status': 'invalid',
                            'reason': 'RxCUI not found or mismatch'
                        })
                else:
                    invalid_count += 1
                    validation_details.append({
                        'rxcui': rxcui,
                        'status': 'invalid',
                        'reason': f'API error: {response.status_code}'
                    })

            except Exception as e:
                error_count += 1
                validation_details.append({
                    'rxcui': rxcui,
                    'status': 'error',
                    'reason': str(e)
                })

        total_validated = len(sample_rxcuis)
        validity_rate = valid_count / total_validated if total_validated > 0 else 0

        status = 'passed' if validity_rate >= 0.95 else 'failed'

        return {
            'status': status,
            'total_validated': total_validated,
            'valid_count': valid_count,
            'invalid_count': invalid_count,
            'error_count': error_count,
            'validity_rate': validity_rate,
            'sample_details': validation_details[:10],  # First 10 for reporting
            'recommendation': (
                'RxCUI validation passed' if status == 'passed'
                else 'Review RxCUI generation logic - high invalid rate detected'
            )
        }