#!/usr/bin/env python3
"""
Quality Monitor for Medication Normalization Pipeline

Monitors data quality, generates reports, and provides quality metrics
for the OMOP CDM medication normalization process.
"""

import sys
import argparse
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.validators import DataValidator, MappingValidator, OMOPValidator
from src.utils.logger import LoggerMixin


class QualityMonitor(LoggerMixin):
    """
    Monitors and reports on medication normalization quality.

    Provides comprehensive quality assessment including data validation,
    mapping coverage, OMOP compliance, and trend analysis.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize quality monitor."""
        self.config = config
        self.quality_config = config.get('validation', {})

        # Initialize validators
        self.data_validator = DataValidator(config)
        self.mapping_validator = MappingValidator(config)
        self.omop_validator = OMOPValidator(config)

        # Quality thresholds
        self.thresholds = {
            'min_mapping_coverage': self.quality_config.get('mapping_validation', {}).get('min_mapping_coverage', 0.80),
            'transplant_medication_coverage': self.quality_config.get('mapping_validation', {}).get('transplant_medication_coverage', 0.95),
            'confidence_threshold': self.quality_config.get('mapping_validation', {}).get('confidence_threshold', 0.70),
            'max_error_rate': 0.05,
            'min_success_rate': 0.95
        }

        # Quality metrics
        self.quality_metrics = {
            'timestamp': None,
            'overall_score': 0,
            'data_quality': {},
            'mapping_quality': {},
            'omop_compliance': {},
            'performance_metrics': {},
            'recommendations': []
        }

    def assess_quality(
        self,
        output_dir: str,
        include_trends: bool = False,
        generate_report: bool = True
    ) -> Dict[str, Any]:
        """
        Assess quality of medication normalization output.

        Args:
            output_dir: Directory containing OMOP output
            include_trends: Include trend analysis
            generate_report: Generate detailed quality report

        Returns:
            Quality assessment results
        """
        self.quality_metrics['timestamp'] = datetime.now()
        self.logger.info(f"Starting quality assessment for: {output_dir}")

        try:
            # Load OMOP tables
            omop_tables = self._load_omop_tables(output_dir)

            if not omop_tables:
                return self._create_empty_assessment("No OMOP tables found")

            # Assess data quality
            self._assess_data_quality(omop_tables)

            # Assess mapping quality
            self._assess_mapping_quality(output_dir, omop_tables)

            # Assess OMOP compliance
            self._assess_omop_compliance(omop_tables)

            # Calculate overall quality score
            self._calculate_overall_score()

            # Generate recommendations
            self._generate_recommendations()

            # Include trend analysis if requested
            if include_trends:
                self._analyze_trends(output_dir)

            # Generate report if requested
            if generate_report:
                report_path = self._generate_quality_report(output_dir)
                self.quality_metrics['report_path'] = report_path

            self.logger.info(f"Quality assessment completed. Overall score: {self.quality_metrics['overall_score']:.1f}%")
            return self.quality_metrics

        except Exception as e:
            self.logger.error(f"Quality assessment failed: {str(e)}")
            raise

    def _load_omop_tables(self, output_dir: str) -> Dict[str, pd.DataFrame]:
        """Load OMOP tables from output directory."""
        output_path = Path(output_dir)
        omop_tables = {}

        if not output_path.exists():
            self.logger.error(f"Output directory not found: {output_dir}")
            return {}

        # Load parquet files
        for parquet_file in output_path.glob("*.parquet"):
            table_name = parquet_file.stem
            try:
                omop_tables[table_name] = pd.read_parquet(parquet_file)
                self.logger.info(f"Loaded {table_name}: {len(omop_tables[table_name])} records")
            except Exception as e:
                self.logger.error(f"Failed to load {parquet_file}: {str(e)}")

        return omop_tables

    def _assess_data_quality(self, omop_tables: Dict[str, pd.DataFrame]) -> None:
        """Assess general data quality metrics."""
        self.logger.info("Assessing data quality...")

        data_quality = {
            'total_records': 0,
            'table_completeness': {},
            'field_completeness': {},
            'data_consistency': {},
            'score': 0
        }

        for table_name, df in omop_tables.items():
            if df.empty:
                continue

            data_quality['total_records'] += len(df)

            # Table completeness
            data_quality['table_completeness'][table_name] = {
                'record_count': len(df),
                'has_data': len(df) > 0
            }

            # Field completeness
            field_completeness = {}
            for column in df.columns:
                null_count = df[column].isnull().sum()
                completeness_rate = (len(df) - null_count) / len(df)
                field_completeness[column] = {
                    'null_count': int(null_count),
                    'completeness_rate': float(completeness_rate)
                }

            data_quality['field_completeness'][table_name] = field_completeness

            # Data consistency checks
            consistency_checks = self._check_data_consistency(df, table_name)
            data_quality['data_consistency'][table_name] = consistency_checks

        # Calculate overall data quality score
        data_quality['score'] = self._calculate_data_quality_score(data_quality)
        self.quality_metrics['data_quality'] = data_quality

    def _assess_mapping_quality(self, output_dir: str, omop_tables: Dict[str, pd.DataFrame]) -> None:
        """Assess medication mapping quality."""
        self.logger.info("Assessing mapping quality...")

        mapping_quality = {
            'overall_coverage': 0,
            'transplant_coverage': 0,
            'confidence_distribution': {},
            'method_effectiveness': {},
            'unmapped_medications': [],
            'score': 0
        }

        try:
            # Load execution summary for mapping statistics
            summary_path = Path(output_dir) / 'execution_summary.json'
            if summary_path.exists():
                with open(summary_path, 'r') as f:
                    execution_summary = json.load(f)

                # Extract mapping statistics
                norm_stats = execution_summary.get('detailed_statistics', {}).get('normalization', {})

                if 'rxnorm_mapper_stats' in norm_stats:
                    mapper_stats = norm_stats['rxnorm_mapper_stats']
                    mapping_quality['overall_coverage'] = mapper_stats.get('mapping_success_rate', 0)

                if 'medication_matcher_stats' in norm_stats:
                    matcher_stats = norm_stats['medication_matcher_stats']
                    mapping_quality['method_effectiveness'] = matcher_stats.get('method_effectiveness', {})

            # Analyze DRUG_EXPOSURE table for mapping quality
            if 'DRUG_EXPOSURE' in omop_tables:
                drug_exposure_df = omop_tables['DRUG_EXPOSURE']

                # Check for valid concept IDs
                valid_concepts = drug_exposure_df['drug_concept_id'].notna() & (drug_exposure_df['drug_concept_id'] > 0)
                mapping_quality['concept_coverage'] = valid_concepts.sum() / len(drug_exposure_df)

                # Check for transplant medications
                transplant_indicators = drug_exposure_df.get('drug_source_value', pd.Series()).str.lower()
                transplant_keywords = ['tacrolimus', 'cyclosporine', 'mycophenolate', 'sirolimus', 'prednisone']

                is_transplant = transplant_indicators.str.contains('|'.join(transplant_keywords), na=False)
                if is_transplant.any():
                    transplant_mapped = valid_concepts[is_transplant].sum()
                    mapping_quality['transplant_coverage'] = transplant_mapped / is_transplant.sum()

        except Exception as e:
            self.logger.warning(f"Could not assess mapping quality: {str(e)}")

        # Calculate mapping quality score
        mapping_quality['score'] = self._calculate_mapping_quality_score(mapping_quality)
        self.quality_metrics['mapping_quality'] = mapping_quality

    def _assess_omop_compliance(self, omop_tables: Dict[str, pd.DataFrame]) -> None:
        """Assess OMOP CDM compliance."""
        self.logger.info("Assessing OMOP compliance...")

        omop_compliance = {
            'table_compliance': {},
            'schema_compliance': {},
            'business_rule_compliance': {},
            'score': 0
        }

        for table_name, df in omop_tables.items():
            try:
                # Validate table against OMOP standards
                validation_result = self.omop_validator.validate_table(df, table_name)

                omop_compliance['table_compliance'][table_name] = {
                    'is_valid': validation_result.get('is_valid', False),
                    'error_count': len(validation_result.get('errors', [])),
                    'warning_count': len(validation_result.get('warnings', [])),
                    'errors': validation_result.get('errors', []),
                    'warnings': validation_result.get('warnings', [])
                }

            except Exception as e:
                self.logger.error(f"OMOP validation failed for {table_name}: {str(e)}")
                omop_compliance['table_compliance'][table_name] = {
                    'is_valid': False,
                    'error_count': 1,
                    'errors': [str(e)]
                }

        # Calculate OMOP compliance score
        omop_compliance['score'] = self._calculate_omop_compliance_score(omop_compliance)
        self.quality_metrics['omop_compliance'] = omop_compliance

    def _check_data_consistency(self, df: pd.DataFrame, table_name: str) -> Dict[str, Any]:
        """Check data consistency within a table."""
        consistency_checks = {
            'date_consistency': True,
            'id_consistency': True,
            'value_ranges': True,
            'issues': []
        }

        # Date consistency checks
        if table_name == 'DRUG_EXPOSURE':
            if all(col in df.columns for col in ['drug_exposure_start_date', 'drug_exposure_end_date']):
                start_dates = pd.to_datetime(df['drug_exposure_start_date'], errors='coerce')
                end_dates = pd.to_datetime(df['drug_exposure_end_date'], errors='coerce')

                invalid_date_ranges = (start_dates > end_dates).sum()
                if invalid_date_ranges > 0:
                    consistency_checks['date_consistency'] = False
                    consistency_checks['issues'].append(f"Invalid date ranges: {invalid_date_ranges}")

        # ID consistency checks
        id_columns = [col for col in df.columns if col.endswith('_id')]
        for col in id_columns:
            if col in df.columns:
                negative_ids = (df[col] < 0).sum()
                if negative_ids > 0:
                    consistency_checks['id_consistency'] = False
                    consistency_checks['issues'].append(f"Negative IDs in {col}: {negative_ids}")

        # Value range checks
        if 'dose_value' in df.columns:
            invalid_doses = ((df['dose_value'] <= 0) | (df['dose_value'] > 10000)).sum()
            if invalid_doses > 0:
                consistency_checks['value_ranges'] = False
                consistency_checks['issues'].append(f"Invalid dose values: {invalid_doses}")

        return consistency_checks

    def _calculate_data_quality_score(self, data_quality: Dict[str, Any]) -> float:
        """Calculate overall data quality score."""
        if not data_quality['table_completeness']:
            return 0

        # Calculate average field completeness
        all_completeness_rates = []
        for table_completeness in data_quality['field_completeness'].values():
            for field_info in table_completeness.values():
                all_completeness_rates.append(field_info['completeness_rate'])

        avg_completeness = sum(all_completeness_rates) / len(all_completeness_rates) if all_completeness_rates else 0

        # Calculate consistency score
        consistency_scores = []
        for table_consistency in data_quality['data_consistency'].values():
            table_score = sum([
                table_consistency['date_consistency'],
                table_consistency['id_consistency'],
                table_consistency['value_ranges']
            ]) / 3
            consistency_scores.append(table_score)

        avg_consistency = sum(consistency_scores) / len(consistency_scores) if consistency_scores else 0

        # Combined score
        return (avg_completeness * 0.7 + avg_consistency * 0.3) * 100

    def _calculate_mapping_quality_score(self, mapping_quality: Dict[str, Any]) -> float:
        """Calculate mapping quality score."""
        overall_coverage = mapping_quality.get('overall_coverage', 0)
        transplant_coverage = mapping_quality.get('transplant_coverage', 0)
        concept_coverage = mapping_quality.get('concept_coverage', 0)

        # Weighted score emphasizing transplant medication coverage
        score = (
            overall_coverage * 0.3 +
            transplant_coverage * 0.5 +
            concept_coverage * 0.2
        ) * 100

        return min(score, 100)

    def _calculate_omop_compliance_score(self, omop_compliance: Dict[str, Any]) -> float:
        """Calculate OMOP compliance score."""
        if not omop_compliance['table_compliance']:
            return 0

        table_scores = []
        for table_info in omop_compliance['table_compliance'].values():
            if table_info['is_valid']:
                table_scores.append(100)
            else:
                # Deduct points for errors and warnings
                error_penalty = table_info['error_count'] * 10
                warning_penalty = table_info.get('warning_count', 0) * 2
                table_score = max(0, 100 - error_penalty - warning_penalty)
                table_scores.append(table_score)

        return sum(table_scores) / len(table_scores) if table_scores else 0

    def _calculate_overall_score(self) -> None:
        """Calculate overall quality score."""
        data_score = self.quality_metrics['data_quality'].get('score', 0)
        mapping_score = self.quality_metrics['mapping_quality'].get('score', 0)
        omop_score = self.quality_metrics['omop_compliance'].get('score', 0)

        # Weighted overall score
        overall_score = (
            data_score * 0.3 +
            mapping_score * 0.4 +
            omop_score * 0.3
        )

        self.quality_metrics['overall_score'] = overall_score

    def _generate_recommendations(self) -> None:
        """Generate quality improvement recommendations."""
        recommendations = []

        # Data quality recommendations
        data_score = self.quality_metrics['data_quality'].get('score', 0)
        if data_score < 90:
            recommendations.append({
                'category': 'Data Quality',
                'priority': 'HIGH',
                'issue': f'Data quality score is {data_score:.1f}%',
                'recommendation': 'Review data extraction logic and add validation rules',
                'impact': 'Improved data reliability and downstream analytics'
            })

        # Mapping quality recommendations
        mapping_score = self.quality_metrics['mapping_quality'].get('score', 0)
        overall_coverage = self.quality_metrics['mapping_quality'].get('overall_coverage', 0)

        if overall_coverage < self.thresholds['min_mapping_coverage']:
            recommendations.append({
                'category': 'Mapping Quality',
                'priority': 'HIGH',
                'issue': f'Mapping coverage is {overall_coverage:.1%}',
                'recommendation': 'Review unmapped medications and enhance fuzzy matching rules',
                'impact': 'Better medication standardization and data completeness'
            })

        transplant_coverage = self.quality_metrics['mapping_quality'].get('transplant_coverage', 0)
        if transplant_coverage < self.thresholds['transplant_medication_coverage']:
            recommendations.append({
                'category': 'Transplant Medications',
                'priority': 'CRITICAL',
                'issue': f'Transplant medication coverage is {transplant_coverage:.1%}',
                'recommendation': 'Focus on improving transplant medication mapping accuracy',
                'impact': 'Critical for transplant patient care analytics'
            })

        # OMOP compliance recommendations
        omop_score = self.quality_metrics['omop_compliance'].get('score', 0)
        if omop_score < 95:
            recommendations.append({
                'category': 'OMOP Compliance',
                'priority': 'MEDIUM',
                'issue': f'OMOP compliance score is {omop_score:.1f}%',
                'recommendation': 'Address OMOP validation errors and warnings',
                'impact': 'Improved interoperability and standard compliance'
            })

        self.quality_metrics['recommendations'] = recommendations

    def _analyze_trends(self, output_dir: str) -> None:
        """Analyze quality trends over time."""
        # This would compare with historical quality metrics
        # For now, just placeholder
        self.quality_metrics['trends'] = {
            'note': 'Trend analysis requires historical data',
            'current_assessment': datetime.now().isoformat()
        }

    def _generate_quality_report(self, output_dir: str) -> str:
        """Generate detailed quality report."""
        report_lines = [
            "# Medication Normalization Quality Report",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## Executive Summary",
            f"**Overall Quality Score: {self.quality_metrics['overall_score']:.1f}%**",
            "",
            f"- Data Quality: {self.quality_metrics['data_quality'].get('score', 0):.1f}%",
            f"- Mapping Quality: {self.quality_metrics['mapping_quality'].get('score', 0):.1f}%",
            f"- OMOP Compliance: {self.quality_metrics['omop_compliance'].get('score', 0):.1f}%",
            "",
        ]

        # Add recommendations
        if self.quality_metrics['recommendations']:
            report_lines.extend([
                "## Recommendations",
                ""
            ])

            for rec in self.quality_metrics['recommendations']:
                report_lines.extend([
                    f"### {rec['category']} ({rec['priority']} Priority)",
                    f"**Issue:** {rec['issue']}",
                    f"**Recommendation:** {rec['recommendation']}",
                    f"**Impact:** {rec['impact']}",
                    ""
                ])

        # Add detailed metrics
        report_lines.extend([
            "## Detailed Metrics",
            "",
            "### Data Quality",
            f"- Total Records: {self.quality_metrics['data_quality'].get('total_records', 0):,}",
            "",
            "### Mapping Quality",
            f"- Overall Coverage: {self.quality_metrics['mapping_quality'].get('overall_coverage', 0):.1%}",
            f"- Transplant Coverage: {self.quality_metrics['mapping_quality'].get('transplant_coverage', 0):.1%}",
            "",
            "### OMOP Compliance",
            "- Table validation results available in detailed metrics"
        ])

        # Save report
        report_path = Path(output_dir) / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_path, 'w') as f:
            f.write('\n'.join(report_lines))

        self.logger.info(f"Quality report saved to: {report_path}")
        return str(report_path)

    def _create_empty_assessment(self, message: str) -> Dict[str, Any]:
        """Create empty assessment result."""
        return {
            'timestamp': datetime.now(),
            'overall_score': 0,
            'message': message,
            'data_quality': {'score': 0},
            'mapping_quality': {'score': 0},
            'omop_compliance': {'score': 0},
            'recommendations': []
        }


def main():
    """Main entry point for quality monitoring."""
    parser = argparse.ArgumentParser(
        description="Monitor medication normalization quality",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument(
        '--output-dir',
        required=True,
        help='Directory containing OMOP output to assess'
    )

    parser.add_argument(
        '--config',
        default='../config/pipeline_config.yaml',
        help='Configuration file path'
    )

    parser.add_argument(
        '--generate-report',
        action='store_true',
        default=True,
        help='Generate detailed quality report'
    )

    parser.add_argument(
        '--include-trends',
        action='store_true',
        help='Include trend analysis (requires historical data)'
    )

    parser.add_argument(
        '--threshold-override',
        help='JSON file with custom quality thresholds'
    )

    args = parser.parse_args()

    try:
        # Load configuration
        config_path = Path(args.config)
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
        else:
            with open(config_path, 'r') as f:
                config = json.load(f)

        # Override thresholds if provided
        if args.threshold_override:
            with open(args.threshold_override, 'r') as f:
                threshold_overrides = json.load(f)

            config.setdefault('validation', {}).setdefault('mapping_validation', {}).update(threshold_overrides)

        # Initialize monitor
        monitor = QualityMonitor(config)

        # Run quality assessment
        results = monitor.assess_quality(
            output_dir=args.output_dir,
            include_trends=args.include_trends,
            generate_report=args.generate_report
        )

        # Print results
        print("\n" + "="*60)
        print("QUALITY ASSESSMENT RESULTS")
        print("="*60)
        print(f"Overall Quality Score: {results['overall_score']:.1f}%")
        print(f"Data Quality: {results['data_quality']['score']:.1f}%")
        print(f"Mapping Quality: {results['mapping_quality']['score']:.1f}%")
        print(f"OMOP Compliance: {results['omop_compliance']['score']:.1f}%")

        if results.get('recommendations'):
            print(f"\nRecommendations: {len(results['recommendations'])}")
            for rec in results['recommendations']:
                print(f"  {rec['priority']} - {rec['category']}: {rec['issue']}")

        if 'report_path' in results:
            print(f"\nDetailed report: {results['report_path']}")

        # Exit with appropriate code based on quality score
        if results['overall_score'] >= 90:
            print("\n✅ Quality assessment PASSED - Excellent quality!")
            sys.exit(0)
        elif results['overall_score'] >= 70:
            print("\n⚠️ Quality assessment PASSED - Good quality with room for improvement")
            sys.exit(0)
        else:
            print("\n❌ Quality assessment FAILED - Quality improvements needed")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Quality assessment failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()